"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/components/layout/footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/footer.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Facebook,Instagram,Mail,MapPin,Phone,Shield,Truck,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n\n\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-muted/50 border-t\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-12 w-12 items-center justify-center rounded-full bg-primary/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-6 w-6 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold\",\n                                                children: \"شحن مجاني\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"للطلبات أكثر من 200 ريال\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-12 w-12 items-center justify-center rounded-full bg-primary/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold\",\n                                                children: \"ضمان الجودة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"منتجات أصلية 100%\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-12 w-12 items-center justify-center rounded-full bg-primary/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold\",\n                                                children: \"دعم 24/7\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"خدمة عملاء متواصلة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-12 w-12 items-center justify-center rounded-full bg-primary/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold\",\n                                                children: \"دفع آمن\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"طرق دفع متعددة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/images/logo.png\",\n                                        alt: \"VisionLens Logo\",\n                                        width: 220,\n                                        height: 70,\n                                        className: \"h-18 w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"متجرك الموثوق للعدسات اللاصقة والنظارات الطبية في العراق. نقدم أفضل المنتجات بأعلى جودة وأفضل الأسعار.\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#\",\n                                            className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#\",\n                                            className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#\",\n                                            className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#\",\n                                            className: \"text-muted-foreground hover:text-primary transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"روابط سريعة\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/about\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"من نحن\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"تواصل معنا\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/shipping\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"الشحن والتوصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/returns\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"سياسة الإرجاع\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"الأسئلة الشائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"فئات المنتجات\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/categories/contact-lenses\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"العدسات اللاصقة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/categories/colored-lenses\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"العدسات الملونة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/categories/glasses\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"النظارات الطبية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/categories/sunglasses\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"النظارات الشمسية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/categories/accessories\",\n                                                className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                                children: \"الإكسسوارات\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"تواصل معنا\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"+964 ************\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Facebook_Instagram_Mail_MapPin_Phone_Shield_Truck_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        \"بغداد، العراق\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"منطقة الكرادة، شارع أبو نواس\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"\\xa9 2024 VisionLens Iraq. جميع الحقوق محفوظة.\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                        children: \"سياسة الخصوصية\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                        children: \"الشروط والأحكام\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/footer.tsx\n"));

/***/ })

});