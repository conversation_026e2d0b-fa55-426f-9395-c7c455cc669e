"use client";

import React, { useState } from "react";
import Link from "next/link";
import {
  Search,
  Filter,
  Eye,
  Mail,
  Phone,
  Calendar,
  ShoppingBag,
  DollarSign,
  UserCheck,
  UserX,
  Download,
  MoreHorizontal,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatPrice } from "@/lib/utils";

// بيانات وهمية للعملاء
const customers = [
  {
    id: "1",
    name: "أحمد محمد",
    email: "<EMAIL>",
    phone: "+966501234567",
    joinDate: "2023-06-15",
    lastOrder: "2024-01-15",
    totalOrders: 12,
    totalSpent: 2450,
    status: "نشط",
    city: "الرياض",
    avatar: "أ",
  },
  {
    id: "2",
    name: "فاطمة أحمد",
    email: "<EMAIL>",
    phone: "+966501234568",
    joinDate: "2023-08-20",
    lastOrder: "2024-01-10",
    totalOrders: 8,
    totalSpent: 1680,
    status: "نشط",
    city: "جدة",
    avatar: "ف",
  },
  {
    id: "3",
    name: "محمد علي",
    email: "<EMAIL>",
    phone: "+966501234569",
    joinDate: "2023-03-10",
    lastOrder: "2024-01-05",
    totalOrders: 15,
    totalSpent: 3200,
    status: "نشط",
    city: "الدمام",
    avatar: "م",
  },
  {
    id: "4",
    name: "سارة خالد",
    email: "<EMAIL>",
    phone: "+966501234570",
    joinDate: "2023-11-05",
    lastOrder: "2023-12-20",
    totalOrders: 3,
    totalSpent: 450,
    status: "غير نشط",
    city: "الرياض",
    avatar: "س",
  },
  {
    id: "5",
    name: "عبدالله سعد",
    email: "<EMAIL>",
    phone: "+966501234571",
    joinDate: "2023-09-12",
    lastOrder: "2024-01-12",
    totalOrders: 6,
    totalSpent: 980,
    status: "نشط",
    city: "مكة",
    avatar: "ع",
  },
];

const customerSegments = [
  { label: "الكل", value: "all" },
  { label: "عملاء نشطون", value: "active" },
  { label: "عملاء غير نشطين", value: "inactive" },
  { label: "عملاء جدد", value: "new" },
  { label: "عملاء مميزون", value: "vip" },
];

export default function CustomersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSegment, setSelectedSegment] = useState("all");
  const [sortBy, setSortBy] = useState("joinDate");

  const filteredCustomers = customers.filter((customer) => {
    const matchesSearch = 
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm);
    
    let matchesSegment = true;
    switch (selectedSegment) {
      case "active":
        matchesSegment = customer.status === "نشط";
        break;
      case "inactive":
        matchesSegment = customer.status === "غير نشط";
        break;
      case "new":
        matchesSegment = new Date(customer.joinDate) > new Date("2023-10-01");
        break;
      case "vip":
        matchesSegment = customer.totalSpent > 2000;
        break;
    }
    
    return matchesSearch && matchesSegment;
  });

  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name);
      case "totalSpent":
        return b.totalSpent - a.totalSpent;
      case "totalOrders":
        return b.totalOrders - a.totalOrders;
      case "lastOrder":
        return new Date(b.lastOrder).getTime() - new Date(a.lastOrder).getTime();
      default:
        return new Date(b.joinDate).getTime() - new Date(a.joinDate).getTime();
    }
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("ar-SA");
  };

  const getStatusColor = (status: string) => {
    return status === "نشط" 
      ? "bg-green-100 text-green-800" 
      : "bg-gray-100 text-gray-800";
  };

  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(c => c.status === "نشط").length;
  const newCustomers = customers.filter(c => new Date(c.joinDate) > new Date("2023-10-01")).length;
  const avgOrderValue = customers.reduce((sum, c) => sum + c.totalSpent, 0) / customers.reduce((sum, c) => sum + c.totalOrders, 0);

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إدارة العملاء</h1>
          <p className="text-gray-600 mt-1">متابعة وإدارة بيانات العملاء</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            تصدير البيانات
          </Button>
          <Button variant="outline">
            <Mail className="h-4 w-4 mr-2" />
            إرسال رسالة جماعية
          </Button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي العملاء</p>
                <p className="text-3xl font-bold">{totalCustomers}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <UserCheck className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">عملاء نشطون</p>
                <p className="text-3xl font-bold text-green-600">{activeCustomers}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <UserCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">عملاء جدد</p>
                <p className="text-3xl font-bold text-blue-600">{newCustomers}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">متوسط قيمة الطلب</p>
                <p className="text-3xl font-bold text-purple-600">
                  {formatPrice(avgOrderValue)}
                </p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والفلترة */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في العملاء..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedSegment}
                onChange={(e) => setSelectedSegment(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {customerSegments.map((segment) => (
                  <option key={segment.value} value={segment.value}>
                    {segment.label}
                  </option>
                ))}
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="joinDate">تاريخ الانضمام</option>
                <option value="name">الاسم</option>
                <option value="totalSpent">إجمالي المشتريات</option>
                <option value="totalOrders">عدد الطلبات</option>
                <option value="lastOrder">آخر طلب</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                فلترة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول العملاء */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة العملاء ({sortedCustomers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right py-3 px-4 font-medium">العميل</th>
                  <th className="text-right py-3 px-4 font-medium">معلومات التواصل</th>
                  <th className="text-right py-3 px-4 font-medium">تاريخ الانضمام</th>
                  <th className="text-right py-3 px-4 font-medium">آخر طلب</th>
                  <th className="text-right py-3 px-4 font-medium">الطلبات</th>
                  <th className="text-right py-3 px-4 font-medium">إجمالي المشتريات</th>
                  <th className="text-right py-3 px-4 font-medium">الحالة</th>
                  <th className="text-right py-3 px-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {sortedCustomers.map((customer) => (
                  <tr key={customer.id} className="border-b hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="font-semibold text-primary">{customer.avatar}</span>
                        </div>
                        <div>
                          <p className="font-medium">{customer.name}</p>
                          <p className="text-sm text-gray-500">{customer.city}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="h-3 w-3 text-gray-400" />
                          <span>{customer.email}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="h-3 w-3 text-gray-400" />
                          <span>{customer.phone}</span>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-sm">
                      {formatDate(customer.joinDate)}
                    </td>
                    <td className="py-4 px-4 text-sm">
                      {formatDate(customer.lastOrder)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <ShoppingBag className="h-4 w-4 text-gray-400" />
                        <span className="font-medium">{customer.totalOrders}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4 font-medium text-primary">
                      {formatPrice(customer.totalSpent)}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(customer.status)}`}>
                        {customer.status}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" asChild>
                          <Link href={`/admin/customers/${customer.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Mail className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {sortedCustomers.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">لا توجد عملاء تطابق البحث</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
