"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { 
  Star, 
  Heart, 
  ShoppingCart, 
  Minus, 
  Plus, 
  Share2, 
  Truck,
  Shield,
  RotateCcw,
  ChevronLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import ProductCard from "@/components/product/product-card";
import { formatPrice } from "@/lib/utils";
import { ProductsStore } from "@/lib/products-store";
import { formatCurrency } from "@/lib/currency";





// تقييمات وهمية
const reviews = [
  {
    id: 1,
    user: "أحمد محمد",
    rating: 5,
    date: "2024-01-10",
    comment: "عدسات ممتازة وراحة طوال اليوم. أنصح بها بشدة!"
  },
  {
    id: 2,
    user: "فاطمة أحمد",
    rating: 4,
    date: "2024-01-08",
    comment: "جودة عالية وسعر مناسب. التوصيل كان سريع."
  },
  {
    id: 3,
    user: "محمد علي",
    rating: 5,
    date: "2024-01-05",
    comment: "أفضل عدسات جربتها. لا أشعر بها في عيني."
  }
];

export default function ProductDetailPage({ params }: { params: Promise<{ slug: string }> }) {
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [activeTab, setActiveTab] = useState("description");

  // فك تشفير params للتوافق مع Next.js 15
  const resolvedParams = React.use(params);

  // البحث عن المنتج في النظام المركزي
  const allProducts = ProductsStore.getAll();
  const product = allProducts.find(p => p.slug === resolvedParams.slug);

  // الحصول على منتجات مشابهة (نفس الفئة)
  const relatedProducts = allProducts
    .filter(p => p.category === product?.category && p.id !== product?.id)
    .slice(0, 4);

  if (!product) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-3xl font-bold mb-4">المنتج غير موجود</h1>
          <p className="text-gray-600 mb-8">عذراً، المنتج الذي تبحث عنه غير متوفر</p>
          <Button asChild>
            <Link href="/products">العودة للمنتجات</Link>
          </Button>
        </main>
        <Footer />
      </div>
    );
  }

  const handleAddToCart = () => {
    console.log(`Added ${quantity} items to cart`);
  };

  const handleWishlistToggle = () => {
    setIsWishlisted(!isWishlisted);
  };

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* مسار التنقل */}
        <nav className="flex items-center gap-2 text-sm text-gray-600 mb-6">
          <Link href="/" className="hover:text-primary">الرئيسية</Link>
          <ChevronLeft className="h-4 w-4" />
          <Link href="/products" className="hover:text-primary">المنتجات</Link>
          <ChevronLeft className="h-4 w-4" />
          <span className="text-gray-900">{product.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* صور المنتج */}
          <div className="space-y-4">
            <div className="aspect-square overflow-hidden rounded-lg border">
              <Image
                src={product.images[selectedImage]}
                alt={product.name}
                width={600}
                height={600}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square overflow-hidden rounded-lg border-2 ${
                    selectedImage === index ? "border-primary" : "border-gray-200"
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    width={150}
                    height={150}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* معلومات المنتج */}
          <div className="space-y-6">
            <div>
              <p className="text-sm text-gray-600 mb-2">{product.brand}</p>
              <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
              <p className="text-lg text-gray-600">{product.nameEn}</p>
            </div>

            {/* التقييم - سيتم إضافة نظام التقييم لاحقاً */}
            <div className="flex items-center gap-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="h-5 w-5 text-gray-300"
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">
                (لا توجد تقييمات بعد)
              </span>
            </div>

            {/* السعر */}
            <div className="flex items-center gap-3">
              <span className="text-3xl font-bold text-primary">
                {formatCurrency(product.price, product.priceCurrency)}
              </span>
              {product.comparePrice && (
                <span className="text-xl text-gray-500 line-through">
                  {formatCurrency(product.comparePrice, product.comparePriceCurrency || product.priceCurrency)}
                </span>
              )}
              {product.comparePrice && (
                <span className="bg-red-100 text-red-800 text-sm px-2 py-1 rounded">
                  وفر {formatCurrency(product.comparePrice - product.price, product.priceCurrency)}
                </span>
              )}
            </div>

            {/* الكمية والإضافة للسلة */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <span className="font-medium">الكمية:</span>
                <div className="flex items-center border rounded-lg">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="px-4 py-2 min-w-[60px] text-center">{quantity}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setQuantity(quantity + 1)}
                    disabled={quantity >= product.stock}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <span className="text-sm text-gray-600">
                  متوفر {product.stock} قطعة
                </span>
              </div>

              <div className="flex gap-3">
                <Button
                  className="flex-1"
                  onClick={handleAddToCart}
                  disabled={product.stock === 0}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  أضف للسلة
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleWishlistToggle}
                >
                  <Heart
                    className={`h-4 w-4 ${
                      isWishlisted ? "fill-red-500 text-red-500" : ""
                    }`}
                  />
                </Button>
                <Button variant="outline" size="icon">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* المميزات */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t">
              <div className="flex items-center gap-2">
                <Truck className="h-5 w-5 text-primary" />
                <span className="text-sm">شحن مجاني</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-primary" />
                <span className="text-sm">ضمان الجودة</span>
              </div>
              <div className="flex items-center gap-2">
                <RotateCcw className="h-5 w-5 text-primary" />
                <span className="text-sm">إرجاع مجاني</span>
              </div>
            </div>
          </div>
        </div>

        {/* تبويبات المعلومات */}
        <div className="mb-12">
          <div className="border-b">
            <nav className="flex space-x-8">
              {[
                { id: "description", label: "الوصف" },
                { id: "specifications", label: "المواصفات" },
                { id: "reviews", label: "التقييمات" },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? "border-primary text-primary"
                      : "border-transparent text-gray-500 hover:text-gray-700"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-6">
            {activeTab === "description" && (
              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">{product.description}</p>
                {product.tags && product.tags.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-3">العلامات:</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.tags.map((tag, index) => (
                        <span key={index} className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === "specifications" && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {product.specifications.map((spec, index) => (
                  <div key={index} className="flex justify-between py-2 border-b">
                    <span className="font-medium">{spec.key}:</span>
                    <span className="text-gray-600">{spec.value}</span>
                  </div>
                ))}
              </div>
            )}

            {activeTab === "reviews" && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">
                    التقييمات (0)
                  </h3>
                  <Button variant="outline">اكتب تقييم</Button>
                </div>
                <div className="text-center py-8 text-gray-500">
                  <p>لا توجد تقييمات بعد</p>
                  <p className="text-sm mt-2">كن أول من يقيم هذا المنتج</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* منتجات مشابهة */}
        <section>
          <h2 className="text-2xl font-bold mb-6">منتجات مشابهة</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {relatedProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
