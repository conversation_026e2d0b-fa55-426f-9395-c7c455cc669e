"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  ArrowLeft,
  Save,
  Eye,
  Trash2,
  AlertCircle,
  Upload,
  X,
  Plus,
  Package,
  DollarSign,
  Tag,
  FileText,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function EditProductFormPage({ params }: { params: Promise<{ id: string }> }) {
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<any>(null);
  const [originalData, setOriginalData] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false);
  const [currentTag, setCurrentTag] = useState("");
  const [uploadingImages, setUploadingImages] = useState(false);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [productId, setProductId] = useState<string>("");

  const categories = [
    "العدسات اليومية",
    "العدسات الشهرية", 
    "العدسات الملونة",
    "العدسات الأسبوعية",
    "النظارات الطبية",
    "النظارات الشمسية",
    "الإكسسوارات",
  ];

  const brands = [
    "Johnson & Johnson",
    "Alcon",
    "CooperVision", 
    "Bausch & Lomb",
    "Ray-Ban",
    "Oakley",
  ];

  useEffect(() => {
    const initializeProduct = async () => {
      try {
        const resolvedParams = await params;
        const id = resolvedParams.id;
        setProductId(id);

        await loadProduct(id);
      } catch (error) {
        console.error("Error initializing product:", error);
        setIsLoading(false);
      }
    };

    initializeProduct();
  }, [params]);

  const loadProduct = async (id: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockProduct = {
        id: id,
          name: "عدسات أكيوفيو اليومية",
          nameEn: "Acuvue Oasys Daily",
          description: "عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي. تتميز بتقنية HydraLuxe للترطيب المستمر وحماية من الأشعة فوق البنفسجية.",
          shortDescription: "عدسات يومية مريحة وآمنة",
          sku: "ACU-001",
          barcode: "1234567890123",
          category: "العدسات اليومية",
          brand: "Johnson & Johnson",
          tags: ["يومية", "مريحة", "آمنة", "حماية UV"],
          price: "120",
          comparePrice: "150",
          cost: "80",
          trackQuantity: true,
          quantity: "45",
          minQuantity: "10",
          maxQuantity: "100",
          location: "A1-B2",
          weight: "50",
          dimensions: {
            length: "10",
            width: "8",
            height: "2",
          },
          metaTitle: "عدسات أكيوفيو اليومية - VisionLens",
          metaDescription: "عدسات لاصقة يومية مريحة وآمنة",
          slug: "acuvue-oasys-daily",
          status: "active",
          featured: true,
          allowBackorder: false,
          specifications: [
            { key: "نوع العدسة", value: "يومية" },
            { key: "المادة", value: "سيليكون هيدروجيل" },
            { key: "نفاذية الأكسجين", value: "عالية" },
            { key: "محتوى الماء", value: "38%" },
            { key: "الحماية من الأشعة", value: "نعم" },
            { key: "العبوة", value: "30 عدسة" },
          ],
          images: [
            "https://picsum.photos/400/400?random=1",
            "https://picsum.photos/400/400?random=11",
            "https://picsum.photos/400/400?random=12",
          ],
        };
        
        setFormData(mockProduct);
        setOriginalData(mockProduct);
        setPreviewImages(mockProduct.images);
      } catch (error) {
        console.error("Error loading product:", error);
    } catch (error) {
      console.error("Error loading product:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev: any) => ({ ...prev, [field]: value }));
    setHasChanges(true);
    
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
    
    // توليد slug تلقائياً من الاسم
    if (field === "name" && value) {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\u0600-\u06FF\s-]/g, "")
        .replace(/\s+/g, "-")
        .trim();
      setFormData((prev: any) => ({ ...prev, slug }));
    }
  };

  const handleDimensionChange = (dimension: string, value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      dimensions: { ...prev.dimensions, [dimension]: value }
    }));
    setHasChanges(true);
  };

  const addTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData((prev: any) => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }));
      setCurrentTag("");
      setHasChanges(true);
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData((prev: any) => ({
      ...prev,
      tags: prev.tags.filter((tag: string) => tag !== tagToRemove)
    }));
    setHasChanges(true);
  };

  const addSpecification = () => {
    setFormData((prev: any) => ({
      ...prev,
      specifications: [...prev.specifications, { key: "", value: "" }]
    }));
    setHasChanges(true);
  };

  const updateSpecification = (index: number, field: string, value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      specifications: prev.specifications.map((spec: any, i: number) => 
        i === index ? { ...spec, [field]: value } : spec
      )
    }));
    setHasChanges(true);
  };

  const removeSpecification = (index: number) => {
    setFormData((prev: any) => ({
      ...prev,
      specifications: prev.specifications.filter((_: any, i: number) => i !== index)
    }));
    setHasChanges(true);
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    setUploadingImages(true);
    const newImages: string[] = [];
    const newPreviews: string[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        const preview = URL.createObjectURL(file);
        newPreviews.push(preview);
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const imageUrl = `https://picsum.photos/400/400?random=${Date.now()}-${i}`;
        newImages.push(imageUrl);
      }

      setFormData((prev: any) => ({
        ...prev,
        images: [...prev.images, ...newImages]
      }));
      
      setPreviewImages(prev => [...prev, ...newPreviews]);
      setHasChanges(true);
      
    } catch (error) {
      console.error("Error uploading images:", error);
      alert("حدث خطأ أثناء رفع الصور");
    } finally {
      setUploadingImages(false);
    }
  };

  const removeImage = (index: number) => {
    setFormData((prev: any) => ({
      ...prev,
      images: prev.images.filter((_: string, i: number) => i !== index)
    }));
    
    setPreviewImages(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      if (prev[index]) {
        URL.revokeObjectURL(prev[index]);
      }
      return newPreviews;
    });
    setHasChanges(true);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = "اسم المنتج مطلوب";
    if (!formData.nameEn.trim()) newErrors.nameEn = "الاسم الإنجليزي مطلوب";
    if (!formData.description.trim()) newErrors.description = "الوصف مطلوب";
    if (!formData.sku.trim()) newErrors.sku = "رمز المنتج مطلوب";
    if (!formData.category) newErrors.category = "الفئة مطلوبة";
    if (!formData.brand) newErrors.brand = "العلامة التجارية مطلوبة";
    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = "السعر مطلوب ويجب أن يكون أكبر من صفر";
    }

    if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {
      newErrors.quantity = "الكمية مطلوبة";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      console.log("Updating product:", formData);
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setOriginalData(formData);
      setHasChanges(false);
      alert("تم تحديث المنتج بنجاح!");
      
    } catch (error) {
      console.error("Error updating product:", error);
      alert("حدث خطأ أثناء تحديث المنتج");
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData(originalData);
    setPreviewImages(originalData.images);
    setHasChanges(false);
    setErrors({});
  };

  const handleDelete = async () => {
    if (!confirm("هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.")) {
      return;
    }

    try {
      console.log("Deleting product:", productId);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert("تم حذف المنتج بنجاح!");
      
    } catch (error) {
      console.error("Error deleting product:", error);
      alert("حدث خطأ أثناء حذف المنتج");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تحميل المنتج...</h1>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!formData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المنتج غير موجود</h1>
          </div>
        </div>
        
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">المنتج المطلوب غير موجود أو تم حذفه</span>
            </div>
            <div className="mt-4">
              <Button asChild>
                <Link href="/admin/products">العودة إلى المنتجات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تعديل المنتج</h1>
            <p className="text-gray-600 mt-1">{formData.name}</p>
            {hasChanges && (
              <p className="text-orange-600 text-sm mt-1">⚠️ يوجد تغييرات غير محفوظة</p>
            )}
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/products/${formData.slug}`} target="_blank">
              <Eye className="h-4 w-4 mr-2" />
              معاينة
            </Link>
          </Button>
          <Button variant="outline" onClick={resetForm} disabled={!hasChanges || isSubmitting}>
            <X className="h-4 w-4 mr-2" />
            إلغاء التغييرات
          </Button>
          <Button 
            variant="outline" 
            onClick={handleDelete}
            className="text-red-600 hover:text-red-700"
            disabled={isSubmitting}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            حذف
          </Button>
          <Button onClick={handleUpdate} disabled={!hasChanges || isSubmitting}>
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? "جاري الحفظ..." : "حفظ التغييرات"}
          </Button>
        </div>
      </div>

      <form onSubmit={(e) => { e.preventDefault(); handleUpdate(); }} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* العمود الرئيسي */}
        <div className="lg:col-span-2 space-y-6">
          {/* المعلومات الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                المعلومات الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    اسم المنتج (عربي) *
                  </label>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="أدخل اسم المنتج"
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && (
                    <p className="text-red-500 text-xs mt-1">{errors.name}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    اسم المنتج (إنجليزي) *
                  </label>
                  <Input
                    value={formData.nameEn}
                    onChange={(e) => handleInputChange("nameEn", e.target.value)}
                    placeholder="Product Name in English"
                    className={errors.nameEn ? "border-red-500" : ""}
                  />
                  {errors.nameEn && (
                    <p className="text-red-500 text-xs mt-1">{errors.nameEn}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  الوصف المختصر
                </label>
                <Input
                  value={formData.shortDescription}
                  onChange={(e) => handleInputChange("shortDescription", e.target.value)}
                  placeholder="وصف مختصر للمنتج"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  الوصف التفصيلي *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="وصف تفصيلي للمنتج..."
                  rows={4}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                    errors.description ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.description && (
                  <p className="text-red-500 text-xs mt-1">{errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    رمز المنتج (SKU) *
                  </label>
                  <Input
                    value={formData.sku}
                    onChange={(e) => handleInputChange("sku", e.target.value)}
                    placeholder="PRD-001"
                    className={errors.sku ? "border-red-500" : ""}
                  />
                  {errors.sku && (
                    <p className="text-red-500 text-xs mt-1">{errors.sku}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    الباركود
                  </label>
                  <Input
                    value={formData.barcode}
                    onChange={(e) => handleInputChange("barcode", e.target.value)}
                    placeholder="1234567890123"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* العمود الجانبي */}
        <div className="lg:col-span-1 space-y-6">
          {/* حالة المنتج */}
          <Card>
            <CardHeader>
              <CardTitle>حالة المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">الحالة</label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange("status", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="draft">مسودة</option>
                  <option value="active">نشط</option>
                  <option value="inactive">غير نشط</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.featured}
                  onChange={(e) => handleInputChange("featured", e.target.checked)}
                />
                <label htmlFor="featured" className="text-sm font-medium">
                  منتج مميز
                </label>
              </div>
            </CardContent>
          </Card>

          {/* الصور */}
          <Card>
            <CardHeader>
              <CardTitle>صور المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-sm text-gray-600 mb-2">
                  اسحب الصور هنا أو انقر للتحديد
                </p>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <Button 
                  type="button"
                  variant="outline" 
                  size="sm" 
                  onClick={() => document.getElementById('image-upload')?.click()}
                  disabled={uploadingImages}
                >
                  {uploadingImages ? "جاري الرفع..." : "اختيار الصور"}
                </Button>
              </div>

              {/* معاينة الصور */}
              {formData.images.length > 0 && (
                <div className="grid grid-cols-2 gap-2">
                  {formData.images.map((image: string, index: number) => (
                    <div key={index} className="relative group">
                      <div className="aspect-square relative rounded-lg overflow-hidden">
                        <Image
                          src={previewImages[index] || image}
                          alt={`صورة ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-3 w-3" />
                      </button>
                      {index === 0 && (
                        <div className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                          الصورة الرئيسية
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </form>

      {/* رسالة تحذيرية */}
      {Object.keys(errors).length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">يرجى تصحيح الأخطاء التالية:</span>
            </div>
            <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
              {Object.values(errors).map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
