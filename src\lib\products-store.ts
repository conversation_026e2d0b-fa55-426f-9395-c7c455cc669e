// نظام إدارة حالة المنتجات المركزي

export interface Product {
  id: string;
  name: string;
  nameEn: string;
  sku: string;
  category: string;
  brand: string;
  price: number;
  priceCurrency: string;
  comparePrice?: number;
  comparePriceCurrency?: string;
  cost?: number;
  costCurrency?: string;
  stock: number;
  minQuantity?: number;
  maxQuantity?: number;
  status: "active" | "draft" | "inactive";
  image: string;
  images: string[];
  description: string;
  shortDescription?: string;
  tags: string[];
  specifications: { key: string; value: string }[];
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  metaTitle?: string;
  metaDescription?: string;
  slug: string;
  featured: boolean;
  allowBackorder: boolean;
  trackQuantity: boolean;
  location?: string;
  barcode?: string;
  createdAt: string;
  updatedAt: string;
}

// مفتاح التخزين المحلي
const STORAGE_KEY = 'visionlens_products';

// تحميل المنتجات من التخزين المحلي
const loadProductsFromStorage = (): Product[] => {
  if (typeof window === 'undefined') return [];

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Error loading products from storage:', error);
  }

  return [];
};

// حفظ المنتجات في التخزين المحلي
const saveProductsToStorage = (products: Product[]): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(products));
  } catch (error) {
    console.error('Error saving products to storage:', error);
  }
};

// قائمة المنتجات - تحميل من التخزين المحلي
let products: Product[] = loadProductsFromStorage();

// دوال إدارة المنتجات
export const ProductsStore = {
  // الحصول على جميع المنتجات
  getAll: (): Product[] => {
    return [...products];
  },

  // الحصول على منتج بالمعرف
  getById: (id: string): Product | undefined => {
    return products.find(p => p.id === id);
  },

  // الحصول على منتج بالمعرف (اسم مختصر)
  get: (id: string): Product | undefined => {
    return products.find(p => p.id === id);
  },

  // الحصول على منتج بالـ SKU
  getBySku: (sku: string): Product | undefined => {
    return products.find(p => p.sku === sku);
  },

  // إضافة منتج جديد
  add: (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Product => {
    const newProduct: Product = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    products.push(newProduct);
    saveProductsToStorage(products); // حفظ في التخزين المحلي
    return newProduct;
  },

  // تحديث منتج
  update: (id: string, updates: Partial<Product>): Product | null => {
    const index = products.findIndex(p => p.id === id);
    if (index === -1) return null;

    products[index] = {
      ...products[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    saveProductsToStorage(products); // حفظ في التخزين المحلي
    return products[index];
  },

  // حذف منتج
  delete: (id: string): boolean => {
    const index = products.findIndex(p => p.id === id);
    if (index === -1) return false;

    products.splice(index, 1);
    saveProductsToStorage(products); // حفظ في التخزين المحلي
    return true;
  },

  // حذف منتجات متعددة
  deleteMultiple: (ids: string[]): number => {
    let deletedCount = 0;
    ids.forEach(id => {
      const index = products.findIndex(p => p.id === id);
      if (index !== -1) {
        products.splice(index, 1);
        deletedCount++;
      }
    });
    if (deletedCount > 0) {
      saveProductsToStorage(products); // حفظ في التخزين المحلي
    }
    return deletedCount;
  },

  // البحث في المنتجات
  search: (query: string): Product[] => {
    const lowerQuery = query.toLowerCase();
    return products.filter(product => 
      product.name.toLowerCase().includes(lowerQuery) ||
      product.nameEn.toLowerCase().includes(lowerQuery) ||
      product.sku.toLowerCase().includes(lowerQuery) ||
      product.brand.toLowerCase().includes(lowerQuery) ||
      product.category.toLowerCase().includes(lowerQuery)
    );
  },

  // فلترة المنتجات
  filter: (filters: {
    category?: string;
    brand?: string;
    status?: string;
    inStock?: boolean;
  }): Product[] => {
    return products.filter(product => {
      if (filters.category && filters.category !== "الكل" && product.category !== filters.category) {
        return false;
      }
      if (filters.brand && filters.brand !== "الكل" && product.brand !== filters.brand) {
        return false;
      }
      if (filters.status && filters.status !== "الكل" && product.status !== filters.status) {
        return false;
      }
      if (filters.inStock !== undefined) {
        const hasStock = product.stock > 0;
        if (filters.inStock !== hasStock) {
          return false;
        }
      }
      return true;
    });
  },

  // الحصول على الإحصائيات
  getStats: () => {
    const total = products.length;
    const active = products.filter(p => p.status === "active").length;
    const draft = products.filter(p => p.status === "draft").length;
    const outOfStock = products.filter(p => p.stock === 0).length;
    const totalStock = products.reduce((sum, p) => sum + p.stock, 0);
    const lowStock = products.filter(p => p.stock > 0 && p.stock <= 10).length;

    return {
      total,
      active,
      draft,
      outOfStock,
      totalStock,
      lowStock,
    };
  },

  // توليد SKU تلقائي
  generateSku: (prefix: string = "PRD"): string => {
    let counter = 1;
    let sku: string;
    
    do {
      sku = `${prefix}-${counter.toString().padStart(3, '0')}`;
      counter++;
    } while (ProductsStore.getBySku(sku));
    
    return sku;
  },

  // توليد slug تلقائي
  generateSlug: (name: string, sku?: string): string => {
    let baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9\u0600-\u06FF\s-]/g, "")
      .replace(/\s+/g, "-")
      .trim();
    
    if (sku) {
      baseSlug += `-${sku.toLowerCase()}`;
    }
    
    let slug = baseSlug;
    let counter = 1;
    
    while (products.some(p => p.slug === slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }
    
    return slug;
  },

  // إعادة تحميل البيانات من التخزين المحلي
  reload: (): void => {
    products = loadProductsFromStorage();
  },

  // مسح جميع البيانات
  clear: (): void => {
    products = [];
    saveProductsToStorage(products);
  },

  // تصدير البيانات
  export: (): Product[] => {
    return [...products];
  },

  // استيراد البيانات
  import: (importedProducts: Product[]): void => {
    products = [...importedProducts];
    saveProductsToStorage(products);
  },
};
