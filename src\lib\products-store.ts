// نظام إدارة حالة المنتجات المركزي

export interface Product {
  id: string;
  name: string;
  nameEn: string;
  sku: string;
  category: string;
  brand: string;
  price: number;
  priceCurrency: string;
  comparePrice?: number;
  comparePriceCurrency?: string;
  cost?: number;
  costCurrency?: string;
  stock: number;
  minQuantity?: number;
  maxQuantity?: number;
  status: "active" | "draft" | "inactive";
  image: string;
  images: string[];
  description: string;
  shortDescription?: string;
  tags: string[];
  specifications: { key: string; value: string }[];
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  metaTitle?: string;
  metaDescription?: string;
  slug: string;
  featured: boolean;
  allowBackorder: boolean;
  trackQuantity: boolean;
  location?: string;
  barcode?: string;
  createdAt: string;
  updatedAt: string;
}

// بيانات وهمية للمنتجات
let products: Product[] = [
  {
    id: "1",
    name: "عدسات أكيوفيو اليومية",
    nameEn: "Acuvue Oasys Daily",
    sku: "ACU-001",
    category: "العدسات اليومية",
    brand: "Johnson & Johnson",
    price: 120000,
    priceCurrency: "IQD",
    cost: 80000,
    costCurrency: "IQD",
    stock: 45,
    status: "active",
    image: "https://picsum.photos/400/400?random=1",
    images: ["https://picsum.photos/400/400?random=1"],
    description: "عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي",
    tags: ["يومية", "مريحة", "آمنة"],
    specifications: [
      { key: "النوع", value: "عدسات يومية" },
      { key: "المادة", value: "سيليكون هيدروجيل" }
    ],
    slug: "acuvue-oasys-daily",
    featured: true,
    allowBackorder: false,
    trackQuantity: true,
    createdAt: "2024-01-10T10:00:00Z",
    updatedAt: "2024-01-10T10:00:00Z",
  },
  {
    id: "2",
    name: "عدسات بايوفينيتي الشهرية",
    nameEn: "Biofinity Monthly",
    sku: "BIO-002",
    category: "العدسات الشهرية",
    brand: "CooperVision",
    price: 85000,
    priceCurrency: "IQD",
    cost: 55000,
    costCurrency: "IQD",
    stock: 32,
    status: "active",
    image: "https://picsum.photos/400/400?random=2",
    images: ["https://picsum.photos/400/400?random=2"],
    description: "عدسات لاصقة شهرية عالية الجودة",
    tags: ["شهرية", "جودة عالية"],
    specifications: [
      { key: "النوع", value: "عدسات شهرية" },
      { key: "المادة", value: "سيليكون هيدروجيل" }
    ],
    slug: "biofinity-monthly",
    featured: false,
    allowBackorder: false,
    trackQuantity: true,
    createdAt: "2024-01-08T10:00:00Z",
    updatedAt: "2024-01-08T10:00:00Z",
  },
  {
    id: "3",
    name: "عدسات إير أوبتكس الملونة",
    nameEn: "Air Optix Colors",
    sku: "AIR-003",
    category: "العدسات الملونة",
    brand: "Alcon",
    price: 95000,
    priceCurrency: "IQD",
    cost: 65000,
    costCurrency: "IQD",
    stock: 0,
    status: "active",
    image: "https://picsum.photos/400/400?random=3",
    images: ["https://picsum.photos/400/400?random=3"],
    description: "عدسات لاصقة ملونة آمنة وجميلة",
    tags: ["ملونة", "جميلة", "آمنة"],
    specifications: [
      { key: "النوع", value: "عدسات ملونة" },
      { key: "المدة", value: "شهرية" }
    ],
    slug: "air-optix-colors",
    featured: true,
    allowBackorder: true,
    trackQuantity: true,
    createdAt: "2024-01-05T10:00:00Z",
    updatedAt: "2024-01-05T10:00:00Z",
  },
];

// دوال إدارة المنتجات
export const ProductsStore = {
  // الحصول على جميع المنتجات
  getAll: (): Product[] => {
    return [...products];
  },

  // الحصول على منتج بالمعرف
  getById: (id: string): Product | undefined => {
    return products.find(p => p.id === id);
  },

  // الحصول على منتج بالـ SKU
  getBySku: (sku: string): Product | undefined => {
    return products.find(p => p.sku === sku);
  },

  // إضافة منتج جديد
  add: (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Product => {
    const newProduct: Product = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    products.push(newProduct);
    return newProduct;
  },

  // تحديث منتج
  update: (id: string, updates: Partial<Product>): Product | null => {
    const index = products.findIndex(p => p.id === id);
    if (index === -1) return null;

    products[index] = {
      ...products[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    return products[index];
  },

  // حذف منتج
  delete: (id: string): boolean => {
    const index = products.findIndex(p => p.id === id);
    if (index === -1) return false;

    products.splice(index, 1);
    return true;
  },

  // حذف منتجات متعددة
  deleteMultiple: (ids: string[]): number => {
    let deletedCount = 0;
    ids.forEach(id => {
      if (ProductsStore.delete(id)) {
        deletedCount++;
      }
    });
    return deletedCount;
  },

  // البحث في المنتجات
  search: (query: string): Product[] => {
    const lowerQuery = query.toLowerCase();
    return products.filter(product => 
      product.name.toLowerCase().includes(lowerQuery) ||
      product.nameEn.toLowerCase().includes(lowerQuery) ||
      product.sku.toLowerCase().includes(lowerQuery) ||
      product.brand.toLowerCase().includes(lowerQuery) ||
      product.category.toLowerCase().includes(lowerQuery)
    );
  },

  // فلترة المنتجات
  filter: (filters: {
    category?: string;
    brand?: string;
    status?: string;
    inStock?: boolean;
  }): Product[] => {
    return products.filter(product => {
      if (filters.category && filters.category !== "الكل" && product.category !== filters.category) {
        return false;
      }
      if (filters.brand && filters.brand !== "الكل" && product.brand !== filters.brand) {
        return false;
      }
      if (filters.status && filters.status !== "الكل" && product.status !== filters.status) {
        return false;
      }
      if (filters.inStock !== undefined) {
        const hasStock = product.stock > 0;
        if (filters.inStock !== hasStock) {
          return false;
        }
      }
      return true;
    });
  },

  // الحصول على الإحصائيات
  getStats: () => {
    const total = products.length;
    const active = products.filter(p => p.status === "active").length;
    const draft = products.filter(p => p.status === "draft").length;
    const outOfStock = products.filter(p => p.stock === 0).length;
    const totalStock = products.reduce((sum, p) => sum + p.stock, 0);
    const lowStock = products.filter(p => p.stock > 0 && p.stock <= 10).length;

    return {
      total,
      active,
      draft,
      outOfStock,
      totalStock,
      lowStock,
    };
  },

  // توليد SKU تلقائي
  generateSku: (prefix: string = "PRD"): string => {
    let counter = 1;
    let sku: string;
    
    do {
      sku = `${prefix}-${counter.toString().padStart(3, '0')}`;
      counter++;
    } while (ProductsStore.getBySku(sku));
    
    return sku;
  },

  // توليد slug تلقائي
  generateSlug: (name: string, sku?: string): string => {
    let baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9\u0600-\u06FF\s-]/g, "")
      .replace(/\s+/g, "-")
      .trim();
    
    if (sku) {
      baseSlug += `-${sku.toLowerCase()}`;
    }
    
    let slug = baseSlug;
    let counter = 1;
    
    while (products.some(p => p.slug === slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }
    
    return slug;
  },
};
