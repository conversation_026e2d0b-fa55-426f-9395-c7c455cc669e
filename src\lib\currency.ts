// نظام إدارة العملات وأسعار الصرف

export interface Currency {
  code: string;
  name: string;
  nameAr: string;
  symbol: string;
  symbolAr: string;
  isDefault: boolean;
  isActive: boolean;
  exchangeRate: number; // مقابل الدينار العراقي
  lastUpdated: string;
}

export interface ExchangeRate {
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  lastUpdated: string;
  source: string;
}

// العملات المدعومة
export const SUPPORTED_CURRENCIES: Currency[] = [
  {
    code: "IQD",
    name: "Iraqi Dinar",
    nameAr: "دينار عراقي",
    symbol: "IQD",
    symbolAr: "د.ع",
    isDefault: true,
    isActive: true,
    exchangeRate: 1, // العملة الأساسية
    lastUpdated: new Date().toISOString(),
  },
  {
    code: "USD",
    name: "US Dollar",
    nameAr: "دولار أمريكي",
    symbol: "$",
    symbolAr: "$",
    isDefault: false,
    isActive: true,
    exchangeRate: 1310, // 1 دولار = 1310 دينار عراقي (سعر تقريبي)
    lastUpdated: new Date().toISOString(),
  },
];

// الحصول على العملة الافتراضية
export const getDefaultCurrency = (): Currency => {
  return SUPPORTED_CURRENCIES.find(c => c.isDefault) || SUPPORTED_CURRENCIES[0];
};

// الحصول على عملة بالكود
export const getCurrencyByCode = (code: string): Currency | undefined => {
  return SUPPORTED_CURRENCIES.find(c => c.code === code);
};

// تحويل المبلغ من عملة إلى أخرى
export const convertCurrency = (
  amount: number,
  fromCurrency: string,
  toCurrency: string
): number => {
  const fromCurr = getCurrencyByCode(fromCurrency);
  const toCurr = getCurrencyByCode(toCurrency);
  
  if (!fromCurr || !toCurr) {
    throw new Error("عملة غير مدعومة");
  }
  
  // إذا كانت نفس العملة
  if (fromCurrency === toCurrency) {
    return amount;
  }
  
  // تحويل إلى الدينار العراقي أولاً
  let amountInIQD: number;
  if (fromCurrency === "IQD") {
    amountInIQD = amount;
  } else {
    amountInIQD = amount * fromCurr.exchangeRate;
  }
  
  // ثم تحويل إلى العملة المطلوبة
  if (toCurrency === "IQD") {
    return Math.round(amountInIQD);
  } else {
    return Math.round((amountInIQD / toCurr.exchangeRate) * 100) / 100;
  }
};

// تنسيق المبلغ حسب العملة
export const formatCurrency = (
  amount: number,
  currencyCode: string = "IQD",
  showSymbol: boolean = true
): string => {
  const currency = getCurrencyByCode(currencyCode);
  if (!currency) {
    return amount.toString();
  }
  
  // تنسيق الأرقام حسب العملة
  let formattedAmount: string;
  
  if (currencyCode === "IQD") {
    // الدينار العراقي - بدون فواصل عشرية
    formattedAmount = Math.round(amount).toLocaleString("ar-IQ");
  } else {
    // العملات الأخرى - مع فاصلتين عشريتين
    formattedAmount = amount.toLocaleString("ar-IQ", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }
  
  if (showSymbol) {
    return `${formattedAmount} ${currency.symbolAr}`;
  }
  
  return formattedAmount;
};

// تحديث سعر الصرف
export const updateExchangeRate = async (
  currencyCode: string,
  newRate: number
): Promise<boolean> => {
  try {
    const currencyIndex = SUPPORTED_CURRENCIES.findIndex(c => c.code === currencyCode);
    if (currencyIndex === -1) {
      throw new Error("عملة غير موجودة");
    }
    
    SUPPORTED_CURRENCIES[currencyIndex].exchangeRate = newRate;
    SUPPORTED_CURRENCIES[currencyIndex].lastUpdated = new Date().toISOString();
    
    // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات
    console.log(`تم تحديث سعر صرف ${currencyCode} إلى ${newRate}`);
    
    return true;
  } catch (error) {
    console.error("خطأ في تحديث سعر الصرف:", error);
    return false;
  }
};

// جلب أسعار الصرف من مصدر خارجي (محاكاة)
export const fetchExchangeRates = async (): Promise<ExchangeRate[]> => {
  try {
    // محاكاة استدعاء API خارجي
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // أسعار وهمية - في التطبيق الحقيقي سيتم جلبها من API
    const rates: ExchangeRate[] = [
      {
        fromCurrency: "USD",
        toCurrency: "IQD",
        rate: 1310,
        lastUpdated: new Date().toISOString(),
        source: "Central Bank of Iraq",
      },
    ];
    
    return rates;
  } catch (error) {
    console.error("خطأ في جلب أسعار الصرف:", error);
    return [];
  }
};

// تحديث أسعار الصرف تلقائياً
export const autoUpdateExchangeRates = async (): Promise<void> => {
  try {
    const rates = await fetchExchangeRates();
    
    for (const rate of rates) {
      await updateExchangeRate(rate.fromCurrency, rate.rate);
    }
    
    console.log("تم تحديث أسعار الصرف تلقائياً");
  } catch (error) {
    console.error("خطأ في التحديث التلقائي لأسعار الصرف:", error);
  }
};

// حساب الربح بالعملة المحلية
export const calculateProfitInIQD = (
  costPrice: number,
  costCurrency: string,
  sellingPrice: number,
  sellingCurrency: string = "IQD"
): {
  costInIQD: number;
  sellingPriceInIQD: number;
  profitInIQD: number;
  profitMargin: number;
} => {
  const costInIQD = convertCurrency(costPrice, costCurrency, "IQD");
  const sellingPriceInIQD = convertCurrency(sellingPrice, sellingCurrency, "IQD");
  const profitInIQD = sellingPriceInIQD - costInIQD;
  const profitMargin = sellingPriceInIQD > 0 ? (profitInIQD / sellingPriceInIQD) * 100 : 0;
  
  return {
    costInIQD,
    sellingPriceInIQD,
    profitInIQD,
    profitMargin,
  };
};

// تحديث utils.ts لاستخدام النظام الجديد
export const formatPrice = (
  amount: number,
  currencyCode: string = "IQD"
): string => {
  return formatCurrency(amount, currencyCode, true);
};
