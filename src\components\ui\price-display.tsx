"use client";

import React from "react";
import { DollarSign, TrendingUp, TrendingDown } from "lucide-react";
import { formatCurrency, convertCurrency, getCurrencyByCode } from "@/lib/currency";

interface PriceDisplayProps {
  price: number;
  currency?: string;
  originalPrice?: number;
  originalCurrency?: string;
  showConversion?: boolean;
  showDiscount?: boolean;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function PriceDisplay({
  price,
  currency = "IQD",
  originalPrice,
  originalCurrency,
  showConversion = false,
  showDiscount = true,
  size = "md",
  className = "",
}: PriceDisplayProps) {
  const currencyInfo = getCurrencyByCode(currency);
  
  // تحويل السعر إلى الدينار العراقي إذا لزم الأمر
  const priceInIQD = currency === "IQD" ? price : convertCurrency(price, currency, "IQD");
  
  // حساب الخصم إذا كان هناك سعر أصلي
  let discountPercent = 0;
  let originalPriceInIQD = 0;
  
  if (originalPrice && originalCurrency) {
    originalPriceInIQD = originalCurrency === "IQD" 
      ? originalPrice 
      : convertCurrency(originalPrice, originalCurrency, "IQD");
    
    if (originalPriceInIQD > priceInIQD) {
      discountPercent = ((originalPriceInIQD - priceInIQD) / originalPriceInIQD) * 100;
    }
  }

  const sizeClasses = {
    sm: "text-sm",
    md: "text-lg",
    lg: "text-2xl",
  };

  const discountSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
  };

  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      {/* السعر الرئيسي */}
      <div className="flex items-center gap-2">
        <span className={`font-bold text-green-600 ${sizeClasses[size]}`}>
          {formatCurrency(priceInIQD, "IQD")}
        </span>
        
        {/* أيقونة العملة */}
        {currencyInfo && (
          <span className="text-gray-500 text-xs">
            {currencyInfo.symbolAr}
          </span>
        )}
      </div>

      {/* السعر الأصلي والخصم */}
      {showDiscount && originalPrice && originalCurrency && discountPercent > 0 && (
        <div className="flex items-center gap-2">
          <span className={`text-gray-500 line-through ${discountSizeClasses[size]}`}>
            {formatCurrency(originalPriceInIQD, "IQD")}
          </span>
          <span className={`bg-red-100 text-red-800 px-2 py-1 rounded-full ${discountSizeClasses[size]} font-medium`}>
            -{discountPercent.toFixed(0)}%
          </span>
        </div>
      )}

      {/* عرض التحويل */}
      {showConversion && currency !== "IQD" && (
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <DollarSign className="h-3 w-3" />
          <span>
            {formatCurrency(price, currency)} = {formatCurrency(priceInIQD, "IQD")}
          </span>
        </div>
      )}
    </div>
  );
}

interface PriceComparisonProps {
  currentPrice: number;
  previousPrice: number;
  currency?: string;
  showPercentage?: boolean;
  className?: string;
}

export function PriceComparison({
  currentPrice,
  previousPrice,
  currency = "IQD",
  showPercentage = true,
  className = "",
}: PriceComparisonProps) {
  const difference = currentPrice - previousPrice;
  const percentageChange = previousPrice > 0 ? (difference / previousPrice) * 100 : 0;
  const isIncrease = difference > 0;
  const isDecrease = difference < 0;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="font-medium">
        {formatCurrency(currentPrice, currency)}
      </span>
      
      {difference !== 0 && (
        <div className={`flex items-center gap-1 text-sm ${
          isIncrease ? 'text-red-600' : isDecrease ? 'text-green-600' : 'text-gray-600'
        }`}>
          {isIncrease ? (
            <TrendingUp className="h-4 w-4" />
          ) : isDecrease ? (
            <TrendingDown className="h-4 w-4" />
          ) : null}
          
          <span>
            {isIncrease ? '+' : ''}{formatCurrency(Math.abs(difference), currency)}
          </span>
          
          {showPercentage && (
            <span className="text-xs">
              ({isIncrease ? '+' : ''}{percentageChange.toFixed(1)}%)
            </span>
          )}
        </div>
      )}
    </div>
  );
}

interface CurrencyConverterProps {
  amount: number;
  fromCurrency: string;
  toCurrency?: string;
  showBothPrices?: boolean;
  className?: string;
}

export function CurrencyConverter({
  amount,
  fromCurrency,
  toCurrency = "IQD",
  showBothPrices = true,
  className = "",
}: CurrencyConverterProps) {
  const convertedAmount = convertCurrency(amount, fromCurrency, toCurrency);
  
  if (!showBothPrices) {
    return (
      <span className={className}>
        {formatCurrency(convertedAmount, toCurrency)}
      </span>
    );
  }

  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      <span className="font-medium">
        {formatCurrency(amount, fromCurrency)}
      </span>
      {fromCurrency !== toCurrency && (
        <span className="text-sm text-gray-600">
          ≈ {formatCurrency(convertedAmount, toCurrency)}
        </span>
      )}
    </div>
  );
}

interface ProfitDisplayProps {
  costPrice: number;
  costCurrency: string;
  sellingPrice: number;
  sellingCurrency?: string;
  showMargin?: boolean;
  className?: string;
}

export function ProfitDisplay({
  costPrice,
  costCurrency,
  sellingPrice,
  sellingCurrency = "IQD",
  showMargin = true,
  className = "",
}: ProfitDisplayProps) {
  // تحويل الأسعار إلى الدينار العراقي للحساب
  const costInIQD = convertCurrency(costPrice, costCurrency, "IQD");
  const sellingPriceInIQD = convertCurrency(sellingPrice, sellingCurrency, "IQD");
  
  const profit = sellingPriceInIQD - costInIQD;
  const profitMargin = sellingPriceInIQD > 0 ? (profit / sellingPriceInIQD) * 100 : 0;
  
  const isProfit = profit > 0;
  const isLoss = profit < 0;

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-gray-600">التكلفة:</span>
          <div className="font-medium">
            {formatCurrency(costInIQD, "IQD")}
          </div>
        </div>
        <div>
          <span className="text-gray-600">البيع:</span>
          <div className="font-medium">
            {formatCurrency(sellingPriceInIQD, "IQD")}
          </div>
        </div>
      </div>
      
      <div className="border-t pt-2">
        <div className="flex items-center justify-between">
          <span className="text-gray-600">الربح:</span>
          <span className={`font-bold ${
            isProfit ? 'text-green-600' : isLoss ? 'text-red-600' : 'text-gray-600'
          }`}>
            {formatCurrency(Math.abs(profit), "IQD")}
          </span>
        </div>
        
        {showMargin && (
          <div className="flex items-center justify-between mt-1">
            <span className="text-gray-600 text-sm">هامش الربح:</span>
            <span className={`font-medium text-sm ${
              isProfit ? 'text-green-600' : isLoss ? 'text-red-600' : 'text-gray-600'
            }`}>
              {profitMargin.toFixed(1)}%
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
