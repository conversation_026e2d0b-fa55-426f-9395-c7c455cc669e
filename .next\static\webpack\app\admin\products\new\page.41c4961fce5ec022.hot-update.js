"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/new/page",{

/***/ "(app-pages-browser)/./src/lib/products-store.ts":
/*!***********************************!*\
  !*** ./src/lib/products-store.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductsStore: () => (/* binding */ ProductsStore)\n/* harmony export */ });\n// نظام إدارة حالة المنتجات المركزي\n// مفتاح التخزين المحلي\nconst STORAGE_KEY = 'visionlens_products';\n// تحميل المنتجات من التخزين المحلي\nconst loadProductsFromStorage = ()=>{\n    if (false) {}\n    try {\n        const stored = localStorage.getItem(STORAGE_KEY);\n        if (stored) {\n            return JSON.parse(stored);\n        }\n    } catch (error) {\n        console.error('Error loading products from storage:', error);\n    }\n    return [];\n};\n// حفظ المنتجات في التخزين المحلي\nconst saveProductsToStorage = (products)=>{\n    if (false) {}\n    try {\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(products));\n    } catch (error) {\n        console.error('Error saving products to storage:', error);\n    }\n};\n// قائمة المنتجات - تحميل من التخزين المحلي\nlet products = loadProductsFromStorage();\n// دوال إدارة المنتجات\nconst ProductsStore = {\n    // الحصول على جميع المنتجات\n    getAll: ()=>{\n        return [\n            ...products\n        ];\n    },\n    // الحصول على منتج بالمعرف\n    getById: (id)=>{\n        return products.find((p)=>p.id === id);\n    },\n    // الحصول على منتج بالمعرف (اسم مختصر)\n    get: (id)=>{\n        return products.find((p)=>p.id === id);\n    },\n    // الحصول على منتج بالـ SKU\n    getBySku: (sku)=>{\n        return products.find((p)=>p.sku === sku);\n    },\n    // إضافة منتج جديد\n    add: (productData)=>{\n        const newProduct = {\n            ...productData,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        products.push(newProduct);\n        saveProductsToStorage(products); // حفظ في التخزين المحلي\n        return newProduct;\n    },\n    // تحديث منتج\n    update: (id, updates)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        products[index] = {\n            ...products[index],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        saveProductsToStorage(products); // حفظ في التخزين المحلي\n        return products[index];\n    },\n    // حذف منتج\n    delete: (id)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        products.splice(index, 1);\n        saveProductsToStorage(products); // حفظ في التخزين المحلي\n        return true;\n    },\n    // حذف منتجات متعددة\n    deleteMultiple: (ids)=>{\n        let deletedCount = 0;\n        ids.forEach((id)=>{\n            const index = products.findIndex((p)=>p.id === id);\n            if (index !== -1) {\n                products.splice(index, 1);\n                deletedCount++;\n            }\n        });\n        if (deletedCount > 0) {\n            saveProductsToStorage(products); // حفظ في التخزين المحلي\n        }\n        return deletedCount;\n    },\n    // البحث في المنتجات\n    search: (query)=>{\n        const lowerQuery = query.toLowerCase();\n        return products.filter((product)=>product.name.toLowerCase().includes(lowerQuery) || product.nameEn.toLowerCase().includes(lowerQuery) || product.sku.toLowerCase().includes(lowerQuery) || product.brand.toLowerCase().includes(lowerQuery) || product.category.toLowerCase().includes(lowerQuery));\n    },\n    // فلترة المنتجات\n    filter: (filters)=>{\n        return products.filter((product)=>{\n            if (filters.category && filters.category !== \"الكل\" && product.category !== filters.category) {\n                return false;\n            }\n            if (filters.brand && filters.brand !== \"الكل\" && product.brand !== filters.brand) {\n                return false;\n            }\n            if (filters.status && filters.status !== \"الكل\" && product.status !== filters.status) {\n                return false;\n            }\n            if (filters.inStock !== undefined) {\n                const hasStock = product.stock > 0;\n                if (filters.inStock !== hasStock) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    },\n    // الحصول على الإحصائيات\n    getStats: ()=>{\n        const total = products.length;\n        const active = products.filter((p)=>p.status === \"active\").length;\n        const draft = products.filter((p)=>p.status === \"draft\").length;\n        const outOfStock = products.filter((p)=>p.stock === 0).length;\n        const totalStock = products.reduce((sum, p)=>sum + p.stock, 0);\n        const lowStock = products.filter((p)=>p.stock > 0 && p.stock <= 10).length;\n        return {\n            total,\n            active,\n            draft,\n            outOfStock,\n            totalStock,\n            lowStock\n        };\n    },\n    // توليد SKU تلقائي\n    generateSku: function() {\n        let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"PRD\";\n        let counter = 1;\n        let sku;\n        do {\n            sku = \"\".concat(prefix, \"-\").concat(counter.toString().padStart(3, '0'));\n            counter++;\n        }while (ProductsStore.getBySku(sku));\n        return sku;\n    },\n    // توليد slug تلقائي\n    generateSlug: (name, sku)=>{\n        let baseSlug = name.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n        if (sku) {\n            baseSlug += \"-\".concat(sku.toLowerCase());\n        }\n        let slug = baseSlug;\n        let counter = 1;\n        while(products.some((p)=>p.slug === slug)){\n            slug = \"\".concat(baseSlug, \"-\").concat(counter);\n            counter++;\n        }\n        return slug;\n    },\n    // إعادة تحميل البيانات من التخزين المحلي\n    reload: ()=>{\n        products = loadProductsFromStorage();\n    },\n    // مسح جميع البيانات\n    clear: ()=>{\n        products = [];\n        saveProductsToStorage(products);\n    },\n    // تصدير البيانات\n    export: ()=>{\n        return [\n            ...products\n        ];\n    },\n    // استيراد البيانات\n    import: (importedProducts)=>{\n        products = [\n            ...importedProducts\n        ];\n        saveProductsToStorage(products);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/products-store.ts\n"));

/***/ })

});