globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/admin/products/bulk-actions/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/next-themes/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/header.tsx":{"*":{"id":"(ssr)/./src/components/layout/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/product/product-card.tsx":{"*":{"id":"(ssr)/./src/components/product/product-card.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/orders/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/orders/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/page.tsx":{"*":{"id":"(ssr)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/categories/[slug]/page.tsx":{"*":{"id":"(ssr)/./src/app/categories/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/customers/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/customers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/analytics/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/cart/page.tsx":{"*":{"id":"(ssr)/./src/app/cart/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/[slug]/page.tsx":{"*":{"id":"(ssr)/./src/app/products/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/account/page.tsx":{"*":{"id":"(ssr)/./src/app/account/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/new/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/products/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/products/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/currency/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/currency/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/categories/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/categories/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/coupons/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/coupons/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/contact/page.tsx":{"*":{"id":"(ssr)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/bulk-actions/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/products/bulk-actions/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"G:\\visionlens\\node_modules\\next-themes\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\visionlens\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\visionlens\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\"],\"variable\":\"--font-cairo\",\"display\":\"swap\"}],\"variableName\":\"cairo\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\visionlens\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\visionlens\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/admin/page","static/chunks/app/admin/page.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/admin/page","static/chunks/app/admin/page.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/admin/layout","static/chunks/app/admin/layout.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/admin/layout","static/chunks/app/admin/layout.js"],"async":false},"G:\\visionlens\\src\\components\\layout\\header.tsx":{"id":"(app-pages-browser)/./src/components/layout/header.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"G:\\visionlens\\src\\components\\product\\product-card.tsx":{"id":"(app-pages-browser)/./src/components/product/product-card.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\visionlens\\src\\app\\admin\\products\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/products/page.tsx","name":"*","chunks":["app/admin/products/page","static/chunks/app/admin/products/page.js"],"async":false},"G:\\visionlens\\src\\app\\admin\\orders\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/orders/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\products\\page.tsx":{"id":"(app-pages-browser)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\categories\\[slug]\\page.tsx":{"id":"(app-pages-browser)/./src/app/categories/[slug]/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\customers\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/customers/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\analytics\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\cart\\page.tsx":{"id":"(app-pages-browser)/./src/app/cart/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\products\\[slug]\\page.tsx":{"id":"(app-pages-browser)/./src/app/products/[slug]/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\account\\page.tsx":{"id":"(app-pages-browser)/./src/app/account/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/settings/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\products\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/products/new/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\products\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/products/settings/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\currency\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/currency/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\categories\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/categories/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\coupons\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/coupons/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\contact\\page.tsx":{"id":"(app-pages-browser)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false},"G:\\visionlens\\src\\app\\admin\\products\\bulk-actions\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/products/bulk-actions/page.tsx","name":"*","chunks":["app/admin/products/bulk-actions/page","static/chunks/app/admin/products/bulk-actions/page.js"],"async":false}},"entryCSSFiles":{"G:\\visionlens\\src\\":[],"G:\\visionlens\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"G:\\visionlens\\src\\app\\page":[],"G:\\visionlens\\src\\app\\admin\\layout":[],"G:\\visionlens\\src\\app\\admin\\page":[],"G:\\visionlens\\src\\app\\admin\\products\\page":[],"G:\\visionlens\\src\\app\\admin\\products\\bulk-actions\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/next-themes/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/header.tsx":{"*":{"id":"(rsc)/./src/components/layout/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/product/product-card.tsx":{"*":{"id":"(rsc)/./src/components/product/product-card.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/orders/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/orders/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/page.tsx":{"*":{"id":"(rsc)/./src/app/products/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/categories/[slug]/page.tsx":{"*":{"id":"(rsc)/./src/app/categories/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/customers/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/customers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/analytics/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/cart/page.tsx":{"*":{"id":"(rsc)/./src/app/cart/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/products/[slug]/page.tsx":{"*":{"id":"(rsc)/./src/app/products/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/account/page.tsx":{"*":{"id":"(rsc)/./src/app/account/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/settings/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/new/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/products/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/settings/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/products/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/currency/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/currency/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/categories/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/categories/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/coupons/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/coupons/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/contact/page.tsx":{"*":{"id":"(rsc)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/bulk-actions/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/products/bulk-actions/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}