import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Cairo } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { Toaster } from "react-hot-toast";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const cairo = Cairo({
  subsets: ["arabic"],
  variable: "--font-cairo",
  display: "swap",
});

export const metadata: Metadata = {
  title: "VisionLens - متجر العدسات اللاصقة والنظارات الطبية",
  description: "متجر إلكتروني متخصص في بيع العدسات اللاصقة والنظارات الطبية بأفضل الأسعار وأعلى جودة",
  keywords: "عدسات لاصقة, نظارات طبية, عدسات ملونة, عدسات يومية, عدسات شهرية",
  authors: [{ name: "VisionLens Team" }],
  creator: "VisionLens",
  publisher: "VisionLens",
  icons: {
    icon: "/images/logo.png",
    shortcut: "/images/logo.png",
    apple: "/images/logo.png",
  },
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"),
  openGraph: {
    title: "VisionLens - متجر العدسات اللاصقة والنظارات الطبية",
    description: "متجر إلكتروني متخصص في بيع العدسات اللاصقة والنظارات الطبية",
    url: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
    siteName: "VisionLens",
    locale: "ar_SA",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "VisionLens - متجر العدسات اللاصقة والنظارات الطبية",
    description: "متجر إلكتروني متخصص في بيع العدسات اللاصقة والنظارات الطبية",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body className={`${inter.variable} ${cairo.variable} arabic antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster
            position="top-center"
            toastOptions={{
              duration: 4000,
              style: {
                background: "hsl(var(--background))",
                color: "hsl(var(--foreground))",
                border: "1px solid hsl(var(--border))",
              },
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  );
}
