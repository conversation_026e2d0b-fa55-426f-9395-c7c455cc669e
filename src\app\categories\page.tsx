import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ArrowR<PERSON>, Eye } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import { ProductsStore } from "@/lib/products-store";

// بيانات الفئات
const categories = [
  {
    id: "daily-lenses",
    name: "العدسات اليومية",
    description: "عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي",
    image: "https://picsum.photos/400/300?random=10",
    featured: true,
    benefits: [
      "راحة طوال اليوم",
      "سهولة الاستخدام",
      "نظافة مضمونة",
      "لا تحتاج تنظيف"
    ]
  },
  {
    id: "monthly-lenses",
    name: "العدسات الشهرية",
    description: "عدسات لاصقة شهرية اقتصادية وعملية",
    image: "https://picsum.photos/400/300?random=11",
    featured: true,
    benefits: [
      "اقتصادية",
      "جودة عالية",
      "مقاومة للترسبات",
      "راحة طويلة المدى"
    ]
  },
  {
    id: "colored-lenses",
    name: "العدسات الملونة",
    description: "عدسات ملونة لإطلالة مميزة وجذابة",
    image: "https://picsum.photos/400/300?random=12",
    featured: true,
    benefits: [
      "ألوان طبيعية",
      "تغطية ممتازة",
      "آمنة ومريحة",
      "تصاميم متنوعة"
    ]
  },
  {
    id: "glasses",
    name: "النظارات الطبية",
    description: "نظارات طبية عالية الجودة لتصحيح البصر",
    image: "https://picsum.photos/400/300?random=14",
    featured: true,
    benefits: [
      "تصحيح دقيق للبصر",
      "تصاميم عصرية",
      "خامات عالية الجودة",
      "راحة في الارتداء"
    ]
  }
];

export default function CategoriesPage() {
  // الحصول على عدد المنتجات لكل فئة من النظام المركزي
  const allProducts = ProductsStore.getAll();

  const getCategoryCount = (categoryName: string) => {
    return allProducts.filter(product => product.category === categoryName).length;
  };

  // إضافة عدد المنتجات لكل فئة
  const categoriesWithCount = categories.map(category => ({
    ...category,
    productCount: getCategoryCount(category.name)
  }));

  const featuredCategories = categoriesWithCount.filter(cat => cat.featured);

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان والوصف */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">فئات المنتجات</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            اكتشف مجموعتنا الواسعة من العدسات اللاصقة والنظارات الطبية المصممة لتلبية جميع احتياجاتك
          </p>
        </div>

        {/* الفئات المميزة */}
        <section className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold">الفئات المميزة</h2>
            <div className="h-1 flex-1 bg-gradient-to-r from-primary to-transparent mx-8"></div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredCategories.map((category) => (
              <Card key={category.id} className="group overflow-hidden hover:shadow-xl transition-all duration-300">
                <div className="relative aspect-[4/3] overflow-hidden">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                  <div className="absolute bottom-4 left-4 right-4 text-white">
                    <h3 className="text-xl font-bold mb-1">{category.name}</h3>
                    <p className="text-sm opacity-90">{category.productCount} منتج</p>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">
                      مميز
                    </span>
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {category.description}
                  </p>
                  
                  <div className="space-y-2 mb-6">
                    {category.benefits.slice(0, 3).map((benefit, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                        <span>{benefit}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button asChild className="flex-1">
                      <Link href={`/categories/${category.id}`}>
                        تصفح المنتجات
                        <ArrowRight className="mr-2 h-4 w-4" />
                      </Link>
                    </Button>
                    <Button variant="outline" size="icon" asChild>
                      <Link href={`/categories/${category.id}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>



        {/* قسم المساعدة */}
        <section className="mt-16 bg-gradient-to-r from-primary/10 to-primary/5 rounded-2xl p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">تحتاج مساعدة في الاختيار؟</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            فريقنا من الخبراء جاهز لمساعدتك في اختيار المنتج المناسب لاحتياجاتك
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/contact">
                تواصل معنا
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/guide">
                دليل الاختيار
              </Link>
            </Button>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
