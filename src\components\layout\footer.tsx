import React from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  Truck,
  Shield,
  Clock,
} from "lucide-react";

export default function Footer() {
  return (
    <footer className="bg-muted/50 border-t">
      {/* المميزات */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="flex items-center gap-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <Truck className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold">شحن مجاني</h3>
                <p className="text-sm text-muted-foreground">للطلبات أكثر من 200 ريال</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold">ضمان الجودة</h3>
                <p className="text-sm text-muted-foreground">منتجات أصلية 100%</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <Clock className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold">دعم 24/7</h3>
                <p className="text-sm text-muted-foreground">خدمة عملاء متواصلة</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <CreditCard className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold">دفع آمن</h3>
                <p className="text-sm text-muted-foreground">طرق دفع متعددة</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* معلومات الشركة */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Image
                src="/images/logo-transparent.svg"
                alt="VisionLens Logo"
                width={220}
                height={70}
                className="h-18 w-auto"
              />
            </div>
            <p className="text-sm text-muted-foreground">
              متجرك الموثوق للعدسات اللاصقة والنظارات الطبية في العراق. نقدم أفضل المنتجات
              بأعلى جودة وأفضل الأسعار.
            </p>
            <div className="flex space-x-4">
              <Link
                href="#"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Facebook className="h-5 w-5" />
              </Link>
              <Link
                href="#"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Twitter className="h-5 w-5" />
              </Link>
              <Link
                href="#"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Instagram className="h-5 w-5" />
              </Link>
              <Link
                href="#"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Youtube className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* روابط سريعة */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">روابط سريعة</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  من نحن
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  تواصل معنا
                </Link>
              </li>
              <li>
                <Link
                  href="/shipping"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  الشحن والتوصيل
                </Link>
              </li>
              <li>
                <Link
                  href="/returns"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  سياسة الإرجاع
                </Link>
              </li>
              <li>
                <Link
                  href="/faq"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  الأسئلة الشائعة
                </Link>
              </li>
            </ul>
          </div>

          {/* الفئات */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">فئات المنتجات</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/categories/contact-lenses"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  العدسات اللاصقة
                </Link>
              </li>
              <li>
                <Link
                  href="/categories/colored-lenses"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  العدسات الملونة
                </Link>
              </li>
              <li>
                <Link
                  href="/categories/glasses"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  النظارات الطبية
                </Link>
              </li>
              <li>
                <Link
                  href="/categories/sunglasses"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  النظارات الشمسية
                </Link>
              </li>
              <li>
                <Link
                  href="/categories/accessories"
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  الإكسسوارات
                </Link>
              </li>
            </ul>
          </div>

          {/* معلومات التواصل */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">تواصل معنا</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-primary" />
                <span className="text-sm">+964 ************</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-primary" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-start gap-3">
                <MapPin className="h-4 w-4 text-primary mt-0.5" />
                <span className="text-sm">
                  بغداد، العراق
                  <br />
                  منطقة الكرادة، شارع أبو نواس
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* الحقوق */}
      <div className="border-t">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm text-muted-foreground">
              © 2024 VisionLens Iraq. جميع الحقوق محفوظة.
            </p>
            <div className="flex items-center gap-4">
              <Link
                href="/privacy"
                className="text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                سياسة الخصوصية
              </Link>
              <Link
                href="/terms"
                className="text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                الشروط والأحكام
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
