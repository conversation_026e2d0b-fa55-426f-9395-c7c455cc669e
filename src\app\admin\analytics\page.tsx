"use client";

import React, { useState } from "react";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  Download,
  Filter,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  LineChart,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatPrice } from "@/lib/utils";

// بيانات وهمية للتقارير
const salesData = {
  today: { sales: 1250, orders: 8, customers: 6 },
  yesterday: { sales: 980, orders: 6, customers: 5 },
  thisWeek: { sales: 8500, orders: 45, customers: 32 },
  lastWeek: { sales: 7200, orders: 38, customers: 28 },
  thisMonth: { sales: 32000, orders: 180, customers: 120 },
  lastMonth: { sales: 28500, orders: 165, customers: 110 },
};

const topProducts = [
  { name: "عدسات أكيوفيو اليومية", sales: 45, revenue: 5400, growth: 12 },
  { name: "عدسات بايوفينيتي الشهرية", sales: 32, revenue: 2720, growth: 8 },
  { name: "نظارات طبية كلاسيكية", sales: 15, revenue: 5250, growth: -3 },
  { name: "عدسات ملونة", sales: 28, revenue: 2660, growth: 15 },
  { name: "عدسات ديليز توتال", sales: 22, revenue: 3080, growth: 5 },
];

const salesByCategory = [
  { category: "العدسات اليومية", sales: 15600, percentage: 35 },
  { category: "العدسات الشهرية", sales: 12800, percentage: 28 },
  { category: "النظارات الطبية", sales: 8900, percentage: 20 },
  { category: "العدسات الملونة", sales: 5200, percentage: 12 },
  { category: "الإكسسوارات", sales: 2300, percentage: 5 },
];

const monthlyData = [
  { month: "يناير", sales: 28500, orders: 165, customers: 110 },
  { month: "فبراير", sales: 32000, orders: 180, customers: 120 },
  { month: "مارس", sales: 35200, orders: 195, customers: 135 },
  { month: "أبريل", sales: 31800, orders: 175, customers: 125 },
  { month: "مايو", sales: 38500, orders: 210, customers: 145 },
  { month: "يونيو", sales: 42000, orders: 230, customers: 160 },
];

export default function AnalyticsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("thisMonth");
  const [selectedChart, setSelectedChart] = useState("sales");

  const currentData = salesData[selectedPeriod as keyof typeof salesData];
  const previousData = selectedPeriod === "today" ? salesData.yesterday :
                      selectedPeriod === "thisWeek" ? salesData.lastWeek :
                      salesData.lastMonth;

  const calculateGrowth = (current: number, previous: number) => {
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const salesGrowth = calculateGrowth(currentData.sales, previousData.sales);
  const ordersGrowth = calculateGrowth(currentData.orders, previousData.orders);
  const customersGrowth = calculateGrowth(currentData.customers, previousData.customers);

  const periods = [
    { value: "today", label: "اليوم" },
    { value: "thisWeek", label: "هذا الأسبوع" },
    { value: "thisMonth", label: "هذا الشهر" },
  ];

  return (
    <div className="space-y-6">
      {/* العنوان والأدوات */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">التقارير والإحصائيات</h1>
          <p className="text-gray-600 mt-1">تحليل شامل لأداء المتجر</p>
        </div>
        <div className="flex gap-2">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
          >
            {periods.map((period) => (
              <option key={period.value} value={period.value}>
                {period.label}
              </option>
            ))}
          </select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            تصدير التقرير
          </Button>
        </div>
      </div>

      {/* المؤشرات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المبيعات</p>
                <p className="text-3xl font-bold">{formatPrice(currentData.sales)}</p>
                <div className="flex items-center mt-2">
                  {parseFloat(salesGrowth) >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${parseFloat(salesGrowth) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {salesGrowth}%
                  </span>
                  <span className="text-sm text-gray-500 mr-1">من الفترة السابقة</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الطلبات</p>
                <p className="text-3xl font-bold">{currentData.orders}</p>
                <div className="flex items-center mt-2">
                  {parseFloat(ordersGrowth) >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${parseFloat(ordersGrowth) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {ordersGrowth}%
                  </span>
                  <span className="text-sm text-gray-500 mr-1">من الفترة السابقة</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <ShoppingCart className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">العملاء</p>
                <p className="text-3xl font-bold">{currentData.customers}</p>
                <div className="flex items-center mt-2">
                  {parseFloat(customersGrowth) >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${parseFloat(customersGrowth) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {customersGrowth}%
                  </span>
                  <span className="text-sm text-gray-500 mr-1">من الفترة السابقة</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* أفضل المنتجات مبيعاً */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              أفضل المنتجات مبيعاً
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-primary">{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium text-sm">{product.name}</p>
                      <p className="text-xs text-gray-500">{product.sales} مبيعة</p>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-sm">{formatPrice(product.revenue)}</p>
                    <div className="flex items-center">
                      {product.growth >= 0 ? (
                        <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                      )}
                      <span className={`text-xs ${product.growth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                        {product.growth}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* المبيعات حسب الفئة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              المبيعات حسب الفئة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {salesByCategory.map((category, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{category.category}</span>
                    <span className="text-sm text-gray-600">{category.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>{formatPrice(category.sales)}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* الرسم البياني الشهري */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LineChart className="h-5 w-5" />
            الأداء الشهري
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* أزرار اختيار نوع البيانات */}
            <div className="flex gap-2">
              <Button
                variant={selectedChart === "sales" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedChart("sales")}
              >
                المبيعات
              </Button>
              <Button
                variant={selectedChart === "orders" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedChart("orders")}
              >
                الطلبات
              </Button>
              <Button
                variant={selectedChart === "customers" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedChart("customers")}
              >
                العملاء
              </Button>
            </div>

            {/* الرسم البياني البسيط */}
            <div className="h-64 flex items-end justify-between gap-2 p-4 bg-gray-50 rounded-lg">
              {monthlyData.map((data, index) => {
                const value = selectedChart === "sales" ? data.sales :
                             selectedChart === "orders" ? data.orders :
                             data.customers;
                const maxValue = Math.max(...monthlyData.map(d => 
                  selectedChart === "sales" ? d.sales :
                  selectedChart === "orders" ? d.orders :
                  d.customers
                ));
                const height = (value / maxValue) * 200;
                
                return (
                  <div key={index} className="flex flex-col items-center gap-2">
                    <div className="text-xs text-gray-600 font-medium">
                      {selectedChart === "sales" ? formatPrice(value) :
                       selectedChart === "orders" ? `${value} طلب` :
                       `${value} عميل`}
                    </div>
                    <div
                      className="w-8 bg-primary rounded-t transition-all duration-300"
                      style={{ height: `${height}px` }}
                    ></div>
                    <div className="text-xs text-gray-500 transform -rotate-45 origin-center">
                      {data.month}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* إحصائيات إضافية */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">
              {formatPrice(currentData.sales / currentData.orders)}
            </div>
            <p className="text-sm text-gray-600">متوسط قيمة الطلب</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">
              {(currentData.orders / currentData.customers).toFixed(1)}
            </div>
            <p className="text-sm text-gray-600">طلبات لكل عميل</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <Package className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">234</div>
            <p className="text-sm text-gray-600">إجمالي المنتجات</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <TrendingUp className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">92%</div>
            <p className="text-sm text-gray-600">معدل رضا العملاء</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
