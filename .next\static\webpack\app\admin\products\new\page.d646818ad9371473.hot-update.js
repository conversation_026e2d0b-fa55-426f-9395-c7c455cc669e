"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/new/page",{

/***/ "(app-pages-browser)/./src/lib/products-store.ts":
/*!***********************************!*\
  !*** ./src/lib/products-store.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductsStore: () => (/* binding */ ProductsStore)\n/* harmony export */ });\n// نظام إدارة حالة المنتجات المركزي\n// قائمة المنتجات - تبدأ فارغة للعمل الجدي\nlet products = [];\n// دوال إدارة المنتجات\nconst ProductsStore = {\n    // الحصول على جميع المنتجات\n    getAll: ()=>{\n        return [\n            ...products\n        ];\n    },\n    // الحصول على منتج بالمعرف\n    getById: (id)=>{\n        return products.find((p)=>p.id === id);\n    },\n    // الحصول على منتج بالـ SKU\n    getBySku: (sku)=>{\n        return products.find((p)=>p.sku === sku);\n    },\n    // إضافة منتج جديد\n    add: (productData)=>{\n        const newProduct = {\n            ...productData,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        products.push(newProduct);\n        return newProduct;\n    },\n    // تحديث منتج\n    update: (id, updates)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        products[index] = {\n            ...products[index],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        return products[index];\n    },\n    // حذف منتج\n    delete: (id)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        products.splice(index, 1);\n        return true;\n    },\n    // حذف منتجات متعددة\n    deleteMultiple: (ids)=>{\n        let deletedCount = 0;\n        ids.forEach((id)=>{\n            if (ProductsStore.delete(id)) {\n                deletedCount++;\n            }\n        });\n        return deletedCount;\n    },\n    // البحث في المنتجات\n    search: (query)=>{\n        const lowerQuery = query.toLowerCase();\n        return products.filter((product)=>product.name.toLowerCase().includes(lowerQuery) || product.nameEn.toLowerCase().includes(lowerQuery) || product.sku.toLowerCase().includes(lowerQuery) || product.brand.toLowerCase().includes(lowerQuery) || product.category.toLowerCase().includes(lowerQuery));\n    },\n    // فلترة المنتجات\n    filter: (filters)=>{\n        return products.filter((product)=>{\n            if (filters.category && filters.category !== \"الكل\" && product.category !== filters.category) {\n                return false;\n            }\n            if (filters.brand && filters.brand !== \"الكل\" && product.brand !== filters.brand) {\n                return false;\n            }\n            if (filters.status && filters.status !== \"الكل\" && product.status !== filters.status) {\n                return false;\n            }\n            if (filters.inStock !== undefined) {\n                const hasStock = product.stock > 0;\n                if (filters.inStock !== hasStock) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    },\n    // الحصول على الإحصائيات\n    getStats: ()=>{\n        const total = products.length;\n        const active = products.filter((p)=>p.status === \"active\").length;\n        const draft = products.filter((p)=>p.status === \"draft\").length;\n        const outOfStock = products.filter((p)=>p.stock === 0).length;\n        const totalStock = products.reduce((sum, p)=>sum + p.stock, 0);\n        const lowStock = products.filter((p)=>p.stock > 0 && p.stock <= 10).length;\n        return {\n            total,\n            active,\n            draft,\n            outOfStock,\n            totalStock,\n            lowStock\n        };\n    },\n    // توليد SKU تلقائي\n    generateSku: function() {\n        let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"PRD\";\n        let counter = 1;\n        let sku;\n        do {\n            sku = \"\".concat(prefix, \"-\").concat(counter.toString().padStart(3, '0'));\n            counter++;\n        }while (ProductsStore.getBySku(sku));\n        return sku;\n    },\n    // توليد slug تلقائي\n    generateSlug: (name, sku)=>{\n        let baseSlug = name.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n        if (sku) {\n            baseSlug += \"-\".concat(sku.toLowerCase());\n        }\n        let slug = baseSlug;\n        let counter = 1;\n        while(products.some((p)=>p.slug === slug)){\n            slug = \"\".concat(baseSlug, \"-\").concat(counter);\n            counter++;\n        }\n        return slug;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvcHJvZHVjdHMtc3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLG1DQUFtQztBQTJDbkMsMENBQTBDO0FBQzFDLElBQUlBLFdBQXNCLEVBQUU7QUFFNUIsc0JBQXNCO0FBQ2YsTUFBTUMsZ0JBQWdCO0lBQzNCLDJCQUEyQjtJQUMzQkMsUUFBUTtRQUNOLE9BQU87ZUFBSUY7U0FBUztJQUN0QjtJQUVBLDBCQUEwQjtJQUMxQkcsU0FBUyxDQUFDQztRQUNSLE9BQU9KLFNBQVNLLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUYsRUFBRSxLQUFLQTtJQUNyQztJQUVBLDJCQUEyQjtJQUMzQkcsVUFBVSxDQUFDQztRQUNULE9BQU9SLFNBQVNLLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUUsR0FBRyxLQUFLQTtJQUN0QztJQUVBLGtCQUFrQjtJQUNsQkMsS0FBSyxDQUFDQztRQUNKLE1BQU1DLGFBQXNCO1lBQzFCLEdBQUdELFdBQVc7WUFDZE4sSUFBSVEsS0FBS0MsR0FBRyxHQUFHQyxRQUFRO1lBQ3ZCQyxXQUFXLElBQUlILE9BQU9JLFdBQVc7WUFDakNDLFdBQVcsSUFBSUwsT0FBT0ksV0FBVztRQUNuQztRQUVBaEIsU0FBU2tCLElBQUksQ0FBQ1A7UUFDZCxPQUFPQTtJQUNUO0lBRUEsYUFBYTtJQUNiUSxRQUFRLENBQUNmLElBQVlnQjtRQUNuQixNQUFNQyxRQUFRckIsU0FBU3NCLFNBQVMsQ0FBQ2hCLENBQUFBLElBQUtBLEVBQUVGLEVBQUUsS0FBS0E7UUFDL0MsSUFBSWlCLFVBQVUsQ0FBQyxHQUFHLE9BQU87UUFFekJyQixRQUFRLENBQUNxQixNQUFNLEdBQUc7WUFDaEIsR0FBR3JCLFFBQVEsQ0FBQ3FCLE1BQU07WUFDbEIsR0FBR0QsT0FBTztZQUNWSCxXQUFXLElBQUlMLE9BQU9JLFdBQVc7UUFDbkM7UUFFQSxPQUFPaEIsUUFBUSxDQUFDcUIsTUFBTTtJQUN4QjtJQUVBLFdBQVc7SUFDWEUsUUFBUSxDQUFDbkI7UUFDUCxNQUFNaUIsUUFBUXJCLFNBQVNzQixTQUFTLENBQUNoQixDQUFBQSxJQUFLQSxFQUFFRixFQUFFLEtBQUtBO1FBQy9DLElBQUlpQixVQUFVLENBQUMsR0FBRyxPQUFPO1FBRXpCckIsU0FBU3dCLE1BQU0sQ0FBQ0gsT0FBTztRQUN2QixPQUFPO0lBQ1Q7SUFFQSxvQkFBb0I7SUFDcEJJLGdCQUFnQixDQUFDQztRQUNmLElBQUlDLGVBQWU7UUFDbkJELElBQUlFLE9BQU8sQ0FBQ3hCLENBQUFBO1lBQ1YsSUFBSUgsY0FBY3NCLE1BQU0sQ0FBQ25CLEtBQUs7Z0JBQzVCdUI7WUFDRjtRQUNGO1FBQ0EsT0FBT0E7SUFDVDtJQUVBLG9CQUFvQjtJQUNwQkUsUUFBUSxDQUFDQztRQUNQLE1BQU1DLGFBQWFELE1BQU1FLFdBQVc7UUFDcEMsT0FBT2hDLFNBQVNpQyxNQUFNLENBQUNDLENBQUFBLFVBQ3JCQSxRQUFRQyxJQUFJLENBQUNILFdBQVcsR0FBR0ksUUFBUSxDQUFDTCxlQUNwQ0csUUFBUUcsTUFBTSxDQUFDTCxXQUFXLEdBQUdJLFFBQVEsQ0FBQ0wsZUFDdENHLFFBQVExQixHQUFHLENBQUN3QixXQUFXLEdBQUdJLFFBQVEsQ0FBQ0wsZUFDbkNHLFFBQVFJLEtBQUssQ0FBQ04sV0FBVyxHQUFHSSxRQUFRLENBQUNMLGVBQ3JDRyxRQUFRSyxRQUFRLENBQUNQLFdBQVcsR0FBR0ksUUFBUSxDQUFDTDtJQUU1QztJQUVBLGlCQUFpQjtJQUNqQkUsUUFBUSxDQUFDTztRQU1QLE9BQU94QyxTQUFTaUMsTUFBTSxDQUFDQyxDQUFBQTtZQUNyQixJQUFJTSxRQUFRRCxRQUFRLElBQUlDLFFBQVFELFFBQVEsS0FBSyxVQUFVTCxRQUFRSyxRQUFRLEtBQUtDLFFBQVFELFFBQVEsRUFBRTtnQkFDNUYsT0FBTztZQUNUO1lBQ0EsSUFBSUMsUUFBUUYsS0FBSyxJQUFJRSxRQUFRRixLQUFLLEtBQUssVUFBVUosUUFBUUksS0FBSyxLQUFLRSxRQUFRRixLQUFLLEVBQUU7Z0JBQ2hGLE9BQU87WUFDVDtZQUNBLElBQUlFLFFBQVFDLE1BQU0sSUFBSUQsUUFBUUMsTUFBTSxLQUFLLFVBQVVQLFFBQVFPLE1BQU0sS0FBS0QsUUFBUUMsTUFBTSxFQUFFO2dCQUNwRixPQUFPO1lBQ1Q7WUFDQSxJQUFJRCxRQUFRRSxPQUFPLEtBQUtDLFdBQVc7Z0JBQ2pDLE1BQU1DLFdBQVdWLFFBQVFXLEtBQUssR0FBRztnQkFDakMsSUFBSUwsUUFBUUUsT0FBTyxLQUFLRSxVQUFVO29CQUNoQyxPQUFPO2dCQUNUO1lBQ0Y7WUFDQSxPQUFPO1FBQ1Q7SUFDRjtJQUVBLHdCQUF3QjtJQUN4QkUsVUFBVTtRQUNSLE1BQU1DLFFBQVEvQyxTQUFTZ0QsTUFBTTtRQUM3QixNQUFNQyxTQUFTakQsU0FBU2lDLE1BQU0sQ0FBQzNCLENBQUFBLElBQUtBLEVBQUVtQyxNQUFNLEtBQUssVUFBVU8sTUFBTTtRQUNqRSxNQUFNRSxRQUFRbEQsU0FBU2lDLE1BQU0sQ0FBQzNCLENBQUFBLElBQUtBLEVBQUVtQyxNQUFNLEtBQUssU0FBU08sTUFBTTtRQUMvRCxNQUFNRyxhQUFhbkQsU0FBU2lDLE1BQU0sQ0FBQzNCLENBQUFBLElBQUtBLEVBQUV1QyxLQUFLLEtBQUssR0FBR0csTUFBTTtRQUM3RCxNQUFNSSxhQUFhcEQsU0FBU3FELE1BQU0sQ0FBQyxDQUFDQyxLQUFLaEQsSUFBTWdELE1BQU1oRCxFQUFFdUMsS0FBSyxFQUFFO1FBQzlELE1BQU1VLFdBQVd2RCxTQUFTaUMsTUFBTSxDQUFDM0IsQ0FBQUEsSUFBS0EsRUFBRXVDLEtBQUssR0FBRyxLQUFLdkMsRUFBRXVDLEtBQUssSUFBSSxJQUFJRyxNQUFNO1FBRTFFLE9BQU87WUFDTEQ7WUFDQUU7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUc7UUFDRjtJQUNGO0lBRUEsbUJBQW1CO0lBQ25CQyxhQUFhO1lBQUNDLDBFQUFpQjtRQUM3QixJQUFJQyxVQUFVO1FBQ2QsSUFBSWxEO1FBRUosR0FBRztZQUNEQSxNQUFNLEdBQWFrRCxPQUFWRCxRQUFPLEtBQXVDLE9BQXBDQyxRQUFRNUMsUUFBUSxHQUFHNkMsUUFBUSxDQUFDLEdBQUc7WUFDbEREO1FBQ0YsUUFBU3pELGNBQWNNLFFBQVEsQ0FBQ0MsTUFBTTtRQUV0QyxPQUFPQTtJQUNUO0lBRUEsb0JBQW9CO0lBQ3BCb0QsY0FBYyxDQUFDekIsTUFBYzNCO1FBQzNCLElBQUlxRCxXQUFXMUIsS0FDWkgsV0FBVyxHQUNYOEIsT0FBTyxDQUFDLDhCQUE4QixJQUN0Q0EsT0FBTyxDQUFDLFFBQVEsS0FDaEJDLElBQUk7UUFFUCxJQUFJdkQsS0FBSztZQUNQcUQsWUFBWSxJQUFzQixPQUFsQnJELElBQUl3QixXQUFXO1FBQ2pDO1FBRUEsSUFBSWdDLE9BQU9IO1FBQ1gsSUFBSUgsVUFBVTtRQUVkLE1BQU8xRCxTQUFTaUUsSUFBSSxDQUFDM0QsQ0FBQUEsSUFBS0EsRUFBRTBELElBQUksS0FBS0EsTUFBTztZQUMxQ0EsT0FBTyxHQUFlTixPQUFaRyxVQUFTLEtBQVcsT0FBUkg7WUFDdEJBO1FBQ0Y7UUFFQSxPQUFPTTtJQUNUO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRzpcXHZpc2lvbmxlbnNcXHNyY1xcbGliXFxwcm9kdWN0cy1zdG9yZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDZhti42KfZhSDYpdiv2KfYsdipINit2KfZhNipINin2YTZhdmG2KrYrNin2Kog2KfZhNmF2LHZg9iy2YpcblxuZXhwb3J0IGludGVyZmFjZSBQcm9kdWN0IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBuYW1lRW46IHN0cmluZztcbiAgc2t1OiBzdHJpbmc7XG4gIGNhdGVnb3J5OiBzdHJpbmc7XG4gIGJyYW5kOiBzdHJpbmc7XG4gIHByaWNlOiBudW1iZXI7XG4gIHByaWNlQ3VycmVuY3k6IHN0cmluZztcbiAgY29tcGFyZVByaWNlPzogbnVtYmVyO1xuICBjb21wYXJlUHJpY2VDdXJyZW5jeT86IHN0cmluZztcbiAgY29zdD86IG51bWJlcjtcbiAgY29zdEN1cnJlbmN5Pzogc3RyaW5nO1xuICBzdG9jazogbnVtYmVyO1xuICBtaW5RdWFudGl0eT86IG51bWJlcjtcbiAgbWF4UXVhbnRpdHk/OiBudW1iZXI7XG4gIHN0YXR1czogXCJhY3RpdmVcIiB8IFwiZHJhZnRcIiB8IFwiaW5hY3RpdmVcIjtcbiAgaW1hZ2U6IHN0cmluZztcbiAgaW1hZ2VzOiBzdHJpbmdbXTtcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgc2hvcnREZXNjcmlwdGlvbj86IHN0cmluZztcbiAgdGFnczogc3RyaW5nW107XG4gIHNwZWNpZmljYXRpb25zOiB7IGtleTogc3RyaW5nOyB2YWx1ZTogc3RyaW5nIH1bXTtcbiAgd2VpZ2h0PzogbnVtYmVyO1xuICBkaW1lbnNpb25zPzoge1xuICAgIGxlbmd0aDogbnVtYmVyO1xuICAgIHdpZHRoOiBudW1iZXI7XG4gICAgaGVpZ2h0OiBudW1iZXI7XG4gIH07XG4gIG1ldGFUaXRsZT86IHN0cmluZztcbiAgbWV0YURlc2NyaXB0aW9uPzogc3RyaW5nO1xuICBzbHVnOiBzdHJpbmc7XG4gIGZlYXR1cmVkOiBib29sZWFuO1xuICBhbGxvd0JhY2tvcmRlcjogYm9vbGVhbjtcbiAgdHJhY2tRdWFudGl0eTogYm9vbGVhbjtcbiAgbG9jYXRpb24/OiBzdHJpbmc7XG4gIGJhcmNvZGU/OiBzdHJpbmc7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICB1cGRhdGVkQXQ6IHN0cmluZztcbn1cblxuLy8g2YLYp9im2YXYqSDYp9mE2YXZhtiq2KzYp9iqIC0g2KrYqNiv2KMg2YHYp9ix2LrYqSDZhNmE2LnZhdmEINin2YTYrNiv2YpcbmxldCBwcm9kdWN0czogUHJvZHVjdFtdID0gW107XG5cbi8vINiv2YjYp9mEINil2K/Yp9ix2Kkg2KfZhNmF2YbYqtis2KfYqlxuZXhwb3J0IGNvbnN0IFByb2R1Y3RzU3RvcmUgPSB7XG4gIC8vINin2YTYrdi12YjZhCDYudmE2Ykg2KzZhdmK2Lkg2KfZhNmF2YbYqtis2KfYqlxuICBnZXRBbGw6ICgpOiBQcm9kdWN0W10gPT4ge1xuICAgIHJldHVybiBbLi4ucHJvZHVjdHNdO1xuICB9LFxuXG4gIC8vINin2YTYrdi12YjZhCDYudmE2Ykg2YXZhtiq2Kwg2KjYp9mE2YXYudix2YFcbiAgZ2V0QnlJZDogKGlkOiBzdHJpbmcpOiBQcm9kdWN0IHwgdW5kZWZpbmVkID0+IHtcbiAgICByZXR1cm4gcHJvZHVjdHMuZmluZChwID0+IHAuaWQgPT09IGlkKTtcbiAgfSxcblxuICAvLyDYp9mE2K3YtdmI2YQg2LnZhNmJINmF2YbYqtisINio2KfZhNmAIFNLVVxuICBnZXRCeVNrdTogKHNrdTogc3RyaW5nKTogUHJvZHVjdCB8IHVuZGVmaW5lZCA9PiB7XG4gICAgcmV0dXJuIHByb2R1Y3RzLmZpbmQocCA9PiBwLnNrdSA9PT0gc2t1KTtcbiAgfSxcblxuICAvLyDYpdi22KfZgdipINmF2YbYqtisINis2K/ZitivXG4gIGFkZDogKHByb2R1Y3REYXRhOiBPbWl0PFByb2R1Y3QsICdpZCcgfCAnY3JlYXRlZEF0JyB8ICd1cGRhdGVkQXQnPik6IFByb2R1Y3QgPT4ge1xuICAgIGNvbnN0IG5ld1Byb2R1Y3Q6IFByb2R1Y3QgPSB7XG4gICAgICAuLi5wcm9kdWN0RGF0YSxcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIH07XG4gICAgXG4gICAgcHJvZHVjdHMucHVzaChuZXdQcm9kdWN0KTtcbiAgICByZXR1cm4gbmV3UHJvZHVjdDtcbiAgfSxcblxuICAvLyDYqtit2K/ZitirINmF2YbYqtisXG4gIHVwZGF0ZTogKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8UHJvZHVjdD4pOiBQcm9kdWN0IHwgbnVsbCA9PiB7XG4gICAgY29uc3QgaW5kZXggPSBwcm9kdWN0cy5maW5kSW5kZXgocCA9PiBwLmlkID09PSBpZCk7XG4gICAgaWYgKGluZGV4ID09PSAtMSkgcmV0dXJuIG51bGw7XG5cbiAgICBwcm9kdWN0c1tpbmRleF0gPSB7XG4gICAgICAuLi5wcm9kdWN0c1tpbmRleF0sXG4gICAgICAuLi51cGRhdGVzLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgfTtcblxuICAgIHJldHVybiBwcm9kdWN0c1tpbmRleF07XG4gIH0sXG5cbiAgLy8g2K3YsNmBINmF2YbYqtisXG4gIGRlbGV0ZTogKGlkOiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgICBjb25zdCBpbmRleCA9IHByb2R1Y3RzLmZpbmRJbmRleChwID0+IHAuaWQgPT09IGlkKTtcbiAgICBpZiAoaW5kZXggPT09IC0xKSByZXR1cm4gZmFsc2U7XG5cbiAgICBwcm9kdWN0cy5zcGxpY2UoaW5kZXgsIDEpO1xuICAgIHJldHVybiB0cnVlO1xuICB9LFxuXG4gIC8vINit2LDZgSDZhdmG2KrYrNin2Kog2YXYqti52K/Yr9ipXG4gIGRlbGV0ZU11bHRpcGxlOiAoaWRzOiBzdHJpbmdbXSk6IG51bWJlciA9PiB7XG4gICAgbGV0IGRlbGV0ZWRDb3VudCA9IDA7XG4gICAgaWRzLmZvckVhY2goaWQgPT4ge1xuICAgICAgaWYgKFByb2R1Y3RzU3RvcmUuZGVsZXRlKGlkKSkge1xuICAgICAgICBkZWxldGVkQ291bnQrKztcbiAgICAgIH1cbiAgICB9KTtcbiAgICByZXR1cm4gZGVsZXRlZENvdW50O1xuICB9LFxuXG4gIC8vINin2YTYqNit2Ksg2YHZiiDYp9mE2YXZhtiq2KzYp9iqXG4gIHNlYXJjaDogKHF1ZXJ5OiBzdHJpbmcpOiBQcm9kdWN0W10gPT4ge1xuICAgIGNvbnN0IGxvd2VyUXVlcnkgPSBxdWVyeS50b0xvd2VyQ2FzZSgpO1xuICAgIHJldHVybiBwcm9kdWN0cy5maWx0ZXIocHJvZHVjdCA9PiBcbiAgICAgIHByb2R1Y3QubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyUXVlcnkpIHx8XG4gICAgICBwcm9kdWN0Lm5hbWVFbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyUXVlcnkpIHx8XG4gICAgICBwcm9kdWN0LnNrdS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGxvd2VyUXVlcnkpIHx8XG4gICAgICBwcm9kdWN0LmJyYW5kLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMobG93ZXJRdWVyeSkgfHxcbiAgICAgIHByb2R1Y3QuY2F0ZWdvcnkudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhsb3dlclF1ZXJ5KVxuICAgICk7XG4gIH0sXG5cbiAgLy8g2YHZhNiq2LHYqSDYp9mE2YXZhtiq2KzYp9iqXG4gIGZpbHRlcjogKGZpbHRlcnM6IHtcbiAgICBjYXRlZ29yeT86IHN0cmluZztcbiAgICBicmFuZD86IHN0cmluZztcbiAgICBzdGF0dXM/OiBzdHJpbmc7XG4gICAgaW5TdG9jaz86IGJvb2xlYW47XG4gIH0pOiBQcm9kdWN0W10gPT4ge1xuICAgIHJldHVybiBwcm9kdWN0cy5maWx0ZXIocHJvZHVjdCA9PiB7XG4gICAgICBpZiAoZmlsdGVycy5jYXRlZ29yeSAmJiBmaWx0ZXJzLmNhdGVnb3J5ICE9PSBcItin2YTZg9mEXCIgJiYgcHJvZHVjdC5jYXRlZ29yeSAhPT0gZmlsdGVycy5jYXRlZ29yeSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgICBpZiAoZmlsdGVycy5icmFuZCAmJiBmaWx0ZXJzLmJyYW5kICE9PSBcItin2YTZg9mEXCIgJiYgcHJvZHVjdC5icmFuZCAhPT0gZmlsdGVycy5icmFuZCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgICBpZiAoZmlsdGVycy5zdGF0dXMgJiYgZmlsdGVycy5zdGF0dXMgIT09IFwi2KfZhNmD2YRcIiAmJiBwcm9kdWN0LnN0YXR1cyAhPT0gZmlsdGVycy5zdGF0dXMpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgICAgaWYgKGZpbHRlcnMuaW5TdG9jayAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGNvbnN0IGhhc1N0b2NrID0gcHJvZHVjdC5zdG9jayA+IDA7XG4gICAgICAgIGlmIChmaWx0ZXJzLmluU3RvY2sgIT09IGhhc1N0b2NrKSB7XG4gICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9KTtcbiAgfSxcblxuICAvLyDYp9mE2K3YtdmI2YQg2LnZhNmJINin2YTYpdit2LXYp9im2YrYp9iqXG4gIGdldFN0YXRzOiAoKSA9PiB7XG4gICAgY29uc3QgdG90YWwgPSBwcm9kdWN0cy5sZW5ndGg7XG4gICAgY29uc3QgYWN0aXZlID0gcHJvZHVjdHMuZmlsdGVyKHAgPT4gcC5zdGF0dXMgPT09IFwiYWN0aXZlXCIpLmxlbmd0aDtcbiAgICBjb25zdCBkcmFmdCA9IHByb2R1Y3RzLmZpbHRlcihwID0+IHAuc3RhdHVzID09PSBcImRyYWZ0XCIpLmxlbmd0aDtcbiAgICBjb25zdCBvdXRPZlN0b2NrID0gcHJvZHVjdHMuZmlsdGVyKHAgPT4gcC5zdG9jayA9PT0gMCkubGVuZ3RoO1xuICAgIGNvbnN0IHRvdGFsU3RvY2sgPSBwcm9kdWN0cy5yZWR1Y2UoKHN1bSwgcCkgPT4gc3VtICsgcC5zdG9jaywgMCk7XG4gICAgY29uc3QgbG93U3RvY2sgPSBwcm9kdWN0cy5maWx0ZXIocCA9PiBwLnN0b2NrID4gMCAmJiBwLnN0b2NrIDw9IDEwKS5sZW5ndGg7XG5cbiAgICByZXR1cm4ge1xuICAgICAgdG90YWwsXG4gICAgICBhY3RpdmUsXG4gICAgICBkcmFmdCxcbiAgICAgIG91dE9mU3RvY2ssXG4gICAgICB0b3RhbFN0b2NrLFxuICAgICAgbG93U3RvY2ssXG4gICAgfTtcbiAgfSxcblxuICAvLyDYqtmI2YTZitivIFNLVSDYqtmE2YLYp9im2YpcbiAgZ2VuZXJhdGVTa3U6IChwcmVmaXg6IHN0cmluZyA9IFwiUFJEXCIpOiBzdHJpbmcgPT4ge1xuICAgIGxldCBjb3VudGVyID0gMTtcbiAgICBsZXQgc2t1OiBzdHJpbmc7XG4gICAgXG4gICAgZG8ge1xuICAgICAgc2t1ID0gYCR7cHJlZml4fS0ke2NvdW50ZXIudG9TdHJpbmcoKS5wYWRTdGFydCgzLCAnMCcpfWA7XG4gICAgICBjb3VudGVyKys7XG4gICAgfSB3aGlsZSAoUHJvZHVjdHNTdG9yZS5nZXRCeVNrdShza3UpKTtcbiAgICBcbiAgICByZXR1cm4gc2t1O1xuICB9LFxuXG4gIC8vINiq2YjZhNmK2K8gc2x1ZyDYqtmE2YLYp9im2YpcbiAgZ2VuZXJhdGVTbHVnOiAobmFtZTogc3RyaW5nLCBza3U/OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICAgIGxldCBiYXNlU2x1ZyA9IG5hbWVcbiAgICAgIC50b0xvd2VyQ2FzZSgpXG4gICAgICAucmVwbGFjZSgvW15hLXowLTlcXHUwNjAwLVxcdTA2RkZcXHMtXS9nLCBcIlwiKVxuICAgICAgLnJlcGxhY2UoL1xccysvZywgXCItXCIpXG4gICAgICAudHJpbSgpO1xuICAgIFxuICAgIGlmIChza3UpIHtcbiAgICAgIGJhc2VTbHVnICs9IGAtJHtza3UudG9Mb3dlckNhc2UoKX1gO1xuICAgIH1cbiAgICBcbiAgICBsZXQgc2x1ZyA9IGJhc2VTbHVnO1xuICAgIGxldCBjb3VudGVyID0gMTtcbiAgICBcbiAgICB3aGlsZSAocHJvZHVjdHMuc29tZShwID0+IHAuc2x1ZyA9PT0gc2x1ZykpIHtcbiAgICAgIHNsdWcgPSBgJHtiYXNlU2x1Z30tJHtjb3VudGVyfWA7XG4gICAgICBjb3VudGVyKys7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBzbHVnO1xuICB9LFxufTtcbiJdLCJuYW1lcyI6WyJwcm9kdWN0cyIsIlByb2R1Y3RzU3RvcmUiLCJnZXRBbGwiLCJnZXRCeUlkIiwiaWQiLCJmaW5kIiwicCIsImdldEJ5U2t1Iiwic2t1IiwiYWRkIiwicHJvZHVjdERhdGEiLCJuZXdQcm9kdWN0IiwiRGF0ZSIsIm5vdyIsInRvU3RyaW5nIiwiY3JlYXRlZEF0IiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJwdXNoIiwidXBkYXRlIiwidXBkYXRlcyIsImluZGV4IiwiZmluZEluZGV4IiwiZGVsZXRlIiwic3BsaWNlIiwiZGVsZXRlTXVsdGlwbGUiLCJpZHMiLCJkZWxldGVkQ291bnQiLCJmb3JFYWNoIiwic2VhcmNoIiwicXVlcnkiLCJsb3dlclF1ZXJ5IiwidG9Mb3dlckNhc2UiLCJmaWx0ZXIiLCJwcm9kdWN0IiwibmFtZSIsImluY2x1ZGVzIiwibmFtZUVuIiwiYnJhbmQiLCJjYXRlZ29yeSIsImZpbHRlcnMiLCJzdGF0dXMiLCJpblN0b2NrIiwidW5kZWZpbmVkIiwiaGFzU3RvY2siLCJzdG9jayIsImdldFN0YXRzIiwidG90YWwiLCJsZW5ndGgiLCJhY3RpdmUiLCJkcmFmdCIsIm91dE9mU3RvY2siLCJ0b3RhbFN0b2NrIiwicmVkdWNlIiwic3VtIiwibG93U3RvY2siLCJnZW5lcmF0ZVNrdSIsInByZWZpeCIsImNvdW50ZXIiLCJwYWRTdGFydCIsImdlbmVyYXRlU2x1ZyIsImJhc2VTbHVnIiwicmVwbGFjZSIsInRyaW0iLCJzbHVnIiwic29tZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/products-store.ts\n"));

/***/ })

});