"use client";

import React, { useState } from "react";
import Link from "next/link";
import {
  Search,
  Filter,
  Eye,
  Download,
  Calendar,
  Package,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// بيانات وهمية للطلبات
const orders = [
  {
    id: "ORD-001",
    customer: {
      name: "أحمد محمد",
      email: "<EMAIL>",
      phone: "+966501234567",
    },
    items: [
      { name: "عدسات أكيوفيو اليومية", quantity: 2, price: 120 },
    ],
    total: 240,
    status: "مكتمل",
    paymentStatus: "مدفوع",
    shippingAddress: "الرياض، حي الملك فهد",
    orderDate: "2024-01-15T10:30:00",
    deliveryDate: "2024-01-17T14:00:00",
  },
  {
    id: "ORD-002",
    customer: {
      name: "فاطمة أحمد",
      email: "<EMAIL>",
      phone: "+966501234568",
    },
    items: [
      { name: "عدسات بايوفينيتي الشهرية", quantity: 1, price: 85 },
      { name: "محلول تنظيف", quantity: 1, price: 25 },
    ],
    total: 110,
    status: "قيد المعالجة",
    paymentStatus: "مدفوع",
    shippingAddress: "جدة، حي الصفا",
    orderDate: "2024-01-15T09:15:00",
    deliveryDate: null,
  },
  {
    id: "ORD-003",
    customer: {
      name: "محمد علي",
      email: "<EMAIL>",
      phone: "+966501234569",
    },
    items: [
      { name: "نظارات طبية كلاسيكية", quantity: 1, price: 350 },
    ],
    total: 350,
    status: "تم الشحن",
    paymentStatus: "مدفوع",
    shippingAddress: "الدمام، حي الفيصلية",
    orderDate: "2024-01-14T16:45:00",
    deliveryDate: null,
  },
  {
    id: "ORD-004",
    customer: {
      name: "سارة خالد",
      email: "<EMAIL>",
      phone: "+966501234570",
    },
    items: [
      { name: "عدسات ملونة زرقاء", quantity: 1, price: 95 },
    ],
    total: 95,
    status: "ملغي",
    paymentStatus: "مسترد",
    shippingAddress: "الرياض، حي النرجس",
    orderDate: "2024-01-14T11:20:00",
    deliveryDate: null,
  },
  {
    id: "ORD-005",
    customer: {
      name: "عبدالله سعد",
      email: "<EMAIL>",
      phone: "+966501234571",
    },
    items: [
      { name: "عدسات ديليز توتال ون", quantity: 3, price: 140 },
    ],
    total: 420,
    status: "جديد",
    paymentStatus: "في الانتظار",
    shippingAddress: "مكة، حي العزيزية",
    orderDate: "2024-01-15T14:10:00",
    deliveryDate: null,
  },
];

const statusConfig = {
  "جديد": { color: "bg-blue-100 text-blue-800", icon: Clock },
  "قيد المعالجة": { color: "bg-yellow-100 text-yellow-800", icon: Package },
  "تم الشحن": { color: "bg-purple-100 text-purple-800", icon: Truck },
  "مكتمل": { color: "bg-green-100 text-green-800", icon: CheckCircle },
  "ملغي": { color: "bg-red-100 text-red-800", icon: XCircle },
};

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("الكل");

  const filteredOrders = orders.filter((order) => {
    const matchesSearch = 
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = selectedStatus === "الكل" || order.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });

  const statuses = ["الكل", ...Array.from(new Set(orders.map(o => o.status)))];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إدارة الطلبات</h1>
          <p className="text-gray-600 mt-1">متابعة وإدارة طلبات العملاء</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            تصدير
          </Button>
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            تقرير
          </Button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{orders.length}</div>
            <p className="text-sm text-gray-600">إجمالي الطلبات</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {orders.filter(o => o.status === "جديد").length}
            </div>
            <p className="text-sm text-gray-600">طلبات جديدة</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {orders.filter(o => o.status === "قيد المعالجة").length}
            </div>
            <p className="text-sm text-gray-600">قيد المعالجة</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {orders.filter(o => o.status === "مكتمل").length}
            </div>
            <p className="text-sm text-gray-600">مكتملة</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {orders.reduce((sum, o) => sum + o.total, 0).toLocaleString()} ريال
            </div>
            <p className="text-sm text-gray-600">إجمالي المبيعات</p>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والفلترة */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الطلبات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {statuses.map((status) => (
                  <option key={status} value={status}>
                    {status}
                  </option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                فلترة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول الطلبات */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة الطلبات ({filteredOrders.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right py-3 px-4 font-medium">رقم الطلب</th>
                  <th className="text-right py-3 px-4 font-medium">العميل</th>
                  <th className="text-right py-3 px-4 font-medium">المنتجات</th>
                  <th className="text-right py-3 px-4 font-medium">المبلغ</th>
                  <th className="text-right py-3 px-4 font-medium">الحالة</th>
                  <th className="text-right py-3 px-4 font-medium">تاريخ الطلب</th>
                  <th className="text-right py-3 px-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredOrders.map((order) => {
                  const StatusIcon = statusConfig[order.status as keyof typeof statusConfig]?.icon || Clock;
                  return (
                    <tr key={order.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-sm">{order.id}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <p className="font-medium text-sm">{order.customer.name}</p>
                          <p className="text-xs text-gray-500">{order.customer.email}</p>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {order.items.map((item, index) => (
                            <div key={index} className="text-xs text-gray-600">
                              {item.name} × {item.quantity}
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-sm font-medium">
                        {order.total.toLocaleString()} ريال
                      </td>
                      <td className="py-3 px-4">
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            statusConfig[order.status as keyof typeof statusConfig]?.color
                          }`}
                        >
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {order.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm">
                        {formatDate(order.orderDate)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon" asChild>
                            <Link href={`/admin/orders/${order.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          
          {filteredOrders.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">لا توجد طلبات تطابق البحث</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
