"use client";

import React, { useState } from "react";
import Link from "next/link";
import { 
  User, 
  Package, 
  Heart, 
  MapPin, 
  Settings, 
  LogOut,
  Edit,
  Eye,
  Star
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import { formatPrice } from "@/lib/utils";

// بيانات وهمية للمستخدم
const user = {
  name: "أحمد محمد",
  email: "<EMAIL>",
  phone: "+966 50 123 4567",
  joinDate: "2023-06-15",
  totalOrders: 12,
  totalSpent: 2450,
};

// طلبات وهمية
const orders = [
  {
    id: "ORD-001",
    date: "2024-01-15",
    status: "تم التسليم",
    total: 240,
    items: [
      { name: "عدسات أكيوفيو اليومية", quantity: 2 }
    ]
  },
  {
    id: "ORD-002",
    date: "2024-01-10",
    status: "قيد الشحن",
    total: 350,
    items: [
      { name: "نظارات طبية كلاسيكية", quantity: 1 }
    ]
  },
  {
    id: "ORD-003",
    date: "2024-01-05",
    status: "تم التسليم",
    total: 95,
    items: [
      { name: "عدسات ملونة", quantity: 1 }
    ]
  },
];

// المفضلة وهمية
const wishlistItems = [
  {
    id: "1",
    name: "عدسات بايوفينيتي الشهرية",
    price: 85,
    image: "https://picsum.photos/100/100?random=2",
    inStock: true,
  },
  {
    id: "2",
    name: "نظارات شمسية رياضية",
    price: 280,
    image: "https://picsum.photos/100/100?random=8",
    inStock: true,
  },
];

export default function AccountPage() {
  const [activeTab, setActiveTab] = useState("overview");

  const menuItems = [
    { id: "overview", label: "نظرة عامة", icon: User },
    { id: "orders", label: "طلباتي", icon: Package },
    { id: "wishlist", label: "المفضلة", icon: Heart },
    { id: "addresses", label: "العناوين", icon: MapPin },
    { id: "settings", label: "الإعدادات", icon: Settings },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "تم التسليم":
        return "bg-green-100 text-green-800";
      case "قيد الشحن":
        return "bg-blue-100 text-blue-800";
      case "قيد المعالجة":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* القائمة الجانبية */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <User className="h-10 w-10 text-primary" />
                  </div>
                  <h2 className="font-semibold text-lg">{user.name}</h2>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
                
                <nav className="space-y-2">
                  {menuItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right transition-colors ${
                        activeTab === item.id
                          ? "bg-primary text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      <item.icon className="h-5 w-5" />
                      <span>{item.label}</span>
                    </button>
                  ))}
                  
                  <button className="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-right text-red-600 hover:bg-red-50 transition-colors">
                    <LogOut className="h-5 w-5" />
                    <span>تسجيل الخروج</span>
                  </button>
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="lg:col-span-3">
            {/* نظرة عامة */}
            {activeTab === "overview" && (
              <div className="space-y-6">
                <div>
                  <h1 className="text-3xl font-bold mb-2">مرحباً، {user.name}</h1>
                  <p className="text-gray-600">إدارة حسابك ومتابعة طلباتك</p>
                </div>

                {/* إحصائيات سريعة */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardContent className="p-6 text-center">
                      <Package className="h-8 w-8 text-primary mx-auto mb-2" />
                      <div className="text-2xl font-bold">{user.totalOrders}</div>
                      <p className="text-sm text-gray-600">إجمالي الطلبات</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="text-2xl font-bold text-primary">
                        {formatPrice(user.totalSpent)}
                      </div>
                      <p className="text-sm text-gray-600">إجمالي المشتريات</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-6 text-center">
                      <Heart className="h-8 w-8 text-primary mx-auto mb-2" />
                      <div className="text-2xl font-bold">{wishlistItems.length}</div>
                      <p className="text-sm text-gray-600">المنتجات المفضلة</p>
                    </CardContent>
                  </Card>
                </div>

                {/* الطلبات الأخيرة */}
                <Card>
                  <CardHeader>
                    <CardTitle>الطلبات الأخيرة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {orders.slice(0, 3).map((order) => (
                        <div key={order.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium">{order.id}</p>
                            <p className="text-sm text-gray-600">{order.date}</p>
                            <p className="text-xs text-gray-500">
                              {order.items.map(item => `${item.name} (${item.quantity})`).join(", ")}
                            </p>
                          </div>
                          <div className="text-left">
                            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                              {order.status}
                            </span>
                            <p className="font-medium mt-1">{formatPrice(order.total)}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                    <Button variant="outline" className="w-full mt-4" onClick={() => setActiveTab("orders")}>
                      عرض جميع الطلبات
                    </Button>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* الطلبات */}
            {activeTab === "orders" && (
              <div className="space-y-6">
                <h1 className="text-3xl font-bold">طلباتي</h1>
                
                <div className="space-y-4">
                  {orders.map((order) => (
                    <Card key={order.id}>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h3 className="font-semibold">{order.id}</h3>
                            <p className="text-sm text-gray-600">تاريخ الطلب: {order.date}</p>
                          </div>
                          <div className="text-left">
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                              {order.status}
                            </span>
                            <p className="font-semibold mt-1">{formatPrice(order.total)}</p>
                          </div>
                        </div>
                        
                        <div className="space-y-2 mb-4">
                          {order.items.map((item, index) => (
                            <p key={index} className="text-sm text-gray-600">
                              {item.name} × {item.quantity}
                            </p>
                          ))}
                        </div>
                        
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            عرض التفاصيل
                          </Button>
                          {order.status === "تم التسليم" && (
                            <Button variant="outline" size="sm">
                              <Star className="h-4 w-4 mr-2" />
                              تقييم المنتج
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* المفضلة */}
            {activeTab === "wishlist" && (
              <div className="space-y-6">
                <h1 className="text-3xl font-bold">المنتجات المفضلة</h1>
                
                {wishlistItems.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {wishlistItems.map((item) => (
                      <Card key={item.id}>
                        <CardContent className="p-4">
                          <div className="flex gap-4">
                            <img
                              src={item.image}
                              alt={item.name}
                              className="w-20 h-20 object-cover rounded-lg"
                            />
                            <div className="flex-1">
                              <h3 className="font-medium mb-2">{item.name}</h3>
                              <p className="text-primary font-semibold mb-3">
                                {formatPrice(item.price)}
                              </p>
                              <div className="flex gap-2">
                                <Button size="sm" className="flex-1">
                                  أضف للسلة
                                </Button>
                                <Button variant="outline" size="sm">
                                  إزالة
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-12 text-center">
                      <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">لا توجد منتجات مفضلة</h3>
                      <p className="text-gray-600 mb-4">ابدأ بإضافة منتجات إلى قائمة المفضلة</p>
                      <Button asChild>
                        <Link href="/products">تصفح المنتجات</Link>
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* العناوين */}
            {activeTab === "addresses" && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h1 className="text-3xl font-bold">عناوين الشحن</h1>
                  <Button>
                    <MapPin className="h-4 w-4 mr-2" />
                    إضافة عنوان جديد
                  </Button>
                </div>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold">العنوان الرئيسي</h3>
                        <span className="text-xs bg-primary text-white px-2 py-1 rounded">افتراضي</span>
                      </div>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="text-gray-600">
                      <p>{user.name}</p>
                      <p>شارع الملك فهد، حي النرجس</p>
                      <p>الرياض، 12345</p>
                      <p>المملكة العربية السعودية</p>
                      <p>{user.phone}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* الإعدادات */}
            {activeTab === "settings" && (
              <div className="space-y-6">
                <h1 className="text-3xl font-bold">إعدادات الحساب</h1>
                
                <Card>
                  <CardHeader>
                    <CardTitle>المعلومات الشخصية</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">الاسم</label>
                        <input
                          type="text"
                          defaultValue={user.name}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                        <input
                          type="email"
                          defaultValue={user.email}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">رقم الهاتف</label>
                      <input
                        type="tel"
                        defaultValue={user.phone}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>
                    <Button>حفظ التغييرات</Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>تغيير كلمة المرور</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">كلمة المرور الحالية</label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">كلمة المرور الجديدة</label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">تأكيد كلمة المرور الجديدة</label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    </div>
                    <Button>تحديث كلمة المرور</Button>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
