"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/categories/[slug]/page",{

/***/ "(app-pages-browser)/./src/app/categories/[slug]/page.tsx":
/*!********************************************!*\
  !*** ./src/app/categories/[slug]/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_layout_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/footer */ \"(app-pages-browser)/./src/components/layout/footer.tsx\");\n/* harmony import */ var _components_product_product_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/product/product-card */ \"(app-pages-browser)/./src/components/product/product-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// بيانات الفئات (يمكن نقلها لاحقاً إلى قاعدة البيانات)\nconst categoryData = {\n    \"daily-lenses\": {\n        name: \"العدسات اليومية\",\n        description: \"عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي. تتميز بسهولة الاستخدام والنظافة المضمونة.\",\n        image: \"https://picsum.photos/1200/400?random=20\",\n        benefits: [\n            \"راحة طوال اليوم\",\n            \"سهولة الاستخدام\",\n            \"نظافة مضمونة\",\n            \"لا تحتاج تنظيف\",\n            \"مناسبة للمبتدئين\",\n            \"تقليل خطر العدوى\"\n        ]\n    },\n    \"monthly-lenses\": {\n        name: \"العدسات الشهرية\",\n        description: \"عدسات لاصقة شهرية اقتصادية وعملية توفر راحة طويلة المدى مع جودة عالية.\",\n        image: \"https://picsum.photos/1200/400?random=21\",\n        benefits: [\n            \"اقتصادية\",\n            \"جودة عالية\",\n            \"مقاومة للترسبات\",\n            \"راحة طويلة المدى\",\n            \"سهولة العناية\",\n            \"متانة عالية\"\n        ]\n    },\n    \"colored-lenses\": {\n        name: \"العدسات الملونة\",\n        description: \"عدسات ملونة لإطلالة مميزة وجذابة مع ألوان طبيعية وتغطية ممتازة.\",\n        image: \"https://picsum.photos/1200/400?random=22\",\n        benefits: [\n            \"ألوان طبيعية\",\n            \"تغطية ممتازة\",\n            \"آمنة ومريحة\",\n            \"تصاميم متنوعة\",\n            \"مناسبة للمناسبات\",\n            \"جودة الألوان\"\n        ]\n    },\n    \"glasses\": {\n        name: \"النظارات الطبية\",\n        description: \"نظارات طبية عالية الجودة من أفضل العلامات التجارية العالمية.\",\n        image: \"https://picsum.photos/1200/400?random=23\",\n        benefits: [\n            \"جودة عالية\",\n            \"تصاميم عصرية\",\n            \"راحة في الارتداء\",\n            \"مقاومة للخدش\",\n            \"ضمان شامل\",\n            \"خدمة ما بعد البيع\"\n        ]\n    }\n};\nconst priceRanges = [\n    {\n        label: \"الكل\",\n        min: 0,\n        max: Infinity\n    },\n    {\n        label: \"أقل من 100,000 د.ع\",\n        min: 0,\n        max: 100000\n    },\n    {\n        label: \"100,000 - 200,000 د.ع\",\n        min: 100000,\n        max: 200000\n    },\n    {\n        label: \"أكثر من 200,000 د.ع\",\n        min: 200000,\n        max: Infinity\n    }\n];\nfunction CategoryPage(param) {\n    let { params } = param;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedBrand, setSelectedBrand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الكل\");\n    const [selectedPriceRange, setSelectedPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(priceRanges[0]);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الأحدث\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const category = categoryData[params.slug];\n    if (!category) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 py-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold mb-4\",\n                            children: \"الفئة غير موجودة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8\",\n                            children: \"عذراً، الفئة التي تبحث عنها غير متوفرة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/categories\",\n                                children: \"العودة للفئات\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    const filteredProducts = categoryProducts.filter((product)=>{\n        const matchesSearch = product.nameAr.toLowerCase().includes(searchTerm.toLowerCase()) || product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.brand.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesBrand = selectedBrand === \"الكل\" || product.brand === selectedBrand;\n        const matchesPrice = product.price >= selectedPriceRange.min && product.price <= selectedPriceRange.max;\n        return matchesSearch && matchesBrand && matchesPrice;\n    });\n    const sortedProducts = [\n        ...filteredProducts\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case \"السعر: من الأقل للأعلى\":\n                return a.price - b.price;\n            case \"السعر: من الأعلى للأقل\":\n                return b.price - a.price;\n            case \"الأعلى تقييماً\":\n                return (b.rating || 0) - (a.rating || 0);\n            case \"الأكثر مبيعاً\":\n                return (b.reviewCount || 0) - (a.reviewCount || 0);\n            default:\n                return 0;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"hover:text-primary\",\n                                    children: \"الرئيسية\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/categories\",\n                                    className: \"hover:text-primary\",\n                                    children: \"الفئات\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-900\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative h-80 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: category.image,\n                                alt: category.name,\n                                fill: true,\n                                className: \"object-cover\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black/50\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative container mx-auto px-4 h-full flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white max-w-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl opacity-90\",\n                                            children: category.description\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-12 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-center mb-8\",\n                                    children: [\n                                        \"مميزات \",\n                                        category.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: category.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 bg-white p-4 rounded-lg shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-primary rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: benefit\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"container mx-auto px-4 py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    placeholder: \"ابحث في المنتجات...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"فلاتر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setViewMode(viewMode === \"grid\" ? \"list\" : \"grid\"),\n                                                        children: viewMode === \"grid\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 42\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 73\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: selectedBrand,\n                                                                onChange: (e)=>setSelectedBrand(e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: brand,\n                                                                        children: brand\n                                                                    }, brand, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"نطاق السعر\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: selectedPriceRange.label,\n                                                                onChange: (e)=>{\n                                                                    const range = priceRanges.find((r)=>r.label === e.target.value);\n                                                                    if (range) setSelectedPriceRange(range);\n                                                                },\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: priceRanges.map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: range.label,\n                                                                        children: range.label\n                                                                    }, range.label, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"ترتيب حسب\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: sortBy,\n                                                                onChange: (e)=>setSortBy(e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأحدث\",\n                                                                        children: \"الأحدث\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"السعر: من الأقل للأعلى\",\n                                                                        children: \"السعر: من الأقل للأعلى\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"السعر: من الأعلى للأقل\",\n                                                                        children: \"السعر: من الأعلى للأقل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأعلى تقييماً\",\n                                                                        children: \"الأعلى تقييماً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأكثر مبيعاً\",\n                                                                        children: \"الأكثر مبيعاً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"عرض \",\n                                                sortedProducts.length,\n                                                \" من \",\n                                                categoryProducts.length,\n                                                \" منتج\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            sortedProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-6 \".concat(viewMode === \"grid\" ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\" : \"grid-cols-1\"),\n                                children: sortedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_product_card__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        product: product\n                                    }, product.id, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-lg\",\n                                        children: \"لا توجد منتجات تطابق البحث\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"mt-4\",\n                                        onClick: ()=>{\n                                            setSearchTerm(\"\");\n                                            setSelectedBrand(\"الكل\");\n                                            setSelectedPriceRange(priceRanges[0]);\n                                        },\n                                        children: \"إعادة تعيين الفلاتر\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoryPage, \"Mj6KKTNG1PfwUpjbb8iP4f1P8Js=\");\n_c = CategoryPage;\nvar _c;\n$RefreshReg$(_c, \"CategoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/categories/[slug]/page.tsx\n"));

/***/ })

});