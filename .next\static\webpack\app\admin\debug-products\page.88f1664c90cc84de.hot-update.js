"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/debug-products/page",{

/***/ "(app-pages-browser)/./src/lib/init-sample-products.ts":
/*!*****************************************!*\
  !*** ./src/lib/init-sample-products.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initSampleProducts: () => (/* binding */ initSampleProducts)\n/* harmony export */ });\n/* harmony import */ var _products_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./products-store */ \"(app-pages-browser)/./src/lib/products-store.ts\");\n// إضافة منتجات تجريبية للاختبار\n\nconst initSampleProducts = ()=>{\n    // التحقق من وجود منتجات تجريبية محددة\n    const existingProducts = _products_store__WEBPACK_IMPORTED_MODULE_0__.ProductsStore.getAll();\n    const hasSampleProducts = existingProducts.some((p)=>p.sku === \"DCL-001\" || p.sku === \"CMG-001\" || p.sku === \"MCL-001\");\n    if (hasSampleProducts) {\n        return; // لا نضيف منتجات إذا كانت المنتجات التجريبية موجودة بالفعل\n    }\n    // إضافة منتجات تجريبية\n    const sampleProducts = [\n        {\n            name: \"عدسات لاصقة يومية\",\n            nameEn: \"Daily Contact Lenses\",\n            sku: \"DCL-001\",\n            category: \"العدسات اليومية\",\n            brand: \"Johnson & Johnson\",\n            price: 25000,\n            priceCurrency: \"IQD\",\n            stock: 50,\n            status: \"active\",\n            image: \"https://picsum.photos/400/400?random=1\",\n            images: [\n                \"https://picsum.photos/400/400?random=1\"\n            ],\n            description: \"عدسات لاصقة يومية عالية الجودة توفر راحة طوال اليوم\",\n            shortDescription: \"عدسات يومية مريحة\",\n            tags: [\n                \"عدسات\",\n                \"يومية\",\n                \"راحة\"\n            ],\n            specifications: [\n                {\n                    key: \"النوع\",\n                    value: \"يومية\"\n                },\n                {\n                    key: \"المادة\",\n                    value: \"هيدروجيل\"\n                },\n                {\n                    key: \"محتوى الماء\",\n                    value: \"58%\"\n                }\n            ],\n            slug: \"daily-contact-lenses-dcl-001\",\n            featured: true,\n            allowBackorder: false,\n            trackQuantity: true,\n            location: \"المخزن الرئيسي\"\n        },\n        {\n            name: \"نظارات طبية كلاسيكية\",\n            nameEn: \"Classic Medical Glasses\",\n            sku: \"CMG-001\",\n            category: \"النظارات الطبية\",\n            brand: \"Ray-Ban\",\n            price: 150000,\n            priceCurrency: \"IQD\",\n            stock: 25,\n            status: \"active\",\n            image: \"https://picsum.photos/400/400?random=2\",\n            images: [\n                \"https://picsum.photos/400/400?random=2\"\n            ],\n            description: \"نظارات طبية أنيقة بتصميم كلاسيكي مناسب لجميع الأعمار\",\n            shortDescription: \"نظارات طبية كلاسيكية\",\n            tags: [\n                \"نظارات\",\n                \"طبية\",\n                \"كلاسيكية\"\n            ],\n            specifications: [\n                {\n                    key: \"المادة\",\n                    value: \"معدن\"\n                },\n                {\n                    key: \"اللون\",\n                    value: \"أسود\"\n                },\n                {\n                    key: \"الحجم\",\n                    value: \"متوسط\"\n                }\n            ],\n            slug: \"classic-medical-glasses-cmg-001\",\n            featured: false,\n            allowBackorder: true,\n            trackQuantity: true,\n            location: \"المخزن الرئيسي\"\n        },\n        {\n            name: \"عدسات ملونة شهرية\",\n            nameEn: \"Monthly Colored Lenses\",\n            sku: \"MCL-001\",\n            category: \"العدسات الملونة\",\n            brand: \"Alcon\",\n            price: 45000,\n            priceCurrency: \"IQD\",\n            stock: 30,\n            status: \"active\",\n            image: \"https://picsum.photos/400/400?random=3\",\n            images: [\n                \"https://picsum.photos/400/400?random=3\"\n            ],\n            description: \"عدسات ملونة شهرية تمنحك إطلالة جذابة ومميزة\",\n            shortDescription: \"عدسات ملونة شهرية\",\n            tags: [\n                \"عدسات\",\n                \"ملونة\",\n                \"شهرية\"\n            ],\n            specifications: [\n                {\n                    key: \"النوع\",\n                    value: \"شهرية\"\n                },\n                {\n                    key: \"اللون\",\n                    value: \"أزرق\"\n                },\n                {\n                    key: \"القطر\",\n                    value: \"14.2 مم\"\n                }\n            ],\n            slug: \"monthly-colored-lenses-mcl-001\",\n            featured: true,\n            allowBackorder: false,\n            trackQuantity: true,\n            location: \"المخزن الرئيسي\"\n        }\n    ];\n    // إضافة المنتجات\n    sampleProducts.forEach((product)=>{\n        _products_store__WEBPACK_IMPORTED_MODULE_0__.ProductsStore.add(product);\n    });\n    console.log(\"تم إضافة المنتجات التجريبية بنجاح\");\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/init-sample-products.ts\n"));

/***/ })

});