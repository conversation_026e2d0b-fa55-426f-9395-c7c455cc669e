"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Save,
  Eye,
  Trash2,
  AlertCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// استيراد نفس المكونات من صفحة الإضافة
// في التطبيق الحقيقي، يمكن إنشاء مكون مشترك

export default function EditProductPage({ params }: { params: { id: string } }) {
  const [isLoading, setIsLoading] = useState(true);
  const [product, setProduct] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // محاكاة تحميل بيانات المنتج
    const loadProduct = async () => {
      try {
        // هنا سيتم جلب بيانات المنتج من API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // بيانات وهمية للمنتج
        const mockProduct = {
          id: params.id,
          name: "عدسات أكيوفيو اليومية",
          nameEn: "Acuvue Oasys Daily",
          description: "عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي",
          shortDescription: "عدسات يومية مريحة",
          sku: "ACU-001",
          barcode: "1234567890123",
          category: "العدسات اليومية",
          brand: "Johnson & Johnson",
          tags: ["يومية", "مريحة", "آمنة"],
          price: "120",
          comparePrice: "150",
          cost: "80",
          trackQuantity: true,
          quantity: "45",
          minQuantity: "10",
          maxQuantity: "100",
          location: "A1-B2",
          weight: "50",
          dimensions: {
            length: "10",
            width: "8",
            height: "2",
          },
          metaTitle: "عدسات أكيوفيو اليومية - VisionLens",
          metaDescription: "عدسات لاصقة يومية مريحة وآمنة",
          slug: "acuvue-oasys-daily",
          status: "active",
          featured: true,
          allowBackorder: false,
          specifications: [
            { key: "نوع العدسة", value: "يومية" },
            { key: "المادة", value: "سيليكون هيدروجيل" },
            { key: "نفاذية الأكسجين", value: "عالية" },
          ],
          images: [
            "https://picsum.photos/400/400?random=1",
            "https://picsum.photos/400/400?random=11",
          ],
        };
        
        setProduct(mockProduct);
      } catch (error) {
        console.error("Error loading product:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProduct();
  }, [params.id]);

  const handleUpdate = async () => {
    setIsSubmitting(true);
    
    try {
      // هنا سيتم إرسال البيانات المحدثة إلى API
      console.log("Updating product:", product);
      
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert("تم تحديث المنتج بنجاح!");
      
    } catch (error) {
      console.error("Error updating product:", error);
      alert("حدث خطأ أثناء تحديث المنتج");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.")) {
      return;
    }

    try {
      // هنا سيتم حذف المنتج من API
      console.log("Deleting product:", params.id);
      
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert("تم حذف المنتج بنجاح!");
      // إعادة توجيه إلى صفحة المنتجات
      // router.push("/admin/products");
      
    } catch (error) {
      console.error("Error deleting product:", error);
      alert("حدث خطأ أثناء حذف المنتج");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تحميل المنتج...</h1>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* هياكل تحميل */}
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المنتج غير موجود</h1>
          </div>
        </div>
        
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">المنتج المطلوب غير موجود أو تم حذفه</span>
            </div>
            <div className="mt-4">
              <Button asChild>
                <Link href="/admin/products">العودة إلى المنتجات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تعديل المنتج</h1>
            <p className="text-gray-600 mt-1">{product.name}</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/products/${product.slug}`} target="_blank">
              <Eye className="h-4 w-4 mr-2" />
              معاينة
            </Link>
          </Button>
          <Button 
            variant="outline" 
            onClick={handleDelete}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            حذف
          </Button>
          <Button onClick={handleUpdate} disabled={isSubmitting}>
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? "جاري الحفظ..." : "حفظ التغييرات"}
          </Button>
        </div>
      </div>

      {/* معلومات المنتج الحالية */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>معلومات المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">اسم المنتج (عربي)</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.name}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">اسم المنتج (إنجليزي)</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.nameEn}</div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">الوصف</label>
                <div className="p-3 bg-gray-50 rounded-md">{product.description}</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">رمز المنتج</label>
                  <div className="p-3 bg-gray-50 rounded-md font-mono">{product.sku}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الباركود</label>
                  <div className="p-3 bg-gray-50 rounded-md font-mono">{product.barcode}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>التصنيف والأسعار</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">الفئة</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.category}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">العلامة التجارية</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.brand}</div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">سعر البيع</label>
                  <div className="p-3 bg-gray-50 rounded-md font-semibold text-green-600">
                    {product.price} ريال
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">السعر المقارن</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {product.comparePrice ? `${product.comparePrice} ريال` : "غير محدد"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">سعر التكلفة</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {product.cost ? `${product.cost} ريال` : "غير محدد"}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>المخزون</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">الكمية الحالية</label>
                  <div className="p-3 bg-gray-50 rounded-md font-semibold">
                    {product.quantity}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الحد الأدنى</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.minQuantity}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الحد الأقصى</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.maxQuantity}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الموقع</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.location}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>حالة المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">الحالة</label>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  product.status === "active" 
                    ? "bg-green-100 text-green-800" 
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {product.status === "active" ? "نشط" : "غير نشط"}
                </span>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">منتج مميز</label>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  product.featured 
                    ? "bg-blue-100 text-blue-800" 
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {product.featured ? "نعم" : "لا"}
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>المواصفات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {product.specifications.map((spec, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-sm font-medium">{spec.key}:</span>
                    <span className="text-sm text-gray-600">{spec.value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>العلامات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {product.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* ملاحظة */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-blue-800">
            <AlertCircle className="h-5 w-5" />
            <span className="font-medium">ملاحظة:</span>
          </div>
          <p className="mt-2 text-sm text-blue-700">
            هذه صفحة عرض المنتج. لتعديل المنتج، يمكنك إنشاء نموذج تعديل مشابه لنموذج الإضافة 
            مع تعبئة البيانات الحالية مسبقاً.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
