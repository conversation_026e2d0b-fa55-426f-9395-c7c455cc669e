"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { ArrowLeft, Save, Trash2, Eye, X, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface EditProductPageProps {
  params: Promise<{ id: string }>;
}

export default function EditProductPage({ params }: EditProductPageProps) {
  const [productId, setProductId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<any>(null);
  const [originalData, setOriginalData] = useState<any>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const initializeProduct = async () => {
      try {
        const resolvedParams = await params;
        const id = resolvedParams.id;
        setProductId(id);

        // محاكاة تحميل بيانات المنتج
        await new Promise(resolve => setTimeout(resolve, 1000));

        // بيانات وهمية للمنتج
        const mockProduct = {
          id: id,
          name: "عدسات أكيوفيو اليومية",
          nameEn: "Acuvue Oasys Daily",
          description: "عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي",
          sku: "ACU-001",
          barcode: "1234567890123",
          category: "العدسات اليومية",
          brand: "Johnson & Johnson",
          tags: ["يومية", "مريحة", "آمنة"],
          price: "120",
          comparePrice: "150",
          cost: "80",
          quantity: "45",
          minQuantity: "10",
          maxQuantity: "100",
          location: "A1-B2",
          slug: "acuvue-oasys-daily",
          status: "active",
          featured: true,
          specifications: [
            { key: "نوع العدسة", value: "يومية" },
            { key: "المادة", value: "سيليكون هيدروجيل" },
            { key: "نفاذية الأكسجين", value: "عالية" },
          ],
          images: [
            "https://picsum.photos/400/400?random=1",
            "https://picsum.photos/400/400?random=11",
          ],
        };
        
        setFormData(mockProduct);
        setOriginalData(mockProduct);
        setIsLoading(false);
      } catch (error) {
        console.error("Error initializing product:", error);
        setIsLoading(false);
      }
    };

    initializeProduct();
  }, [params]);

  const handleUpdate = async () => {
    setIsSubmitting(true);
    try {
      console.log("Updating product:", formData);
      await new Promise(resolve => setTimeout(resolve, 2000));
      setOriginalData(formData);
      setHasChanges(false);
      alert("تم تحديث المنتج بنجاح!");
    } catch (error) {
      console.error("Error updating product:", error);
      alert("حدث خطأ أثناء تحديث المنتج");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.")) {
      return;
    }

    try {
      console.log("Deleting product:", productId);
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert("تم حذف المنتج بنجاح!");
    } catch (error) {
      console.error("Error deleting product:", error);
      alert("حدث خطأ أثناء حذف المنتج");
    }
  };

  const resetForm = () => {
    setFormData(originalData);
    setHasChanges(false);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تحميل المنتج...</h1>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!formData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المنتج غير موجود</h1>
          </div>
        </div>
        
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">المنتج المطلوب غير موجود أو تم حذفه</span>
            </div>
            <div className="mt-4">
              <Button asChild>
                <Link href="/admin/products">العودة إلى المنتجات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تعديل المنتج</h1>
            <p className="text-gray-600 mt-1">{formData.name}</p>
            {hasChanges && (
              <p className="text-orange-600 text-sm mt-1">⚠️ يوجد تغييرات غير محفوظة</p>
            )}
          </div>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/products/${formData.slug}`} target="_blank">
              <Eye className="h-4 w-4 mr-2" />
              معاينة
            </Link>
          </Button>
          <Button variant="outline" onClick={resetForm} disabled={!hasChanges || isSubmitting}>
            <X className="h-4 w-4 mr-2" />
            إلغاء التغييرات
          </Button>
          <Button
            variant="outline"
            onClick={handleDelete}
            className="text-red-600 hover:text-red-700"
            disabled={isSubmitting}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            حذف
          </Button>
          <Button onClick={handleUpdate} disabled={!hasChanges || isSubmitting}>
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? "جاري الحفظ..." : "حفظ التغييرات"}
          </Button>
        </div>
      </div>

      {/* معلومات المنتج */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>معلومات المنتج</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">اسم المنتج</label>
              <div className="p-3 bg-gray-50 rounded-md">{formData.name}</div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">الوصف</label>
              <div className="p-3 bg-gray-50 rounded-md">{formData.description}</div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">رمز المنتج</label>
                <div className="p-3 bg-gray-50 rounded-md font-mono">{formData.sku}</div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">الفئة</label>
                <div className="p-3 bg-gray-50 rounded-md">{formData.category}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>الأسعار والمخزون</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">السعر</label>
                <div className="p-3 bg-gray-50 rounded-md font-semibold text-green-600">
                  {formData.price} د.ع
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">الكمية</label>
                <div className="p-3 bg-gray-50 rounded-md font-semibold">
                  {formData.quantity}
                </div>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">العلامة التجارية</label>
              <div className="p-3 bg-gray-50 rounded-md">{formData.brand}</div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">الحالة</label>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                formData.status === "active"
                  ? "bg-green-100 text-green-800"
                  : "bg-gray-100 text-gray-800"
              }`}>
                {formData.status === "active" ? "نشط" : "غير نشط"}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ملاحظة */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <p className="text-blue-800 text-sm">
            <strong>ملاحظة:</strong> هذه صفحة عرض فقط. لتعديل المنتج، يمكنك إضافة نماذج التعديل هنا.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
