"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  ArrowLeft,
  Save,
  Eye,
  Trash2,
  AlertCircle,
  Upload,
  X,
  Plus,
  Package,
  DollarSign,
  Tag,
  FileText,
  Copy,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function EditProductPage({ params }: { params: Promise<{ id: string }> }) {
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<any>(null);
  const [originalData, setOriginalData] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false);
  const [currentTag, setCurrentTag] = useState("");
  const [uploadingImages, setUploadingImages] = useState(false);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [productId, setProductId] = useState<string>("");

  useEffect(() => {
    // تحليل params والحصول على ID
    const initializeProduct = async () => {
      try {
        const resolvedParams = await params;
        const id = resolvedParams.id;
        setProductId(id);

        // محاكاة تحميل بيانات المنتج
        await loadProduct(id);
      } catch (error) {
        console.error("Error initializing product:", error);
        setIsLoading(false);
      }
    };

    initializeProduct();
  }, [params]);

  const loadProduct = async (id: string) => {
    try {
      // هنا سيتم جلب بيانات المنتج من API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // بيانات وهمية للمنتج
      const mockProduct = {
        id: id,
          name: "عدسات أكيوفيو اليومية",
          nameEn: "Acuvue Oasys Daily",
          description: "عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي",
          shortDescription: "عدسات يومية مريحة",
          sku: "ACU-001",
          barcode: "1234567890123",
          category: "العدسات اليومية",
          brand: "Johnson & Johnson",
          tags: ["يومية", "مريحة", "آمنة"],
          price: "120",
          comparePrice: "150",
          cost: "80",
          trackQuantity: true,
          quantity: "45",
          minQuantity: "10",
          maxQuantity: "100",
          location: "A1-B2",
          weight: "50",
          dimensions: {
            length: "10",
            width: "8",
            height: "2",
          },
          metaTitle: "عدسات أكيوفيو اليومية - VisionLens",
          metaDescription: "عدسات لاصقة يومية مريحة وآمنة",
          slug: "acuvue-oasys-daily",
          status: "active",
          featured: true,
          allowBackorder: false,
          specifications: [
            { key: "نوع العدسة", value: "يومية" },
            { key: "المادة", value: "سيليكون هيدروجيل" },
            { key: "نفاذية الأكسجين", value: "عالية" },
          ],
          images: [
            "https://picsum.photos/400/400?random=1",
            "https://picsum.photos/400/400?random=11",
          ],
        };
        
        setFormData(mockProduct);
        setOriginalData(mockProduct);
        setPreviewImages(mockProduct.images);
      } catch (error) {
        console.error("Error loading product:", error);
      } finally {
        setIsLoading(false);
      }
    }

    loadProduct();
  }, [productId]);

  const categories = [
    "العدسات اليومية",
    "العدسات الشهرية",
    "العدسات الملونة",
    "العدسات الأسبوعية",
    "النظارات الطبية",
    "النظارات الشمسية",
    "الإكسسوارات",
  ];

  const brands = [
    "Johnson & Johnson",
    "Alcon",
    "CooperVision",
    "Bausch & Lomb",
    "Ray-Ban",
    "Oakley",
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev: any) => ({ ...prev, [field]: value }));
    setHasChanges(true);

    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const handleDimensionChange = (dimension: string, value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      dimensions: { ...prev.dimensions, [dimension]: value }
    }));
    setHasChanges(true);
  };

  const addTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData((prev: any) => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }));
      setCurrentTag("");
      setHasChanges(true);
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData((prev: any) => ({
      ...prev,
      tags: prev.tags.filter((tag: string) => tag !== tagToRemove)
    }));
    setHasChanges(true);
  };

  const addSpecification = () => {
    setFormData((prev: any) => ({
      ...prev,
      specifications: [...prev.specifications, { key: "", value: "" }]
    }));
    setHasChanges(true);
  };

  const updateSpecification = (index: number, field: string, value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      specifications: prev.specifications.map((spec: any, i: number) =>
        i === index ? { ...spec, [field]: value } : spec
      )
    }));
    setHasChanges(true);
  };

  const removeSpecification = (index: number) => {
    setFormData((prev: any) => ({
      ...prev,
      specifications: prev.specifications.filter((_: any, i: number) => i !== index)
    }));
    setHasChanges(true);
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    setUploadingImages(true);
    const newImages: string[] = [];
    const newPreviews: string[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        const preview = URL.createObjectURL(file);
        newPreviews.push(preview);

        await new Promise(resolve => setTimeout(resolve, 1000));

        const imageUrl = `https://picsum.photos/400/400?random=${Date.now()}-${i}`;
        newImages.push(imageUrl);
      }

      setFormData((prev: any) => ({
        ...prev,
        images: [...prev.images, ...newImages]
      }));

      setPreviewImages(prev => [...prev, ...newPreviews]);
      setHasChanges(true);

    } catch (error) {
      console.error("Error uploading images:", error);
      alert("حدث خطأ أثناء رفع الصور");
    } finally {
      setUploadingImages(false);
    }
  };

  const removeImage = (index: number) => {
    setFormData((prev: any) => ({
      ...prev,
      images: prev.images.filter((_: string, i: number) => i !== index)
    }));

    setPreviewImages(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      if (prev[index]) {
        URL.revokeObjectURL(prev[index]);
      }
      return newPreviews;
    });
    setHasChanges(true);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = "اسم المنتج مطلوب";
    if (!formData.nameEn.trim()) newErrors.nameEn = "الاسم الإنجليزي مطلوب";
    if (!formData.description.trim()) newErrors.description = "الوصف مطلوب";
    if (!formData.sku.trim()) newErrors.sku = "رمز المنتج مطلوب";
    if (!formData.category) newErrors.category = "الفئة مطلوبة";
    if (!formData.brand) newErrors.brand = "العلامة التجارية مطلوبة";
    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = "السعر مطلوب ويجب أن يكون أكبر من صفر";
    }

    if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {
      newErrors.quantity = "الكمية مطلوبة";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData(originalData);
    setPreviewImages(originalData.images);
    setHasChanges(false);
    setErrors({});
  };

  const handleUpdate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      console.log("Updating product:", formData);

      await new Promise(resolve => setTimeout(resolve, 2000));

      setOriginalData(formData);
      setHasChanges(false);
      alert("تم تحديث المنتج بنجاح!");

    } catch (error) {
      console.error("Error updating product:", error);
      alert("حدث خطأ أثناء تحديث المنتج");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.")) {
      return;
    }

    try {
      // هنا سيتم حذف المنتج من API
      console.log("Deleting product:", productId);
      
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert("تم حذف المنتج بنجاح!");
      // إعادة توجيه إلى صفحة المنتجات
      // router.push("/admin/products");
      
    } catch (error) {
      console.error("Error deleting product:", error);
      alert("حدث خطأ أثناء حذف المنتج");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تحميل المنتج...</h1>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* هياكل تحميل */}
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!formData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المنتج غير موجود</h1>
          </div>
        </div>
        
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">المنتج المطلوب غير موجود أو تم حذفه</span>
            </div>
            <div className="mt-4">
              <Button asChild>
                <Link href="/admin/products">العودة إلى المنتجات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تعديل المنتج</h1>
            <p className="text-gray-600 mt-1">{formData.name}</p>
            {hasChanges && (
              <p className="text-orange-600 text-sm mt-1">⚠️ يوجد تغييرات غير محفوظة</p>
            )}
          </div>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/products/${formData.slug}`} target="_blank">
              <Eye className="h-4 w-4 mr-2" />
              معاينة
            </Link>
          </Button>
          <Button variant="outline" onClick={resetForm} disabled={!hasChanges || isSubmitting}>
            <X className="h-4 w-4 mr-2" />
            إلغاء التغييرات
          </Button>
          <Button
            variant="outline"
            onClick={handleDelete}
            className="text-red-600 hover:text-red-700"
            disabled={isSubmitting}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            حذف
          </Button>
          <Button onClick={handleUpdate} disabled={!hasChanges || isSubmitting}>
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? "جاري الحفظ..." : "حفظ التغييرات"}
          </Button>
        </div>
      </div>

      {/* معلومات المنتج الحالية */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>معلومات المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">اسم المنتج (عربي)</label>
                  <div className="p-3 bg-gray-50 rounded-md">{formData.name}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">اسم المنتج (إنجليزي)</label>
                  <div className="p-3 bg-gray-50 rounded-md">{formData.nameEn}</div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">الوصف</label>
                <div className="p-3 bg-gray-50 rounded-md">{formData.description}</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">رمز المنتج</label>
                  <div className="p-3 bg-gray-50 rounded-md font-mono">{formData.sku}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الباركود</label>
                  <div className="p-3 bg-gray-50 rounded-md font-mono">{formData.barcode}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>التصنيف والأسعار</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">الفئة</label>
                  <div className="p-3 bg-gray-50 rounded-md">{formData.category}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">العلامة التجارية</label>
                  <div className="p-3 bg-gray-50 rounded-md">{formData.brand}</div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">سعر البيع</label>
                  <div className="p-3 bg-gray-50 rounded-md font-semibold text-green-600">
                    {formData.price} د.ع
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">السعر المقارن</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {formData.comparePrice ? `${formData.comparePrice} د.ع` : "غير محدد"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">سعر التكلفة</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {formData.cost ? `${formData.cost} د.ع` : "غير محدد"}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>المخزون</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">الكمية الحالية</label>
                  <div className="p-3 bg-gray-50 rounded-md font-semibold">
                    {formData.quantity}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الحد الأدنى</label>
                  <div className="p-3 bg-gray-50 rounded-md">{formData.minQuantity}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الحد الأقصى</label>
                  <div className="p-3 bg-gray-50 rounded-md">{formData.maxQuantity}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الموقع</label>
                  <div className="p-3 bg-gray-50 rounded-md">{formData.location}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>حالة المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">الحالة</label>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  formData.status === "active"
                    ? "bg-green-100 text-green-800"
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {formData.status === "active" ? "نشط" : "غير نشط"}
                </span>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">منتج مميز</label>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  formData.featured
                    ? "bg-blue-100 text-blue-800"
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {formData.featured ? "نعم" : "لا"}
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>المواصفات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {formData.specifications.map((spec, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-sm font-medium">{spec.key}:</span>
                    <span className="text-sm text-gray-600">{spec.value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>العلامات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* ملاحظة */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-blue-800">
            <AlertCircle className="h-5 w-5" />
            <span className="font-medium">ملاحظة:</span>
          </div>
          <p className="mt-2 text-sm text-blue-700">
            هذه صفحة عرض المنتج. لتعديل المنتج، يمكنك إنشاء نموذج تعديل مشابه لنموذج الإضافة 
            مع تعبئة البيانات الحالية مسبقاً.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
