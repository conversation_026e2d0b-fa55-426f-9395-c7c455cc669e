"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  ArrowLeft,
  Edit,
  Eye,
  Trash2,
  Package,
  DollarSign,
  BarChart3,
  Calendar,
  MapPin,
  Tag,
  Star,
  TrendingUp,
  AlertCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatPrice } from "@/lib/utils";

export default function ProductDetailPage({ params }: { params: { id: string } }) {
  const [isLoading, setIsLoading] = useState(true);
  const [product, setProduct] = useState<any>(null);

  useEffect(() => {
    const loadProduct = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // بيانات وهمية مفصلة للمنتج
        const mockProduct = {
          id: params.id,
          name: "عدسات أكيوفيو اليومية",
          nameEn: "Acuvue Oasys Daily",
          description: "عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي. تتميز بتقنية HydraLuxe للترطيب المستمر وحماية من الأشعة فوق البنفسجية.",
          shortDescription: "عدسات يومية مريحة وآمنة",
          sku: "ACU-001",
          barcode: "1234567890123",
          category: "العدسات اليومية",
          brand: "Johnson & Johnson",
          tags: ["يومية", "مريحة", "آمنة", "حماية UV"],
          price: 120,
          comparePrice: 150,
          cost: 80,
          profit: 40,
          profitMargin: 33.33,
          trackQuantity: true,
          quantity: 45,
          minQuantity: 10,
          maxQuantity: 100,
          location: "A1-B2",
          weight: 50,
          dimensions: {
            length: 10,
            width: 8,
            height: 2,
          },
          metaTitle: "عدسات أكيوفيو اليومية - VisionLens",
          metaDescription: "عدسات لاصقة يومية مريحة وآمنة",
          slug: "acuvue-oasys-daily",
          status: "active",
          featured: true,
          allowBackorder: false,
          createdAt: "2024-01-01T10:00:00Z",
          updatedAt: "2024-01-15T14:30:00Z",
          specifications: [
            { key: "نوع العدسة", value: "يومية" },
            { key: "المادة", value: "سيليكون هيدروجيل" },
            { key: "نفاذية الأكسجين", value: "عالية" },
            { key: "محتوى الماء", value: "38%" },
            { key: "الحماية من الأشعة", value: "نعم" },
            { key: "العبوة", value: "30 عدسة" },
          ],
          images: [
            "https://picsum.photos/400/400?random=1",
            "https://picsum.photos/400/400?random=11",
            "https://picsum.photos/400/400?random=12",
          ],
          // إحصائيات المبيعات
          salesStats: {
            totalSold: 156,
            totalRevenue: 18720,
            averageRating: 4.8,
            reviewCount: 24,
            viewCount: 1250,
            conversionRate: 12.5,
            lastSale: "2024-01-15T16:45:00Z",
          },
          // مبيعات شهرية
          monthlySales: [
            { month: "يناير", sales: 45, revenue: 5400 },
            { month: "ديسمبر", sales: 38, revenue: 4560 },
            { month: "نوفمبر", sales: 42, revenue: 5040 },
            { month: "أكتوبر", sales: 31, revenue: 3720 },
          ],
        };
        
        setProduct(mockProduct);
      } catch (error) {
        console.error("Error loading product:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProduct();
  }, [params.id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تحميل المنتج...</h1>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">المنتج غير موجود</h1>
          </div>
        </div>
        
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">المنتج المطلوب غير موجود أو تم حذفه</span>
            </div>
            <div className="mt-4">
              <Button asChild>
                <Link href="/admin/products">العودة إلى المنتجات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-gray-600 mt-1">{product.nameEn}</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/products/${product.slug}`} target="_blank">
              <Eye className="h-4 w-4 mr-2" />
              معاينة
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/admin/products/${product.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              تعديل
            </Link>
          </Button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                <p className="text-3xl font-bold text-green-600">{product.salesStats.totalSold}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Package className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                <p className="text-3xl font-bold text-blue-600">
                  {formatPrice(product.salesStats.totalRevenue)}
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">التقييم</p>
                <div className="flex items-center gap-1">
                  <p className="text-3xl font-bold text-yellow-600">{product.salesStats.averageRating}</p>
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                </div>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">معدل التحويل</p>
                <p className="text-3xl font-bold text-purple-600">{product.salesStats.conversionRate}%</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* العمود الرئيسي */}
        <div className="lg:col-span-2 space-y-6">
          {/* معلومات المنتج */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                معلومات المنتج
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">رمز المنتج</label>
                  <div className="p-3 bg-gray-50 rounded-md font-mono">{product.sku}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الباركود</label>
                  <div className="p-3 bg-gray-50 rounded-md font-mono">{product.barcode}</div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">الوصف</label>
                <div className="p-3 bg-gray-50 rounded-md">{product.description}</div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">الفئة</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.category}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">العلامة التجارية</label>
                  <div className="p-3 bg-gray-50 rounded-md">{product.brand}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الأسعار والربحية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                الأسعار والربحية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">سعر البيع</label>
                  <div className="p-3 bg-green-50 rounded-md font-semibold text-green-700">
                    {formatPrice(product.price)}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">سعر التكلفة</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {formatPrice(product.cost)}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الربح</label>
                  <div className="p-3 bg-blue-50 rounded-md font-semibold text-blue-700">
                    {formatPrice(product.profit)}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">هامش الربح</label>
                  <div className="p-3 bg-purple-50 rounded-md font-semibold text-purple-700">
                    {product.profitMargin}%
                  </div>
                </div>
              </div>
              
              {product.comparePrice && (
                <div>
                  <label className="block text-sm font-medium mb-2">السعر المقارن</label>
                  <div className="p-3 bg-yellow-50 rounded-md">
                    {formatPrice(product.comparePrice)}
                    <span className="text-sm text-gray-600 mr-2">
                      (توفير {formatPrice(product.comparePrice - product.price)})
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* المبيعات الشهرية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                المبيعات الشهرية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {product.monthlySales.map((month: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{month.month}</p>
                      <p className="text-sm text-gray-600">{month.sales} وحدة</p>
                    </div>
                    <div className="text-left">
                      <p className="font-semibold text-green-600">{formatPrice(month.revenue)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* العمود الجانبي */}
        <div className="lg:col-span-1 space-y-6">
          {/* صور المنتج */}
          <Card>
            <CardHeader>
              <CardTitle>صور المنتج</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                {product.images.map((image: string, index: number) => (
                  <div key={index} className="aspect-square relative rounded-lg overflow-hidden">
                    <Image
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* حالة المنتج */}
          <Card>
            <CardHeader>
              <CardTitle>حالة المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">الحالة</label>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  product.status === "active" 
                    ? "bg-green-100 text-green-800" 
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {product.status === "active" ? "نشط" : "غير نشط"}
                </span>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">منتج مميز</label>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  product.featured 
                    ? "bg-blue-100 text-blue-800" 
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {product.featured ? "نعم" : "لا"}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* المخزون */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                المخزون
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">الكمية الحالية</label>
                <div className="p-3 bg-gray-50 rounded-md font-semibold text-lg">
                  {product.quantity}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">الحد الأدنى</label>
                  <div className="p-2 bg-gray-50 rounded-md text-sm">{product.minQuantity}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">الحد الأقصى</label>
                  <div className="p-2 bg-gray-50 rounded-md text-sm">{product.maxQuantity}</div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">الموقع</label>
                <div className="p-3 bg-gray-50 rounded-md font-mono">{product.location}</div>
              </div>
            </CardContent>
          </Card>

          {/* المواصفات */}
          <Card>
            <CardHeader>
              <CardTitle>المواصفات التقنية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {product.specifications.map((spec: any, index: number) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-sm font-medium">{spec.key}:</span>
                    <span className="text-sm text-gray-600">{spec.value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* العلامات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                العلامات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {product.tags.map((tag: string, index: number) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* معلومات إضافية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                معلومات إضافية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="block text-sm font-medium mb-1">تاريخ الإنشاء</label>
                <p className="text-sm text-gray-600">{formatDate(product.createdAt)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">آخر تحديث</label>
                <p className="text-sm text-gray-600">{formatDate(product.updatedAt)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">آخر بيع</label>
                <p className="text-sm text-gray-600">{formatDate(product.salesStats.lastSale)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">عدد المشاهدات</label>
                <p className="text-sm text-gray-600">{product.salesStats.viewCount.toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
