"use client";

import React, { useState } from "react";
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Send,
  MessageCircle,
  Headphones,
  Users
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
    type: "general"
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    alert("تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.");
    setFormData({
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: "",
      type: "general"
    });
  };

  const contactMethods = [
    {
      icon: Phone,
      title: "اتصل بنا",
      description: "تواصل معنا مباشرة",
      value: "+964 ************",
      action: "tel:+964770123456",
      color: "text-green-600"
    },
    {
      icon: Mail,
      title: "راسلنا",
      description: "أرسل لنا بريد إلكتروني",
      value: "<EMAIL>",
      action: "mailto:<EMAIL>",
      color: "text-blue-600"
    },
    {
      icon: MessageCircle,
      title: "واتساب",
      description: "تواصل عبر الواتساب",
      value: "+964 ************",
      action: "https://wa.me/964770123456",
      color: "text-green-500"
    },
    {
      icon: MapPin,
      title: "زورنا",
      description: "موقعنا الجغرافي",
      value: "بغداد، شارع الكرادة",
      action: "#",
      color: "text-red-600"
    }
  ];

  const workingHours = [
    { day: "السبت - الخميس", hours: "9:00 ص - 10:00 م" },
    { day: "الجمعة", hours: "2:00 م - 10:00 م" },
  ];

  const supportTypes = [
    { value: "general", label: "استفسار عام" },
    { value: "order", label: "استفسار عن طلب" },
    { value: "product", label: "استفسار عن منتج" },
    { value: "technical", label: "مشكلة تقنية" },
    { value: "complaint", label: "شكوى" },
    { value: "suggestion", label: "اقتراح" },
  ];

  return (
    <div className="min-h-screen">
      <Header />
      
      <main>
        {/* العنوان الرئيسي */}
        <section className="bg-gradient-to-r from-primary to-primary/80 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">تواصل معنا</h1>
            <p className="text-xl opacity-90 max-w-2xl mx-auto">
              نحن هنا لمساعدتك! تواصل معنا بأي طريقة تناسبك وسنكون سعداء للرد على استفساراتك
            </p>
          </div>
        </section>

        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* معلومات التواصل */}
            <div className="lg:col-span-1 space-y-6">
              <div>
                <h2 className="text-2xl font-bold mb-6">طرق التواصل</h2>
                <div className="space-y-4">
                  {contactMethods.map((method, index) => (
                    <Card key={index} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <a 
                          href={method.action}
                          className="flex items-center gap-4 group"
                          target={method.action.startsWith('http') ? '_blank' : undefined}
                        >
                          <div className={`p-3 rounded-full bg-gray-100 group-hover:bg-gray-200 transition-colors`}>
                            <method.icon className={`h-6 w-6 ${method.color}`} />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold group-hover:text-primary transition-colors">
                              {method.title}
                            </h3>
                            <p className="text-sm text-gray-600 mb-1">{method.description}</p>
                            <p className="text-sm font-medium">{method.value}</p>
                          </div>
                        </a>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* ساعات العمل */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    ساعات العمل
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {workingHours.map((schedule, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="font-medium">{schedule.day}</span>
                        <span className="text-gray-600">{schedule.hours}</span>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 p-3 bg-green-50 rounded-lg">
                    <p className="text-sm text-green-800">
                      <strong>دعم العملاء متاح 24/7</strong> عبر الواتساب والبريد الإلكتروني
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* إحصائيات الدعم */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Headphones className="h-5 w-5" />
                    خدمة العملاء
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">متوسط وقت الرد</span>
                      <span className="font-semibold text-primary">أقل من ساعة</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">معدل رضا العملاء</span>
                      <span className="font-semibold text-green-600">98%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">اللغات المدعومة</span>
                      <span className="font-semibold">العربية، الإنجليزية</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* نموذج التواصل */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Send className="h-5 w-5" />
                    أرسل لنا رسالة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">الاسم الكامل *</label>
                        <Input
                          required
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                          placeholder="أدخل اسمك الكامل"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">البريد الإلكتروني *</label>
                        <Input
                          type="email"
                          required
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">رقم الهاتف</label>
                        <Input
                          type="tel"
                          value={formData.phone}
                          onChange={(e) => handleInputChange("phone", e.target.value)}
                          placeholder="+964 ************"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">نوع الاستفسار *</label>
                        <select
                          required
                          value={formData.type}
                          onChange={(e) => handleInputChange("type", e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        >
                          {supportTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">موضوع الرسالة *</label>
                      <Input
                        required
                        value={formData.subject}
                        onChange={(e) => handleInputChange("subject", e.target.value)}
                        placeholder="اكتب موضوع رسالتك"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">الرسالة *</label>
                      <textarea
                        required
                        value={formData.message}
                        onChange={(e) => handleInputChange("message", e.target.value)}
                        placeholder="اكتب رسالتك هنا..."
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                      />
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <strong>ملاحظة:</strong> سنقوم بالرد على رسالتك خلال 24 ساعة كحد أقصى. 
                        للاستفسارات العاجلة، يرجى التواصل معنا عبر الهاتف أو الواتساب.
                      </p>
                    </div>

                    <Button type="submit" size="lg" className="w-full">
                      <Send className="h-4 w-4 mr-2" />
                      إرسال الرسالة
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* قسم الأسئلة الشائعة */}
          <section className="mt-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">الأسئلة الشائعة</h2>
              <p className="text-gray-600">إجابات سريعة لأكثر الأسئلة شيوعاً</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                {
                  question: "كم يستغرق وقت التوصيل؟",
                  answer: "عادة ما يستغرق التوصيل من 1-3 أيام عمل داخل المدن الرئيسية، و3-5 أيام للمناطق الأخرى."
                },
                {
                  question: "هل يمكنني إرجاع المنتج؟",
                  answer: "نعم، يمكنك إرجاع المنتج خلال 30 يوم من تاريخ الشراء بشرط أن يكون في حالته الأصلية."
                },
                {
                  question: "هل المنتجات أصلية؟",
                  answer: "جميع منتجاتنا أصلية 100% ومستوردة مباشرة من الشركات المصنعة المعتمدة."
                },
                {
                  question: "كيف يمكنني تتبع طلبي؟",
                  answer: "ستحصل على رقم تتبع عبر الرسائل النصية والبريد الإلكتروني فور شحن طلبك."
                }
              ].map((faq, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-3">{faq.question}</h3>
                    <p className="text-gray-600">{faq.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center mt-8">
              <p className="text-gray-600 mb-4">لم تجد إجابة لسؤالك؟</p>
              <Button variant="outline" asChild>
                <a href="#contact-form">تواصل معنا</a>
              </Button>
            </div>
          </section>

          {/* فريق الدعم */}
          <section className="mt-16 bg-gradient-to-r from-primary/10 to-primary/5 rounded-2xl p-8">
            <div className="text-center">
              <Users className="h-16 w-16 text-primary mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">فريق دعم العملاء</h2>
              <p className="text-gray-600 max-w-2xl mx-auto mb-6">
                فريقنا المتخصص من خبراء العدسات اللاصقة والنظارات الطبية جاهز لمساعدتك 
                في اختيار المنتج المناسب والإجابة على جميع استفساراتك
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <a href="tel:+964770123456">
                    <Phone className="h-4 w-4 mr-2" />
                    اتصل الآن
                  </a>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <a href="https://wa.me/964770123456" target="_blank">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    واتساب
                  </a>
                </Button>
              </div>
            </div>
          </section>
        </div>
      </main>

      <Footer />
    </div>
  );
}
