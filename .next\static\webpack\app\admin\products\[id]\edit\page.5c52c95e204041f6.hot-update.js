"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/lib/products-store.ts":
/*!***********************************!*\
  !*** ./src/lib/products-store.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductsStore: () => (/* binding */ ProductsStore)\n/* harmony export */ });\n// نظام إدارة حالة المنتجات المركزي\n// مفتاح التخزين المحلي\nconst STORAGE_KEY = 'visionlens_products';\n// تحميل المنتجات من التخزين المحلي\nconst loadProductsFromStorage = ()=>{\n    if (false) {}\n    try {\n        const stored = localStorage.getItem(STORAGE_KEY);\n        if (stored) {\n            return JSON.parse(stored);\n        }\n    } catch (error) {\n        console.error('Error loading products from storage:', error);\n    }\n    return [];\n};\n// حفظ المنتجات في التخزين المحلي\nconst saveProductsToStorage = (products)=>{\n    if (false) {}\n    try {\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(products));\n    } catch (error) {\n        console.error('Error saving products to storage:', error);\n    }\n};\n// قائمة المنتجات - تحميل من التخزين المحلي\nlet products = loadProductsFromStorage();\n// دوال إدارة المنتجات\nconst ProductsStore = {\n    // الحصول على جميع المنتجات\n    getAll: ()=>{\n        return [\n            ...products\n        ];\n    },\n    // الحصول على منتج بالمعرف\n    getById: (id)=>{\n        return products.find((p)=>p.id === id);\n    },\n    // الحصول على منتج بالـ SKU\n    getBySku: (sku)=>{\n        return products.find((p)=>p.sku === sku);\n    },\n    // إضافة منتج جديد\n    add: (productData)=>{\n        const newProduct = {\n            ...productData,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        products.push(newProduct);\n        return newProduct;\n    },\n    // تحديث منتج\n    update: (id, updates)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        products[index] = {\n            ...products[index],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        return products[index];\n    },\n    // حذف منتج\n    delete: (id)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        products.splice(index, 1);\n        return true;\n    },\n    // حذف منتجات متعددة\n    deleteMultiple: (ids)=>{\n        let deletedCount = 0;\n        ids.forEach((id)=>{\n            if (ProductsStore.delete(id)) {\n                deletedCount++;\n            }\n        });\n        return deletedCount;\n    },\n    // البحث في المنتجات\n    search: (query)=>{\n        const lowerQuery = query.toLowerCase();\n        return products.filter((product)=>product.name.toLowerCase().includes(lowerQuery) || product.nameEn.toLowerCase().includes(lowerQuery) || product.sku.toLowerCase().includes(lowerQuery) || product.brand.toLowerCase().includes(lowerQuery) || product.category.toLowerCase().includes(lowerQuery));\n    },\n    // فلترة المنتجات\n    filter: (filters)=>{\n        return products.filter((product)=>{\n            if (filters.category && filters.category !== \"الكل\" && product.category !== filters.category) {\n                return false;\n            }\n            if (filters.brand && filters.brand !== \"الكل\" && product.brand !== filters.brand) {\n                return false;\n            }\n            if (filters.status && filters.status !== \"الكل\" && product.status !== filters.status) {\n                return false;\n            }\n            if (filters.inStock !== undefined) {\n                const hasStock = product.stock > 0;\n                if (filters.inStock !== hasStock) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    },\n    // الحصول على الإحصائيات\n    getStats: ()=>{\n        const total = products.length;\n        const active = products.filter((p)=>p.status === \"active\").length;\n        const draft = products.filter((p)=>p.status === \"draft\").length;\n        const outOfStock = products.filter((p)=>p.stock === 0).length;\n        const totalStock = products.reduce((sum, p)=>sum + p.stock, 0);\n        const lowStock = products.filter((p)=>p.stock > 0 && p.stock <= 10).length;\n        return {\n            total,\n            active,\n            draft,\n            outOfStock,\n            totalStock,\n            lowStock\n        };\n    },\n    // توليد SKU تلقائي\n    generateSku: function() {\n        let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"PRD\";\n        let counter = 1;\n        let sku;\n        do {\n            sku = \"\".concat(prefix, \"-\").concat(counter.toString().padStart(3, '0'));\n            counter++;\n        }while (ProductsStore.getBySku(sku));\n        return sku;\n    },\n    // توليد slug تلقائي\n    generateSlug: (name, sku)=>{\n        let baseSlug = name.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n        if (sku) {\n            baseSlug += \"-\".concat(sku.toLowerCase());\n        }\n        let slug = baseSlug;\n        let counter = 1;\n        while(products.some((p)=>p.slug === slug)){\n            slug = \"\".concat(baseSlug, \"-\").concat(counter);\n            counter++;\n        }\n        return slug;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/products-store.ts\n"));

/***/ })

});