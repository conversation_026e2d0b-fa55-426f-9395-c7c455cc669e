"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/new/page",{

/***/ "(app-pages-browser)/./src/lib/products-store.ts":
/*!***********************************!*\
  !*** ./src/lib/products-store.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductsStore: () => (/* binding */ ProductsStore)\n/* harmony export */ });\n// نظام إدارة حالة المنتجات المركزي\n// مفتاح التخزين المحلي\nconst STORAGE_KEY = 'visionlens_products';\n// تحميل المنتجات من التخزين المحلي\nconst loadProductsFromStorage = ()=>{\n    if (false) {}\n    try {\n        const stored = localStorage.getItem(STORAGE_KEY);\n        if (stored) {\n            return JSON.parse(stored);\n        }\n    } catch (error) {\n        console.error('Error loading products from storage:', error);\n    }\n    return [];\n};\n// حفظ المنتجات في التخزين المحلي\nconst saveProductsToStorage = (products)=>{\n    if (false) {}\n    try {\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(products));\n    } catch (error) {\n        console.error('Error saving products to storage:', error);\n    }\n};\n// قائمة المنتجات - تحميل من التخزين المحلي\nlet products = loadProductsFromStorage();\n// دوال إدارة المنتجات\nconst ProductsStore = {\n    // الحصول على جميع المنتجات\n    getAll: ()=>{\n        return [\n            ...products\n        ];\n    },\n    // الحصول على منتج بالمعرف\n    getById: (id)=>{\n        return products.find((p)=>p.id === id);\n    },\n    // الحصول على منتج بالـ SKU\n    getBySku: (sku)=>{\n        return products.find((p)=>p.sku === sku);\n    },\n    // إضافة منتج جديد\n    add: (productData)=>{\n        const newProduct = {\n            ...productData,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        products.push(newProduct);\n        saveProductsToStorage(products); // حفظ في التخزين المحلي\n        return newProduct;\n    },\n    // تحديث منتج\n    update: (id, updates)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        products[index] = {\n            ...products[index],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        saveProductsToStorage(products); // حفظ في التخزين المحلي\n        return products[index];\n    },\n    // حذف منتج\n    delete: (id)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        products.splice(index, 1);\n        saveProductsToStorage(products); // حفظ في التخزين المحلي\n        return true;\n    },\n    // حذف منتجات متعددة\n    deleteMultiple: (ids)=>{\n        let deletedCount = 0;\n        ids.forEach((id)=>{\n            const index = products.findIndex((p)=>p.id === id);\n            if (index !== -1) {\n                products.splice(index, 1);\n                deletedCount++;\n            }\n        });\n        if (deletedCount > 0) {\n            saveProductsToStorage(products); // حفظ في التخزين المحلي\n        }\n        return deletedCount;\n    },\n    // البحث في المنتجات\n    search: (query)=>{\n        const lowerQuery = query.toLowerCase();\n        return products.filter((product)=>product.name.toLowerCase().includes(lowerQuery) || product.nameEn.toLowerCase().includes(lowerQuery) || product.sku.toLowerCase().includes(lowerQuery) || product.brand.toLowerCase().includes(lowerQuery) || product.category.toLowerCase().includes(lowerQuery));\n    },\n    // فلترة المنتجات\n    filter: (filters)=>{\n        return products.filter((product)=>{\n            if (filters.category && filters.category !== \"الكل\" && product.category !== filters.category) {\n                return false;\n            }\n            if (filters.brand && filters.brand !== \"الكل\" && product.brand !== filters.brand) {\n                return false;\n            }\n            if (filters.status && filters.status !== \"الكل\" && product.status !== filters.status) {\n                return false;\n            }\n            if (filters.inStock !== undefined) {\n                const hasStock = product.stock > 0;\n                if (filters.inStock !== hasStock) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    },\n    // الحصول على الإحصائيات\n    getStats: ()=>{\n        const total = products.length;\n        const active = products.filter((p)=>p.status === \"active\").length;\n        const draft = products.filter((p)=>p.status === \"draft\").length;\n        const outOfStock = products.filter((p)=>p.stock === 0).length;\n        const totalStock = products.reduce((sum, p)=>sum + p.stock, 0);\n        const lowStock = products.filter((p)=>p.stock > 0 && p.stock <= 10).length;\n        return {\n            total,\n            active,\n            draft,\n            outOfStock,\n            totalStock,\n            lowStock\n        };\n    },\n    // توليد SKU تلقائي\n    generateSku: function() {\n        let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"PRD\";\n        let counter = 1;\n        let sku;\n        do {\n            sku = \"\".concat(prefix, \"-\").concat(counter.toString().padStart(3, '0'));\n            counter++;\n        }while (ProductsStore.getBySku(sku));\n        return sku;\n    },\n    // توليد slug تلقائي\n    generateSlug: (name, sku)=>{\n        let baseSlug = name.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n        if (sku) {\n            baseSlug += \"-\".concat(sku.toLowerCase());\n        }\n        let slug = baseSlug;\n        let counter = 1;\n        while(products.some((p)=>p.slug === slug)){\n            slug = \"\".concat(baseSlug, \"-\").concat(counter);\n            counter++;\n        }\n        return slug;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/products-store.ts\n"));

/***/ })

});