"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/new/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/admin/products/new/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/currency */ \"(app-pages-browser)/./src/lib/currency.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction NewProductPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // معلومات أساسية\n        name: \"\",\n        nameEn: \"\",\n        description: \"\",\n        shortDescription: \"\",\n        sku: \"\",\n        barcode: \"\",\n        // التصنيف\n        category: \"\",\n        brand: \"\",\n        tags: [],\n        // الأسعار\n        price: \"\",\n        priceCurrency: \"IQD\",\n        comparePrice: \"\",\n        comparePriceCurrency: \"IQD\",\n        cost: \"\",\n        costCurrency: \"USD\",\n        // المخزون\n        trackQuantity: true,\n        quantity: \"\",\n        minQuantity: \"\",\n        maxQuantity: \"\",\n        location: \"\",\n        // الشحن\n        weight: \"\",\n        dimensions: {\n            length: \"\",\n            width: \"\",\n            height: \"\"\n        },\n        // SEO\n        metaTitle: \"\",\n        metaDescription: \"\",\n        slug: \"\",\n        // الحالة\n        status: \"draft\",\n        featured: false,\n        allowBackorder: false,\n        // المواصفات\n        specifications: [],\n        // الصور\n        images: []\n    });\n    const [currentTag, setCurrentTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImages, setPreviewImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCurrencyConverter, setShowCurrencyConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profitCalculation, setProfitCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        \"العدسات اليومية\",\n        \"العدسات الشهرية\",\n        \"العدسات الملونة\",\n        \"العدسات الأسبوعية\",\n        \"النظارات الطبية\",\n        \"النظارات الشمسية\",\n        \"الإكسسوارات\"\n    ];\n    const brands = [\n        \"Johnson & Johnson\",\n        \"Alcon\",\n        \"CooperVision\",\n        \"Bausch & Lomb\",\n        \"Ray-Ban\",\n        \"Oakley\"\n    ];\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // إزالة الخطأ عند التعديل\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n        // توليد slug تلقائياً من الاسم\n        if (field === \"name\" && value) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n        // حساب الربح عند تغيير الأسعار\n        if (field === \"price\" || field === \"cost\" || field === \"priceCurrency\" || field === \"costCurrency\") {\n            calculateProfit();\n        }\n    };\n    const calculateProfit = ()=>{\n        const updatedFormData = {\n            ...formData\n        };\n        if (updatedFormData.price && updatedFormData.cost) {\n            try {\n                const profit = (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.calculateProfitInIQD)(parseFloat(updatedFormData.cost), updatedFormData.costCurrency, parseFloat(updatedFormData.price), updatedFormData.priceCurrency);\n                setProfitCalculation(profit);\n            } catch (error) {\n                console.error(\"خطأ في حساب الربح:\", error);\n                setProfitCalculation(null);\n            }\n        }\n    };\n    const handleDimensionChange = (dimension, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                dimensions: {\n                    ...prev.dimensions,\n                    [dimension]: value\n                }\n            }));\n    };\n    const addTag = ()=>{\n        if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        currentTag.trim()\n                    ]\n                }));\n            setCurrentTag(\"\");\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const addSpecification = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: [\n                    ...prev.specifications,\n                    {\n                        key: \"\",\n                        value: \"\"\n                    }\n                ]\n            }));\n    };\n    const updateSpecification = (index, field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.map((spec, i)=>i === index ? {\n                        ...spec,\n                        [field]: value\n                    } : spec)\n            }));\n    };\n    const removeSpecification = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        setUploadingImages(true);\n        const newImages = [];\n        const newPreviews = [];\n        try {\n            for(let i = 0; i < files.length; i++){\n                const file = files[i];\n                // إنشاء معاينة محلية\n                const preview = URL.createObjectURL(file);\n                newPreviews.push(preview);\n                // محاكاة رفع الصورة\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // في التطبيق الحقيقي، سيتم رفع الصورة إلى الخادم\n                const imageUrl = \"https://picsum.photos/400/400?random=\".concat(Date.now(), \"-\").concat(i);\n                newImages.push(imageUrl);\n            }\n            setFormData((prev)=>({\n                    ...prev,\n                    images: [\n                        ...prev.images,\n                        ...newImages\n                    ]\n                }));\n            setPreviewImages((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error uploading images:\", error);\n            alert(\"حدث خطأ أثناء رفع الصور\");\n        } finally{\n            setUploadingImages(false);\n        }\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n        setPreviewImages((prev)=>{\n            const newPreviews = prev.filter((_, i)=>i !== index);\n            // تنظيف URL المؤقت\n            if (prev[index]) {\n                URL.revokeObjectURL(prev[index]);\n            }\n            return newPreviews;\n        });\n    };\n    const duplicateProduct = ()=>{\n        const duplicatedData = {\n            ...formData,\n            name: \"\".concat(formData.name, \" - نسخة\"),\n            nameEn: \"\".concat(formData.nameEn, \" - Copy\"),\n            sku: \"\".concat(formData.sku, \"-COPY\"),\n            slug: \"\".concat(formData.slug, \"-copy\")\n        };\n        setFormData(duplicatedData);\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) newErrors.name = \"اسم المنتج مطلوب\";\n        if (!formData.nameEn.trim()) newErrors.nameEn = \"الاسم الإنجليزي مطلوب\";\n        if (!formData.description.trim()) newErrors.description = \"الوصف مطلوب\";\n        if (!formData.sku.trim()) newErrors.sku = \"رمز المنتج مطلوب\";\n        if (!formData.category) newErrors.category = \"الفئة مطلوبة\";\n        if (!formData.brand) newErrors.brand = \"العلامة التجارية مطلوبة\";\n        if (!formData.price || parseFloat(formData.price) <= 0) {\n            newErrors.price = \"السعر مطلوب ويجب أن يكون أكبر من صفر\";\n        }\n        if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {\n            newErrors.quantity = \"الكمية مطلوبة\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // هنا سيتم إرسال البيانات إلى API\n            console.log(\"Product data:\", formData);\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            alert(\"تم إضافة المنتج بنجاح!\");\n        // إعادة توجيه إلى صفحة المنتجات\n        // router.push(\"/admin/products\");\n        } catch (error) {\n            console.error(\"Error creating product:\", error);\n            alert(\"حدث خطأ أثناء إضافة المنتج\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSaveAsDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: \"draft\"\n            }));\n        handleSubmit(new Event(\"submit\"));\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: \"active\"\n            }));\n        handleSubmit(new Event(\"submit\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إضافة منتج جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"إنشاء منتج جديد في المتجر\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: duplicateProduct,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نسخ المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleSaveAsDraft,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حفظ كمسودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handlePublish,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نشر المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 330,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (عربي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.name,\n                                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                placeholder: \"أدخل اسم المنتج\",\n                                                                className: errors.name ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (إنجليزي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.nameEn,\n                                                                onChange: (e)=>handleInputChange(\"nameEn\", e.target.value),\n                                                                placeholder: \"Product Name in English\",\n                                                                className: errors.nameEn ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.nameEn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.nameEn\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف المختصر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.shortDescription,\n                                                        onChange: (e)=>handleInputChange(\"shortDescription\", e.target.value),\n                                                        placeholder: \"وصف مختصر للمنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف التفصيلي *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                        placeholder: \"وصف تفصيلي للمنتج...\",\n                                                        rows: 4,\n                                                        className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.description ? \"border-red-500\" : \"border-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-xs mt-1\",\n                                                        children: errors.description\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"رمز المنتج (SKU) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.sku,\n                                                                onChange: (e)=>handleInputChange(\"sku\", e.target.value),\n                                                                placeholder: \"PRD-001\",\n                                                                className: errors.sku ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.sku && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.sku\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الباركود\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.barcode,\n                                                                onChange: (e)=>handleInputChange(\"barcode\", e.target.value),\n                                                                placeholder: \"1234567890123\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"التصنيف\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الفئة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.category,\n                                                                onChange: (e)=>handleInputChange(\"category\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.category ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر الفئة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category,\n                                                                            children: category\n                                                                        }, category, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.category\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.brand,\n                                                                onChange: (e)=>handleInputChange(\"brand\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.brand ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر العلامة التجارية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: brand,\n                                                                            children: brand\n                                                                        }, brand, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.brand\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"العلامات (Tags)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: currentTag,\n                                                                onChange: (e)=>setCurrentTag(e.target.value),\n                                                                placeholder: \"أضف علامة\",\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && (e.preventDefault(), addTag())\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addTag,\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-sm\",\n                                                                children: [\n                                                                    tag,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>removeTag(tag),\n                                                                        className: \"hover:text-red-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الأسعار\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"سعر البيع *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.price,\n                                                            onChange: (e)=>handleInputChange(\"price\", e.target.value),\n                                                            placeholder: \"0.00\",\n                                                            className: errors.price ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-xs mt-1\",\n                                                            children: errors.price\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر المقارن\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.comparePrice,\n                                                            onChange: (e)=>handleInputChange(\"comparePrice\", e.target.value),\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"سعر التكلفة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.cost,\n                                                            onChange: (e)=>handleInputChange(\"cost\", e.target.value),\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"إدارة المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"trackQuantity\",\n                                                        checked: formData.trackQuantity,\n                                                        onChange: (e)=>handleInputChange(\"trackQuantity\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"trackQuantity\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"تتبع الكمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.trackQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الكمية الحالية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.quantity,\n                                                                onChange: (e)=>handleInputChange(\"quantity\", e.target.value),\n                                                                placeholder: \"0\",\n                                                                className: errors.quantity ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            errors.quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأدنى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.minQuantity,\n                                                                onChange: (e)=>handleInputChange(\"minQuantity\", e.target.value),\n                                                                placeholder: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأقصى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 661,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.maxQuantity,\n                                                                onChange: (e)=>handleInputChange(\"maxQuantity\", e.target.value),\n                                                                placeholder: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الموقع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.location,\n                                                                onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                                placeholder: \"A1-B2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"allowBackorder\",\n                                                        checked: formData.allowBackorder,\n                                                        onChange: (e)=>handleInputChange(\"allowBackorder\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"allowBackorder\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"السماح بالطلب المسبق عند نفاد المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"المواصفات التقنية\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: formData.specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                placeholder: \"المواصفة\",\n                                                                value: spec.key,\n                                                                onChange: (e)=>updateSpecification(index, \"key\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                placeholder: \"القيمة\",\n                                                                value: spec.value,\n                                                                onChange: (e)=>updateSpecification(index, \"value\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>removeSpecification(index),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: addSpecification,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"إضافة مواصفة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 700,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"حالة المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"draft\",\n                                                                children: \"مسودة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"active\",\n                                                                children: \"نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"inactive\",\n                                                                children: \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"featured\",\n                                                        checked: formData.featured,\n                                                        onChange: (e)=>handleInputChange(\"featured\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"featured\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"صور المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                        children: \"اسحب الصور هنا أو انقر للتحديد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        multiple: true,\n                                                        accept: \"image/*\",\n                                                        onChange: handleImageUpload,\n                                                        className: \"hidden\",\n                                                        id: \"image-upload\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>{\n                                                            var _document_getElementById;\n                                                            return (_document_getElementById = document.getElementById('image-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                        },\n                                                        disabled: uploadingImages,\n                                                        children: uploadingImages ? \"جاري الرفع...\" : \"اختيار الصور\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.images.length > 0 || previewImages.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-2\",\n                                                children: formData.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square relative rounded-lg overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: previewImages[index] || image,\n                                                                    alt: \"صورة \".concat(index + 1),\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 814,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeImage(index),\n                                                                className: \"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded\",\n                                                                children: \"الصورة الرئيسية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 781,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"معلومات الشحن\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوزن (جرام)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        type: \"number\",\n                                                        value: formData.weight,\n                                                        onChange: (e)=>handleInputChange(\"weight\", e.target.value),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الأبعاد (سم)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.length,\n                                                                onChange: (e)=>handleDimensionChange(\"length\", e.target.value),\n                                                                placeholder: \"طول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.width,\n                                                                onChange: (e)=>handleDimensionChange(\"width\", e.target.value),\n                                                                placeholder: \"عرض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.height,\n                                                                onChange: (e)=>handleDimensionChange(\"height\", e.target.value),\n                                                                placeholder: \"ارتفاع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"تحسين محركات البحث\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الرابط (Slug)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.slug,\n                                                        onChange: (e)=>handleInputChange(\"slug\", e.target.value),\n                                                        placeholder: \"product-slug\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.metaTitle,\n                                                        onChange: (e)=>handleInputChange(\"metaTitle\", e.target.value),\n                                                        placeholder: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"وصف الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>handleInputChange(\"metaDescription\", e.target.value),\n                                                        placeholder: \"وصف الصفحة لمحركات البحث\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 746,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            Object.keys(errors).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"يرجى تصحيح الأخطاء التالية:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 939,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 text-sm text-red-700 list-disc list-inside\",\n                            children: Object.values(errors).map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: error\n                                }, index, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 943,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                    lineNumber: 938,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 937,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n        lineNumber: 328,\n        columnNumber: 5\n    }, this);\n}\n_s(NewProductPage, \"rfWQ75GOzQdmFZpMU25GmY8Du7U=\");\n_c = NewProductPage;\nvar _c;\n$RefreshReg$(_c, \"NewProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/new/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SUPPORTED_CURRENCIES: () => (/* binding */ SUPPORTED_CURRENCIES),\n/* harmony export */   autoUpdateExchangeRates: () => (/* binding */ autoUpdateExchangeRates),\n/* harmony export */   calculateProfitInIQD: () => (/* binding */ calculateProfitInIQD),\n/* harmony export */   convertCurrency: () => (/* binding */ convertCurrency),\n/* harmony export */   fetchExchangeRates: () => (/* binding */ fetchExchangeRates),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getCurrencyByCode: () => (/* binding */ getCurrencyByCode),\n/* harmony export */   getDefaultCurrency: () => (/* binding */ getDefaultCurrency),\n/* harmony export */   updateExchangeRate: () => (/* binding */ updateExchangeRate)\n/* harmony export */ });\n// نظام إدارة العملات وأسعار الصرف\n// العملات المدعومة\nconst SUPPORTED_CURRENCIES = [\n    {\n        code: \"IQD\",\n        name: \"Iraqi Dinar\",\n        nameAr: \"دينار عراقي\",\n        symbol: \"IQD\",\n        symbolAr: \"د.ع\",\n        isDefault: true,\n        isActive: true,\n        exchangeRate: 1,\n        lastUpdated: new Date().toISOString()\n    },\n    {\n        code: \"USD\",\n        name: \"US Dollar\",\n        nameAr: \"دولار أمريكي\",\n        symbol: \"$\",\n        symbolAr: \"$\",\n        isDefault: false,\n        isActive: true,\n        exchangeRate: 1310,\n        lastUpdated: new Date().toISOString()\n    }\n];\n// الحصول على العملة الافتراضية\nconst getDefaultCurrency = ()=>{\n    return SUPPORTED_CURRENCIES.find((c)=>c.isDefault) || SUPPORTED_CURRENCIES[0];\n};\n// الحصول على عملة بالكود\nconst getCurrencyByCode = (code)=>{\n    return SUPPORTED_CURRENCIES.find((c)=>c.code === code);\n};\n// تحويل المبلغ من عملة إلى أخرى\nconst convertCurrency = (amount, fromCurrency, toCurrency)=>{\n    const fromCurr = getCurrencyByCode(fromCurrency);\n    const toCurr = getCurrencyByCode(toCurrency);\n    if (!fromCurr || !toCurr) {\n        throw new Error(\"عملة غير مدعومة\");\n    }\n    // إذا كانت نفس العملة\n    if (fromCurrency === toCurrency) {\n        return amount;\n    }\n    // تحويل إلى الدينار العراقي أولاً\n    let amountInIQD;\n    if (fromCurrency === \"IQD\") {\n        amountInIQD = amount;\n    } else {\n        amountInIQD = amount * fromCurr.exchangeRate;\n    }\n    // ثم تحويل إلى العملة المطلوبة\n    if (toCurrency === \"IQD\") {\n        return Math.round(amountInIQD);\n    } else {\n        return Math.round(amountInIQD / toCurr.exchangeRate * 100) / 100;\n    }\n};\n// تنسيق المبلغ حسب العملة\nconst formatCurrency = function(amount) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"IQD\", showSymbol = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    const currency = getCurrencyByCode(currencyCode);\n    if (!currency) {\n        return amount.toString();\n    }\n    // تنسيق الأرقام حسب العملة\n    let formattedAmount;\n    if (currencyCode === \"IQD\") {\n        // الدينار العراقي - بدون فواصل عشرية\n        formattedAmount = Math.round(amount).toLocaleString(\"ar-IQ\");\n    } else {\n        // العملات الأخرى - مع فاصلتين عشريتين\n        formattedAmount = amount.toLocaleString(\"ar-IQ\", {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n        });\n    }\n    if (showSymbol) {\n        return \"\".concat(formattedAmount, \" \").concat(currency.symbolAr);\n    }\n    return formattedAmount;\n};\n// تحديث سعر الصرف\nconst updateExchangeRate = async (currencyCode, newRate)=>{\n    try {\n        const currencyIndex = SUPPORTED_CURRENCIES.findIndex((c)=>c.code === currencyCode);\n        if (currencyIndex === -1) {\n            throw new Error(\"عملة غير موجودة\");\n        }\n        SUPPORTED_CURRENCIES[currencyIndex].exchangeRate = newRate;\n        SUPPORTED_CURRENCIES[currencyIndex].lastUpdated = new Date().toISOString();\n        // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات\n        console.log(\"تم تحديث سعر صرف \".concat(currencyCode, \" إلى \").concat(newRate));\n        return true;\n    } catch (error) {\n        console.error(\"خطأ في تحديث سعر الصرف:\", error);\n        return false;\n    }\n};\n// جلب أسعار الصرف من مصدر خارجي (محاكاة)\nconst fetchExchangeRates = async ()=>{\n    try {\n        // محاكاة استدعاء API خارجي\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // أسعار وهمية - في التطبيق الحقيقي سيتم جلبها من API\n        const rates = [\n            {\n                fromCurrency: \"USD\",\n                toCurrency: \"IQD\",\n                rate: 1310,\n                lastUpdated: new Date().toISOString(),\n                source: \"Central Bank of Iraq\"\n            }\n        ];\n        return rates;\n    } catch (error) {\n        console.error(\"خطأ في جلب أسعار الصرف:\", error);\n        return [];\n    }\n};\n// تحديث أسعار الصرف تلقائياً\nconst autoUpdateExchangeRates = async ()=>{\n    try {\n        const rates = await fetchExchangeRates();\n        for (const rate of rates){\n            await updateExchangeRate(rate.fromCurrency, rate.rate);\n        }\n        console.log(\"تم تحديث أسعار الصرف تلقائياً\");\n    } catch (error) {\n        console.error(\"خطأ في التحديث التلقائي لأسعار الصرف:\", error);\n    }\n};\n// حساب الربح بالعملة المحلية\nconst calculateProfitInIQD = function(costPrice, costCurrency, sellingPrice) {\n    let sellingCurrency = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"IQD\";\n    const costInIQD = convertCurrency(costPrice, costCurrency, \"IQD\");\n    const sellingPriceInIQD = convertCurrency(sellingPrice, sellingCurrency, \"IQD\");\n    const profitInIQD = sellingPriceInIQD - costInIQD;\n    const profitMargin = sellingPriceInIQD > 0 ? profitInIQD / sellingPriceInIQD * 100 : 0;\n    return {\n        costInIQD,\n        sellingPriceInIQD,\n        profitInIQD,\n        profitMargin\n    };\n};\n// تحديث utils.ts لاستخدام النظام الجديد\nconst formatPrice = function(amount) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"IQD\";\n    return formatCurrency(amount, currencyCode, true);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/currency.ts\n"));

/***/ })

});