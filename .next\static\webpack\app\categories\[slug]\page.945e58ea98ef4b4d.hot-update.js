"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/categories/[slug]/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDiscount: () => (/* binding */ calculateDiscount),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatPrice(price) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"IQD\";\n    if (currencyCode === \"IQD\") {\n        // الدينار العراقي - بدون فواصل عشرية\n        return \"\".concat(Math.round(price).toLocaleString(\"ar-IQ\"), \" د.ع\");\n    } else if (currencyCode === \"USD\") {\n        // الدولار الأمريكي - مع فاصلتين عشريتين\n        return \"$\".concat(price.toLocaleString(\"en-US\", {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n        }));\n    } else {\n        // عملات أخرى\n        return new Intl.NumberFormat('ar-IQ', {\n            style: 'currency',\n            currency: currencyCode,\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 2\n        }).format(price);\n    }\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"ar-IQ\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    }).format(dateObj);\n}\nfunction generateSlug(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, \"\").replace(/[\\s_-]+/g, \"-\").replace(/^-+|-+$/g, \"\");\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + \"...\";\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction isValidPhone(phone) {\n    // تحديث للأرقام العراقية\n    const phoneRegex = /^(\\+964|0)?[7]\\d{8}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\nfunction calculateDiscount(originalPrice, salePrice) {\n    return Math.round((originalPrice - salePrice) / originalPrice * 100);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});