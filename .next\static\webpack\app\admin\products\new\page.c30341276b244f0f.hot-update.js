"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/new/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/admin/products/new/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NewProductPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // معلومات أساسية\n        name: \"\",\n        nameEn: \"\",\n        description: \"\",\n        shortDescription: \"\",\n        sku: \"\",\n        barcode: \"\",\n        // التصنيف\n        category: \"\",\n        brand: \"\",\n        tags: [],\n        // الأسعار\n        price: \"\",\n        priceCurrency: \"IQD\",\n        comparePrice: \"\",\n        comparePriceCurrency: \"IQD\",\n        cost: \"\",\n        costCurrency: \"USD\",\n        // المخزون\n        trackQuantity: true,\n        quantity: \"\",\n        minQuantity: \"\",\n        maxQuantity: \"\",\n        location: \"\",\n        // الشحن\n        weight: \"\",\n        dimensions: {\n            length: \"\",\n            width: \"\",\n            height: \"\"\n        },\n        // SEO\n        metaTitle: \"\",\n        metaDescription: \"\",\n        slug: \"\",\n        // الحالة\n        status: \"draft\",\n        featured: false,\n        allowBackorder: false,\n        // المواصفات\n        specifications: [],\n        // الصور\n        images: []\n    });\n    const [currentTag, setCurrentTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImages, setPreviewImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCurrencyConverter, setShowCurrencyConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profitCalculation, setProfitCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        \"العدسات اليومية\",\n        \"العدسات الشهرية\",\n        \"العدسات الملونة\",\n        \"العدسات الأسبوعية\",\n        \"النظارات الطبية\",\n        \"النظارات الشمسية\",\n        \"الإكسسوارات\"\n    ];\n    const brands = [\n        \"Johnson & Johnson\",\n        \"Alcon\",\n        \"CooperVision\",\n        \"Bausch & Lomb\",\n        \"Ray-Ban\",\n        \"Oakley\"\n    ];\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // إزالة الخطأ عند التعديل\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n        // توليد slug تلقائياً من الاسم\n        if (field === \"name\" && value) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n    };\n    const handleDimensionChange = (dimension, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                dimensions: {\n                    ...prev.dimensions,\n                    [dimension]: value\n                }\n            }));\n    };\n    const addTag = ()=>{\n        if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        currentTag.trim()\n                    ]\n                }));\n            setCurrentTag(\"\");\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const addSpecification = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: [\n                    ...prev.specifications,\n                    {\n                        key: \"\",\n                        value: \"\"\n                    }\n                ]\n            }));\n    };\n    const updateSpecification = (index, field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.map((spec, i)=>i === index ? {\n                        ...spec,\n                        [field]: value\n                    } : spec)\n            }));\n    };\n    const removeSpecification = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        setUploadingImages(true);\n        const newImages = [];\n        const newPreviews = [];\n        try {\n            for(let i = 0; i < files.length; i++){\n                const file = files[i];\n                // إنشاء معاينة محلية\n                const preview = URL.createObjectURL(file);\n                newPreviews.push(preview);\n                // محاكاة رفع الصورة\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // في التطبيق الحقيقي، سيتم رفع الصورة إلى الخادم\n                const imageUrl = \"https://picsum.photos/400/400?random=\".concat(Date.now(), \"-\").concat(i);\n                newImages.push(imageUrl);\n            }\n            setFormData((prev)=>({\n                    ...prev,\n                    images: [\n                        ...prev.images,\n                        ...newImages\n                    ]\n                }));\n            setPreviewImages((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error uploading images:\", error);\n            alert(\"حدث خطأ أثناء رفع الصور\");\n        } finally{\n            setUploadingImages(false);\n        }\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n        setPreviewImages((prev)=>{\n            const newPreviews = prev.filter((_, i)=>i !== index);\n            // تنظيف URL المؤقت\n            if (prev[index]) {\n                URL.revokeObjectURL(prev[index]);\n            }\n            return newPreviews;\n        });\n    };\n    const duplicateProduct = ()=>{\n        const duplicatedData = {\n            ...formData,\n            name: \"\".concat(formData.name, \" - نسخة\"),\n            nameEn: \"\".concat(formData.nameEn, \" - Copy\"),\n            sku: \"\".concat(formData.sku, \"-COPY\"),\n            slug: \"\".concat(formData.slug, \"-copy\")\n        };\n        setFormData(duplicatedData);\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) newErrors.name = \"اسم المنتج مطلوب\";\n        if (!formData.nameEn.trim()) newErrors.nameEn = \"الاسم الإنجليزي مطلوب\";\n        if (!formData.description.trim()) newErrors.description = \"الوصف مطلوب\";\n        if (!formData.sku.trim()) newErrors.sku = \"رمز المنتج مطلوب\";\n        if (!formData.category) newErrors.category = \"الفئة مطلوبة\";\n        if (!formData.brand) newErrors.brand = \"العلامة التجارية مطلوبة\";\n        if (!formData.price || parseFloat(formData.price) <= 0) {\n            newErrors.price = \"السعر مطلوب ويجب أن يكون أكبر من صفر\";\n        }\n        if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {\n            newErrors.quantity = \"الكمية مطلوبة\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // هنا سيتم إرسال البيانات إلى API\n            console.log(\"Product data:\", formData);\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            alert(\"تم إضافة المنتج بنجاح!\");\n        // إعادة توجيه إلى صفحة المنتجات\n        // router.push(\"/admin/products\");\n        } catch (error) {\n            console.error(\"Error creating product:\", error);\n            alert(\"حدث خطأ أثناء إضافة المنتج\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSaveAsDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: \"draft\"\n            }));\n        handleSubmit(new Event(\"submit\"));\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: \"active\"\n            }));\n        handleSubmit(new Event(\"submit\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إضافة منتج جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"إنشاء منتج جديد في المتجر\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: duplicateProduct,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نسخ المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleSaveAsDraft,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حفظ كمسودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handlePublish,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نشر المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (عربي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.name,\n                                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                placeholder: \"أدخل اسم المنتج\",\n                                                                className: errors.name ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (إنجليزي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.nameEn,\n                                                                onChange: (e)=>handleInputChange(\"nameEn\", e.target.value),\n                                                                placeholder: \"Product Name in English\",\n                                                                className: errors.nameEn ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.nameEn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.nameEn\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف المختصر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.shortDescription,\n                                                        onChange: (e)=>handleInputChange(\"shortDescription\", e.target.value),\n                                                        placeholder: \"وصف مختصر للمنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف التفصيلي *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                        placeholder: \"وصف تفصيلي للمنتج...\",\n                                                        rows: 4,\n                                                        className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.description ? \"border-red-500\" : \"border-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-xs mt-1\",\n                                                        children: errors.description\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"رمز المنتج (SKU) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.sku,\n                                                                onChange: (e)=>handleInputChange(\"sku\", e.target.value),\n                                                                placeholder: \"PRD-001\",\n                                                                className: errors.sku ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.sku && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.sku\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الباركود\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.barcode,\n                                                                onChange: (e)=>handleInputChange(\"barcode\", e.target.value),\n                                                                placeholder: \"1234567890123\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"التصنيف\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الفئة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.category,\n                                                                onChange: (e)=>handleInputChange(\"category\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.category ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر الفئة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category,\n                                                                            children: category\n                                                                        }, category, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 461,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.category\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.brand,\n                                                                onChange: (e)=>handleInputChange(\"brand\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.brand ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر العلامة التجارية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: brand,\n                                                                            children: brand\n                                                                        }, brand, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.brand\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"العلامات (Tags)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: currentTag,\n                                                                onChange: (e)=>setCurrentTag(e.target.value),\n                                                                placeholder: \"أضف علامة\",\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && (e.preventDefault(), addTag())\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addTag,\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-sm\",\n                                                                children: [\n                                                                    tag,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>removeTag(tag),\n                                                                        className: \"hover:text-red-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الأسعار\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"سعر البيع *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.price,\n                                                            onChange: (e)=>handleInputChange(\"price\", e.target.value),\n                                                            placeholder: \"0.00\",\n                                                            className: errors.price ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-xs mt-1\",\n                                                            children: errors.price\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر المقارن\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.comparePrice,\n                                                            onChange: (e)=>handleInputChange(\"comparePrice\", e.target.value),\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"سعر التكلفة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.cost,\n                                                            onChange: (e)=>handleInputChange(\"cost\", e.target.value),\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"إدارة المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"trackQuantity\",\n                                                        checked: formData.trackQuantity,\n                                                        onChange: (e)=>handleInputChange(\"trackQuantity\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"trackQuantity\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"تتبع الكمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.trackQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الكمية الحالية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.quantity,\n                                                                onChange: (e)=>handleInputChange(\"quantity\", e.target.value),\n                                                                placeholder: \"0\",\n                                                                className: errors.quantity ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            errors.quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأدنى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.minQuantity,\n                                                                onChange: (e)=>handleInputChange(\"minQuantity\", e.target.value),\n                                                                placeholder: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأقصى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.maxQuantity,\n                                                                onChange: (e)=>handleInputChange(\"maxQuantity\", e.target.value),\n                                                                placeholder: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الموقع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.location,\n                                                                onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                                placeholder: \"A1-B2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"allowBackorder\",\n                                                        checked: formData.allowBackorder,\n                                                        onChange: (e)=>handleInputChange(\"allowBackorder\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"allowBackorder\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"السماح بالطلب المسبق عند نفاد المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"المواصفات التقنية\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: formData.specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                placeholder: \"المواصفة\",\n                                                                value: spec.key,\n                                                                onChange: (e)=>updateSpecification(index, \"key\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                placeholder: \"القيمة\",\n                                                                value: spec.value,\n                                                                onChange: (e)=>updateSpecification(index, \"value\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>removeSpecification(index),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: addSpecification,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"إضافة مواصفة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"حالة المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"draft\",\n                                                                children: \"مسودة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"active\",\n                                                                children: \"نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"inactive\",\n                                                                children: \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"featured\",\n                                                        checked: formData.featured,\n                                                        onChange: (e)=>handleInputChange(\"featured\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"featured\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"صور المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                        children: \"اسحب الصور هنا أو انقر للتحديد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        multiple: true,\n                                                        accept: \"image/*\",\n                                                        onChange: handleImageUpload,\n                                                        className: \"hidden\",\n                                                        id: \"image-upload\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>{\n                                                            var _document_getElementById;\n                                                            return (_document_getElementById = document.getElementById('image-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                        },\n                                                        disabled: uploadingImages,\n                                                        children: uploadingImages ? \"جاري الرفع...\" : \"اختيار الصور\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.images.length > 0 || previewImages.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-2\",\n                                                children: formData.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square relative rounded-lg overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: previewImages[index] || image,\n                                                                    alt: \"صورة \".concat(index + 1),\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeImage(index),\n                                                                className: \"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded\",\n                                                                children: \"الصورة الرئيسية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 757,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"معلومات الشحن\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوزن (جرام)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        type: \"number\",\n                                                        value: formData.weight,\n                                                        onChange: (e)=>handleInputChange(\"weight\", e.target.value),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 827,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الأبعاد (سم)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.length,\n                                                                onChange: (e)=>handleDimensionChange(\"length\", e.target.value),\n                                                                placeholder: \"طول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.width,\n                                                                onChange: (e)=>handleDimensionChange(\"width\", e.target.value),\n                                                                placeholder: \"عرض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.height,\n                                                                onChange: (e)=>handleDimensionChange(\"height\", e.target.value),\n                                                                placeholder: \"ارتفاع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"تحسين محركات البحث\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الرابط (Slug)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.slug,\n                                                        onChange: (e)=>handleInputChange(\"slug\", e.target.value),\n                                                        placeholder: \"product-slug\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.metaTitle,\n                                                        onChange: (e)=>handleInputChange(\"metaTitle\", e.target.value),\n                                                        placeholder: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 883,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"وصف الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>handleInputChange(\"metaDescription\", e.target.value),\n                                                        placeholder: \"وصف الصفحة لمحركات البحث\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            Object.keys(errors).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"يرجى تصحيح الأخطاء التالية:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 915,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 text-sm text-red-700 list-disc list-inside\",\n                            children: Object.values(errors).map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: error\n                                }, index, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 919,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                    lineNumber: 914,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 913,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n        lineNumber: 304,\n        columnNumber: 5\n    }, this);\n}\n_s(NewProductPage, \"rfWQ75GOzQdmFZpMU25GmY8Du7U=\");\n_c = NewProductPage;\nvar _c;\n$RefreshReg$(_c, \"NewProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/new/page.tsx\n"));

/***/ })

});