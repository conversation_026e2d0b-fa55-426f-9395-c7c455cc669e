"use client";

import React, { useState } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Upload,
  Download,
  FileText,
  CheckCircle,
  AlertTriangle,
  X,
  Eye,
  RefreshCw,
  Database,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function ImportExportPage() {
  const [importFile, setImportFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [importResults, setImportResults] = useState<any[]>([]);
  const [showResults, setShowResults] = useState(false);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
    }
  };

  const handleImport = async () => {
    if (!importFile) return;

    setIsImporting(true);
    
    try {
      // محاكاة عملية الاستيراد
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // نتائج وهمية للاستيراد
      const mockResults = [
        { row: 1, name: "عدسات أكيوفيو اليومية", status: "success", message: "تم إضافة المنتج بنجاح" },
        { row: 2, name: "عدسات بايوفينيتي", status: "success", message: "تم إضافة المنتج بنجاح" },
        { row: 3, name: "نظارات طبية", status: "error", message: "رمز المنتج مكرر" },
        { row: 4, name: "عدسات ملونة", status: "warning", message: "تم التحديث - المنتج موجود مسبقاً" },
        { row: 5, name: "عدسات ديليز", status: "success", message: "تم إضافة المنتج بنجاح" },
      ];
      
      setImportResults(mockResults);
      setShowResults(true);
      setImportFile(null);
      
    } catch (error) {
      console.error("Error importing products:", error);
      alert("حدث خطأ أثناء استيراد المنتجات");
    } finally {
      setIsImporting(false);
    }
  };

  const handleExport = async (format: string) => {
    setIsExporting(true);
    
    try {
      // محاكاة عملية التصدير
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // في التطبيق الحقيقي، سيتم تحميل الملف
      const filename = `products_export_${new Date().toISOString().split('T')[0]}.${format}`;
      alert(`تم تصدير المنتجات بنجاح: ${filename}`);
      
    } catch (error) {
      console.error("Error exporting products:", error);
      alert("حدث خطأ أثناء تصدير المنتجات");
    } finally {
      setIsExporting(false);
    }
  };

  const downloadTemplate = () => {
    // في التطبيق الحقيقي، سيتم تحميل ملف القالب
    alert("تم تحميل قالب الاستيراد");
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "error":
        return <X className="h-4 w-4 text-red-600" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-50 text-green-800";
      case "error":
        return "bg-red-50 text-red-800";
      case "warning":
        return "bg-yellow-50 text-yellow-800";
      default:
        return "bg-gray-50 text-gray-800";
    }
  };

  const successCount = importResults.filter(r => r.status === "success").length;
  const errorCount = importResults.filter(r => r.status === "error").length;
  const warningCount = importResults.filter(r => r.status === "warning").length;

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">استيراد وتصدير المنتجات</h1>
            <p className="text-gray-600 mt-1">إدارة المنتجات بشكل مجمع</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* قسم الاستيراد */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              استيراد المنتجات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-sm text-gray-600">
              <p>قم برفع ملف Excel أو CSV يحتوي على بيانات المنتجات</p>
            </div>

            {/* منطقة رفع الملف */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-sm text-gray-600 mb-2">
                اسحب ملف Excel أو CSV هنا أو انقر للتحديد
              </p>
              <input
                type="file"
                accept=".xlsx,.xls,.csv"
                onChange={handleFileSelect}
                className="hidden"
                id="file-upload"
              />
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => document.getElementById('file-upload')?.click()}
              >
                اختيار ملف
              </Button>
            </div>

            {/* معلومات الملف المحدد */}
            {importFile && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-blue-800">{importFile.name}</p>
                    <p className="text-sm text-blue-600">
                      {(importFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* أزرار الإجراءات */}
            <div className="space-y-3">
              <Button 
                onClick={handleImport} 
                disabled={!importFile || isImporting}
                className="w-full"
              >
                {isImporting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    جاري الاستيراد...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    استيراد المنتجات
                  </>
                )}
              </Button>
              
              <Button variant="outline" onClick={downloadTemplate} className="w-full">
                <Download className="h-4 w-4 mr-2" />
                تحميل قالب الاستيراد
              </Button>
            </div>

            {/* تعليمات الاستيراد */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">تعليمات الاستيراد:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• استخدم قالب الاستيراد المتوفر</li>
                <li>• تأكد من صحة أرقام المنتجات (SKU)</li>
                <li>• الحقول المطلوبة: الاسم، الفئة، السعر</li>
                <li>• أقصى حجم للملف: 10 MB</li>
                <li>• الصيغ المدعومة: Excel (.xlsx, .xls), CSV</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* قسم التصدير */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              تصدير المنتجات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-sm text-gray-600">
              <p>قم بتصدير جميع المنتجات أو المنتجات المحددة</p>
            </div>

            {/* خيارات التصدير */}
            <div className="space-y-4">
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-3">تصدير جميع المنتجات</h4>
                <div className="grid grid-cols-2 gap-3">
                  <Button 
                    variant="outline" 
                    onClick={() => handleExport("xlsx")}
                    disabled={isExporting}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Excel
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => handleExport("csv")}
                    disabled={isExporting}
                  >
                    <Database className="h-4 w-4 mr-2" />
                    CSV
                  </Button>
                </div>
              </div>

              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-3">تصدير مخصص</h4>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full" disabled={isExporting}>
                    <Eye className="h-4 w-4 mr-2" />
                    تصدير المنتجات النشطة فقط
                  </Button>
                  <Button variant="outline" className="w-full" disabled={isExporting}>
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    تصدير المنتجات منخفضة المخزون
                  </Button>
                  <Button variant="outline" className="w-full" disabled={isExporting}>
                    <X className="h-4 w-4 mr-2" />
                    تصدير المنتجات غير النشطة
                  </Button>
                </div>
              </div>
            </div>

            {/* معلومات التصدير */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">معلومات التصدير:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• يتم تصدير جميع بيانات المنتج</li>
                <li>• يشمل الصور والمواصفات</li>
                <li>• متوافق مع أنظمة إدارة المخزون</li>
                <li>• يمكن إعادة استيراد الملف المصدر</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* نتائج الاستيراد */}
      {showResults && importResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                نتائج الاستيراد
              </div>
              <Button variant="outline" size="sm" onClick={() => setShowResults(false)}>
                <X className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* ملخص النتائج */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="p-4 bg-green-50 rounded-lg text-center">
                <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-green-800">{successCount}</div>
                <p className="text-sm text-green-600">نجح</p>
              </div>
              
              <div className="p-4 bg-yellow-50 rounded-lg text-center">
                <AlertTriangle className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-yellow-800">{warningCount}</div>
                <p className="text-sm text-yellow-600">تحذير</p>
              </div>
              
              <div className="p-4 bg-red-50 rounded-lg text-center">
                <X className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-red-800">{errorCount}</div>
                <p className="text-sm text-red-600">خطأ</p>
              </div>
            </div>

            {/* تفاصيل النتائج */}
            <div className="space-y-3">
              <h4 className="font-medium">تفاصيل النتائج:</h4>
              <div className="max-h-64 overflow-y-auto space-y-2">
                {importResults.map((result, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-lg ${getStatusColor(result.status)}`}
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result.status)}
                      <div>
                        <p className="font-medium text-sm">الصف {result.row}: {result.name}</p>
                        <p className="text-xs opacity-75">{result.message}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="mt-6 flex gap-2">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                تحميل تقرير مفصل
              </Button>
              <Button variant="outline" onClick={() => setImportResults([])}>
                مسح النتائج
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6 text-center">
            <Upload className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">156</div>
            <p className="text-sm text-gray-600">منتجات مستوردة</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <Download className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">89</div>
            <p className="text-sm text-gray-600">منتجات مصدرة</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <FileText className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">12</div>
            <p className="text-sm text-gray-600">ملفات معالجة</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <CheckCircle className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">98%</div>
            <p className="text-sm text-gray-600">معدل النجاح</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
