"use client";

import React, { useState } from "react";
import { Filter, Grid, List, SlidersHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import ProductCard from "@/components/product/product-card";

// بيانات وهمية للمنتجات
const allProducts = [
  {
    id: "1",
    name: "Acuvue Oasys Daily",
    nameAr: "عدسات أكيوفيو أوازيس اليومية",
    slug: "acuvue-oasys-daily",
    price: 120,
    comparePrice: 150,
    image: "https://picsum.photos/400/400?random=1",
    brand: "<PERSON> & Johnson",
    category: "العدسات اليومية",
    rating: 4.8,
    reviewCount: 124,
    isNew: true,
    isFeatured: true,
    inStock: true,
  },
  {
    id: "2",
    name: "Biofinity Monthly",
    nameAr: "عدسات بايوفينيتي الشهرية",
    slug: "biofinity-monthly",
    price: 85,
    comparePrice: 100,
    image: "https://picsum.photos/400/400?random=2",
    brand: "CooperVision",
    category: "العدسات الشهرية",
    rating: 4.6,
    reviewCount: 89,
    isFeatured: true,
    inStock: true,
  },
  {
    id: "3",
    name: "Air Optix Colors",
    nameAr: "عدسات إير أوبتكس الملونة",
    slug: "air-optix-colors",
    price: 95,
    image: "https://picsum.photos/400/400?random=3",
    brand: "Alcon",
    category: "العدسات الملونة",
    rating: 4.7,
    reviewCount: 156,
    inStock: true,
  },
  {
    id: "4",
    name: "Dailies Total 1",
    nameAr: "عدسات ديليز توتال ون",
    slug: "dailies-total-1",
    price: 140,
    comparePrice: 160,
    image: "https://picsum.photos/400/400?random=4",
    brand: "Alcon",
    category: "العدسات اليومية",
    rating: 4.9,
    reviewCount: 203,
    isNew: true,
    inStock: true,
  },
  {
    id: "5",
    name: "Classic Glasses",
    nameAr: "نظارات طبية كلاسيكية",
    slug: "classic-glasses",
    price: 350,
    comparePrice: 400,
    image: "https://picsum.photos/400/400?random=5",
    brand: "Ray-Ban",
    category: "النظارات الطبية",
    rating: 4.5,
    reviewCount: 67,
    inStock: true,
  },
  {
    id: "6",
    name: "Colored Lenses Blue",
    nameAr: "عدسات ملونة زرقاء",
    slug: "colored-lenses-blue",
    price: 75,
    image: "https://picsum.photos/400/400?random=6",
    brand: "FreshLook",
    category: "العدسات الملونة",
    rating: 4.3,
    reviewCount: 45,
    inStock: true,
  },
  {
    id: "7",
    name: "Weekly Lenses",
    nameAr: "عدسات أسبوعية",
    slug: "weekly-lenses",
    price: 65,
    comparePrice: 80,
    image: "https://picsum.photos/400/400?random=7",
    brand: "Bausch & Lomb",
    category: "العدسات الأسبوعية",
    rating: 4.4,
    reviewCount: 78,
    inStock: false,
  },
  {
    id: "8",
    name: "Sports Glasses",
    nameAr: "نظارات رياضية",
    slug: "sports-glasses",
    price: 280,
    image: "https://picsum.photos/400/400?random=8",
    brand: "Oakley",
    category: "النظارات الرياضية",
    rating: 4.6,
    reviewCount: 92,
    inStock: true,
  },
];

const categories = ["الكل", ...Array.from(new Set(allProducts.map(p => p.category)))];
const brands = ["الكل", ...Array.from(new Set(allProducts.map(p => p.brand)))];
const priceRanges = [
  { label: "الكل", min: 0, max: Infinity },
  { label: "أقل من 100 ريال", min: 0, max: 100 },
  { label: "100 - 200 ريال", min: 100, max: 200 },
  { label: "200 - 300 ريال", min: 200, max: 300 },
  { label: "أكثر من 300 ريال", min: 300, max: Infinity },
];

export default function ProductsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("الكل");
  const [selectedBrand, setSelectedBrand] = useState("الكل");
  const [selectedPriceRange, setSelectedPriceRange] = useState(priceRanges[0]);
  const [sortBy, setSortBy] = useState("الأحدث");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);

  const filteredProducts = allProducts.filter((product) => {
    const matchesSearch = product.nameAr.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === "الكل" || product.category === selectedCategory;
    const matchesBrand = selectedBrand === "الكل" || product.brand === selectedBrand;
    const matchesPrice = product.price >= selectedPriceRange.min && product.price <= selectedPriceRange.max;
    
    return matchesSearch && matchesCategory && matchesBrand && matchesPrice;
  });

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "السعر: من الأقل للأعلى":
        return a.price - b.price;
      case "السعر: من الأعلى للأقل":
        return b.price - a.price;
      case "الأعلى تقييماً":
        return (b.rating || 0) - (a.rating || 0);
      case "الأكثر مبيعاً":
        return (b.reviewCount || 0) - (a.reviewCount || 0);
      default:
        return 0;
    }
  });

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان والبحث */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">جميع المنتجات</h1>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="ابحث عن المنتجات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                فلاتر
              </Button>
              <Button
                variant="outline"
                onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
              >
                {viewMode === "grid" ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>

        <div className="flex gap-8">
          {/* الفلاتر الجانبية */}
          <aside className={`w-64 space-y-6 ${showFilters ? 'block' : 'hidden'} md:block`}>
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-3">الفئات</h3>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <label key={category} className="flex items-center">
                      <input
                        type="radio"
                        name="category"
                        value={category}
                        checked={selectedCategory === category}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm">{category}</span>
                    </label>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-3">العلامات التجارية</h3>
                <div className="space-y-2">
                  {brands.map((brand) => (
                    <label key={brand} className="flex items-center">
                      <input
                        type="radio"
                        name="brand"
                        value={brand}
                        checked={selectedBrand === brand}
                        onChange={(e) => setSelectedBrand(e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm">{brand}</span>
                    </label>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-3">نطاق السعر</h3>
                <div className="space-y-2">
                  {priceRanges.map((range) => (
                    <label key={range.label} className="flex items-center">
                      <input
                        type="radio"
                        name="priceRange"
                        value={range.label}
                        checked={selectedPriceRange.label === range.label}
                        onChange={() => setSelectedPriceRange(range)}
                        className="mr-2"
                      />
                      <span className="text-sm">{range.label}</span>
                    </label>
                  ))}
                </div>
              </CardContent>
            </Card>
          </aside>

          {/* المنتجات */}
          <div className="flex-1">
            {/* شريط الترتيب */}
            <div className="flex justify-between items-center mb-6">
              <p className="text-gray-600">
                عرض {sortedProducts.length} من {allProducts.length} منتج
              </p>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="الأحدث">الأحدث</option>
                <option value="السعر: من الأقل للأعلى">السعر: من الأقل للأعلى</option>
                <option value="السعر: من الأعلى للأقل">السعر: من الأعلى للأقل</option>
                <option value="الأعلى تقييماً">الأعلى تقييماً</option>
                <option value="الأكثر مبيعاً">الأكثر مبيعاً</option>
              </select>
            </div>

            {/* شبكة المنتجات */}
            {sortedProducts.length > 0 ? (
              <div className={`grid gap-6 ${
                viewMode === "grid" 
                  ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
                  : "grid-cols-1"
              }`}>
                {sortedProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">لا توجد منتجات تطابق البحث</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedCategory("الكل");
                    setSelectedBrand("الكل");
                    setSelectedPriceRange(priceRanges[0]);
                  }}
                >
                  إعادة تعيين الفلاتر
                </Button>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
