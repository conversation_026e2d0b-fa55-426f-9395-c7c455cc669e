"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  Download,
  Plus,
  Minus,
  Edit,
  RefreshCw,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatPrice } from "@/lib/utils";

// بيانات وهمية للمخزون
const inventoryItems = [
  {
    id: "1",
    name: "عدسات أكيوفيو اليومية",
    sku: "ACU-001",
    image: "https://picsum.photos/60/60?random=1",
    category: "العدسات اليومية",
    brand: "<PERSON> & Johnson",
    currentStock: 45,
    minStock: 10,
    maxStock: 100,
    cost: 80,
    price: 120,
    lastRestocked: "2024-01-10",
    status: "متوفر",
    location: "A1-B2",
  },
  {
    id: "2",
    name: "عدسات بايوفينيتي الشهرية",
    sku: "BIO-002",
    image: "https://picsum.photos/60/60?random=2",
    category: "العدسات الشهرية",
    brand: "CooperVision",
    currentStock: 8,
    minStock: 15,
    maxStock: 80,
    cost: 60,
    price: 85,
    lastRestocked: "2024-01-05",
    status: "مخزون منخفض",
    location: "A2-B1",
  },
  {
    id: "3",
    name: "عدسات إير أوبتكس الملونة",
    sku: "AIR-003",
    image: "https://picsum.photos/60/60?random=3",
    category: "العدسات الملونة",
    brand: "Alcon",
    currentStock: 0,
    minStock: 5,
    maxStock: 50,
    cost: 70,
    price: 95,
    lastRestocked: "2023-12-20",
    status: "نفد المخزون",
    location: "B1-C2",
  },
  {
    id: "4",
    name: "نظارات طبية كلاسيكية",
    sku: "GLS-004",
    image: "https://picsum.photos/60/60?random=4",
    category: "النظارات الطبية",
    brand: "Ray-Ban",
    currentStock: 25,
    minStock: 5,
    maxStock: 30,
    cost: 250,
    price: 350,
    lastRestocked: "2024-01-08",
    status: "متوفر",
    location: "C1-A1",
  },
  {
    id: "5",
    name: "عدسات ديليز توتال ون",
    sku: "DAI-005",
    image: "https://picsum.photos/60/60?random=5",
    category: "العدسات اليومية",
    brand: "Alcon",
    currentStock: 3,
    minStock: 8,
    maxStock: 60,
    cost: 100,
    price: 140,
    lastRestocked: "2023-12-28",
    status: "مخزون منخفض",
    location: "A1-C3",
  },
];

export default function InventoryPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("الكل");
  const [selectedCategory, setSelectedCategory] = useState("الكل");

  const statuses = ["الكل", "متوفر", "مخزون منخفض", "نفد المخزون"];
  const categories = ["الكل", ...Array.from(new Set(inventoryItems.map(item => item.category)))];

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.brand.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = selectedStatus === "الكل" || item.status === selectedStatus;
    const matchesCategory = selectedCategory === "الكل" || item.category === selectedCategory;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "متوفر":
        return "bg-green-100 text-green-800";
      case "مخزون منخفض":
        return "bg-yellow-100 text-yellow-800";
      case "نفد المخزون":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStockIcon = (current: number, min: number) => {
    if (current === 0) return <AlertTriangle className="h-4 w-4 text-red-500" />;
    if (current <= min) return <TrendingDown className="h-4 w-4 text-yellow-500" />;
    return <TrendingUp className="h-4 w-4 text-green-500" />;
  };

  const totalItems = inventoryItems.length;
  const lowStockItems = inventoryItems.filter(item => item.currentStock <= item.minStock && item.currentStock > 0).length;
  const outOfStockItems = inventoryItems.filter(item => item.currentStock === 0).length;
  const totalValue = inventoryItems.reduce((sum, item) => sum + (item.currentStock * item.cost), 0);

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إدارة المخزون</h1>
          <p className="text-gray-600 mt-1">متابعة وإدارة مخزون المنتجات</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            تصدير المخزون
          </Button>
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            تحديث المخزون
          </Button>
        </div>
      </div>

      {/* إحصائيات المخزون */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المنتجات</p>
                <p className="text-3xl font-bold">{totalItems}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مخزون منخفض</p>
                <p className="text-3xl font-bold text-yellow-600">{lowStockItems}</p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <TrendingDown className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">نفد المخزون</p>
                <p className="text-3xl font-bold text-red-600">{outOfStockItems}</p>
              </div>
              <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">قيمة المخزون</p>
                <p className="text-3xl font-bold text-green-600">
                  {formatPrice(totalValue)}
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والفلترة */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المخزون..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {statuses.map((status) => (
                  <option key={status} value={status}>
                    {status}
                  </option>
                ))}
              </select>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                فلترة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول المخزون */}
      <Card>
        <CardHeader>
          <CardTitle>تفاصيل المخزون ({filteredItems.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right py-3 px-4 font-medium">المنتج</th>
                  <th className="text-right py-3 px-4 font-medium">المخزون الحالي</th>
                  <th className="text-right py-3 px-4 font-medium">الحد الأدنى</th>
                  <th className="text-right py-3 px-4 font-medium">الموقع</th>
                  <th className="text-right py-3 px-4 font-medium">التكلفة</th>
                  <th className="text-right py-3 px-4 font-medium">آخر تجديد</th>
                  <th className="text-right py-3 px-4 font-medium">الحالة</th>
                  <th className="text-right py-3 px-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredItems.map((item) => (
                  <tr key={item.id} className="border-b hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <Image
                          src={item.image}
                          alt={item.name}
                          width={40}
                          height={40}
                          className="rounded-lg object-cover"
                        />
                        <div>
                          <p className="font-medium text-sm">{item.name}</p>
                          <p className="text-xs text-gray-500">{item.sku}</p>
                          <p className="text-xs text-gray-500">{item.brand}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        {getStockIcon(item.currentStock, item.minStock)}
                        <span className="font-medium">{item.currentStock}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-sm">{item.minStock}</td>
                    <td className="py-4 px-4 text-sm">{item.location}</td>
                    <td className="py-4 px-4 text-sm">
                      <div>
                        <p className="font-medium">{formatPrice(item.cost)}</p>
                        <p className="text-xs text-gray-500">
                          القيمة: {formatPrice(item.currentStock * item.cost)}
                        </p>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-sm">
                      {new Date(item.lastRestocked).toLocaleDateString("ar-SA")}
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                        {item.status}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Plus className="h-3 w-3" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredItems.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">لا توجد منتجات تطابق البحث</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* تنبيهات المخزون */}
      {(lowStockItems > 0 || outOfStockItems > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-600">
              <AlertTriangle className="h-5 w-5" />
              تنبيهات المخزون
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {outOfStockItems > 0 && (
                <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="font-medium text-red-800">
                      {outOfStockItems} منتج نفد من المخزون
                    </p>
                    <p className="text-sm text-red-600">
                      يحتاج إلى إعادة تجديد فوري
                    </p>
                  </div>
                </div>
              )}
              
              {lowStockItems > 0 && (
                <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                  <TrendingDown className="h-5 w-5 text-yellow-500" />
                  <div>
                    <p className="font-medium text-yellow-800">
                      {lowStockItems} منتج بمخزون منخفض
                    </p>
                    <p className="text-sm text-yellow-600">
                      يُنصح بإعادة التجديد قريباً
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
