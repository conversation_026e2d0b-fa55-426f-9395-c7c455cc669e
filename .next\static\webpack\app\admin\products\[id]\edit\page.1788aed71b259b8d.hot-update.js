"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/admin/products/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction EditProductPage(param) {\n    let { params } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [originalData, setOriginalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTag, setCurrentTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImages, setPreviewImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditProductPage.useEffect\": ()=>{\n            // محاكاة تحميل بيانات المنتج\n            const loadProduct = {\n                \"EditProductPage.useEffect.loadProduct\": async ()=>{\n                    try {\n                        // هنا سيتم جلب بيانات المنتج من API\n                        await new Promise({\n                            \"EditProductPage.useEffect.loadProduct\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"EditProductPage.useEffect.loadProduct\"]);\n                        // بيانات وهمية للمنتج\n                        const mockProduct = {\n                            id: params.id,\n                            name: \"عدسات أكيوفيو اليومية\",\n                            nameEn: \"Acuvue Oasys Daily\",\n                            description: \"عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي\",\n                            shortDescription: \"عدسات يومية مريحة\",\n                            sku: \"ACU-001\",\n                            barcode: \"1234567890123\",\n                            category: \"العدسات اليومية\",\n                            brand: \"Johnson & Johnson\",\n                            tags: [\n                                \"يومية\",\n                                \"مريحة\",\n                                \"آمنة\"\n                            ],\n                            price: \"120\",\n                            comparePrice: \"150\",\n                            cost: \"80\",\n                            trackQuantity: true,\n                            quantity: \"45\",\n                            minQuantity: \"10\",\n                            maxQuantity: \"100\",\n                            location: \"A1-B2\",\n                            weight: \"50\",\n                            dimensions: {\n                                length: \"10\",\n                                width: \"8\",\n                                height: \"2\"\n                            },\n                            metaTitle: \"عدسات أكيوفيو اليومية - VisionLens\",\n                            metaDescription: \"عدسات لاصقة يومية مريحة وآمنة\",\n                            slug: \"acuvue-oasys-daily\",\n                            status: \"active\",\n                            featured: true,\n                            allowBackorder: false,\n                            specifications: [\n                                {\n                                    key: \"نوع العدسة\",\n                                    value: \"يومية\"\n                                },\n                                {\n                                    key: \"المادة\",\n                                    value: \"سيليكون هيدروجيل\"\n                                },\n                                {\n                                    key: \"نفاذية الأكسجين\",\n                                    value: \"عالية\"\n                                }\n                            ],\n                            images: [\n                                \"https://picsum.photos/400/400?random=1\",\n                                \"https://picsum.photos/400/400?random=11\"\n                            ]\n                        };\n                        setFormData(mockProduct);\n                        setOriginalData(mockProduct);\n                        setPreviewImages(mockProduct.images);\n                    } catch (error) {\n                        console.error(\"Error loading product:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"EditProductPage.useEffect.loadProduct\"];\n            loadProduct();\n        }\n    }[\"EditProductPage.useEffect\"], [\n        params.id\n    ]);\n    const categories = [\n        \"العدسات اليومية\",\n        \"العدسات الشهرية\",\n        \"العدسات الملونة\",\n        \"العدسات الأسبوعية\",\n        \"النظارات الطبية\",\n        \"النظارات الشمسية\",\n        \"الإكسسوارات\"\n    ];\n    const brands = [\n        \"Johnson & Johnson\",\n        \"Alcon\",\n        \"CooperVision\",\n        \"Bausch & Lomb\",\n        \"Ray-Ban\",\n        \"Oakley\"\n    ];\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setHasChanges(true);\n        // إزالة الخطأ عند التعديل\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const handleDimensionChange = (dimension, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                dimensions: {\n                    ...prev.dimensions,\n                    [dimension]: value\n                }\n            }));\n        setHasChanges(true);\n    };\n    const addTag = ()=>{\n        if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        currentTag.trim()\n                    ]\n                }));\n            setCurrentTag(\"\");\n            setHasChanges(true);\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n        setHasChanges(true);\n    };\n    const addSpecification = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: [\n                    ...prev.specifications,\n                    {\n                        key: \"\",\n                        value: \"\"\n                    }\n                ]\n            }));\n        setHasChanges(true);\n    };\n    const updateSpecification = (index, field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.map((spec, i)=>i === index ? {\n                        ...spec,\n                        [field]: value\n                    } : spec)\n            }));\n        setHasChanges(true);\n    };\n    const removeSpecification = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.filter((_, i)=>i !== index)\n            }));\n        setHasChanges(true);\n    };\n    const handleImageUpload = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        setUploadingImages(true);\n        const newImages = [];\n        const newPreviews = [];\n        try {\n            for(let i = 0; i < files.length; i++){\n                const file = files[i];\n                const preview = URL.createObjectURL(file);\n                newPreviews.push(preview);\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                const imageUrl = \"https://picsum.photos/400/400?random=\".concat(Date.now(), \"-\").concat(i);\n                newImages.push(imageUrl);\n            }\n            setFormData((prev)=>({\n                    ...prev,\n                    images: [\n                        ...prev.images,\n                        ...newImages\n                    ]\n                }));\n            setPreviewImages((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n            setHasChanges(true);\n        } catch (error) {\n            console.error(\"Error uploading images:\", error);\n            alert(\"حدث خطأ أثناء رفع الصور\");\n        } finally{\n            setUploadingImages(false);\n        }\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n        setPreviewImages((prev)=>{\n            const newPreviews = prev.filter((_, i)=>i !== index);\n            if (prev[index]) {\n                URL.revokeObjectURL(prev[index]);\n            }\n            return newPreviews;\n        });\n        setHasChanges(true);\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) newErrors.name = \"اسم المنتج مطلوب\";\n        if (!formData.nameEn.trim()) newErrors.nameEn = \"الاسم الإنجليزي مطلوب\";\n        if (!formData.description.trim()) newErrors.description = \"الوصف مطلوب\";\n        if (!formData.sku.trim()) newErrors.sku = \"رمز المنتج مطلوب\";\n        if (!formData.category) newErrors.category = \"الفئة مطلوبة\";\n        if (!formData.brand) newErrors.brand = \"العلامة التجارية مطلوبة\";\n        if (!formData.price || parseFloat(formData.price) <= 0) {\n            newErrors.price = \"السعر مطلوب ويجب أن يكون أكبر من صفر\";\n        }\n        if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {\n            newErrors.quantity = \"الكمية مطلوبة\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const resetForm = ()=>{\n        setFormData(originalData);\n        setPreviewImages(originalData.images);\n        setHasChanges(false);\n        setErrors({});\n    };\n    const handleUpdate = async ()=>{\n        setIsSubmitting(true);\n        try {\n            // هنا سيتم إرسال البيانات المحدثة إلى API\n            console.log(\"Updating product:\", product);\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            alert(\"تم تحديث المنتج بنجاح!\");\n        } catch (error) {\n            console.error(\"Error updating product:\", error);\n            alert(\"حدث خطأ أثناء تحديث المنتج\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDelete = async ()=>{\n        if (!confirm(\"هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.\")) {\n            return;\n        }\n        try {\n            // هنا سيتم حذف المنتج من API\n            console.log(\"Deleting product:\", params.id);\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            alert(\"تم حذف المنتج بنجاح!\");\n        // إعادة توجيه إلى صفحة المنتجات\n        // router.push(\"/admin/products\");\n        } catch (error) {\n            console.error(\"Error deleting product:\", error);\n            alert(\"حدث خطأ أثناء حذف المنتج\");\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"icon\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/products\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"تحميل المنتج...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 306,\n            columnNumber: 7\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"icon\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/products\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"المنتج غير موجود\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"المنتج المطلوب غير موجود أو تم حذفه\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/admin/products\",\n                                        children: \"العودة إلى المنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 341,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"تعديل المنتج\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products/\".concat(product.slug),\n                                    target: \"_blank\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"معاينة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleDelete,\n                                className: \"text-red-600 hover:text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حذف\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleUpdate,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    isSubmitting ? \"جاري الحفظ...\" : \"حفظ التغييرات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"معلومات المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (عربي)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (إنجليزي)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.nameEn\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-gray-50 rounded-md\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"رمز المنتج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md font-mono\",\n                                                                children: product.sku\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الباركود\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md font-mono\",\n                                                                children: product.barcode\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"التصنيف والأسعار\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الفئة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.brand\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"سعر البيع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md font-semibold text-green-600\",\n                                                                children: [\n                                                                    product.price,\n                                                                    \" ريال\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"السعر المقارن\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.comparePrice ? \"\".concat(product.comparePrice, \" ريال\") : \"غير محدد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"سعر التكلفة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.cost ? \"\".concat(product.cost, \" ريال\") : \"غير محدد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الكمية الحالية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 rounded-md font-semibold\",\n                                                            children: product.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الحد الأدنى\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 rounded-md\",\n                                                            children: product.minQuantity\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الحد الأقصى\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 rounded-md\",\n                                                            children: product.maxQuantity\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الموقع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 rounded-md\",\n                                                            children: product.location\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"حالة المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(product.status === \"active\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                                        children: product.status === \"active\" ? \"نشط\" : \"غير نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(product.featured ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                        children: product.featured ? \"نعم\" : \"لا\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"المواصفات\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: product.specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: [\n                                                                spec.key,\n                                                                \":\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: spec.value\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"العلامات\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: product.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md text-sm\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 409,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-blue-200 bg-blue-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"ملاحظة:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 582,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-blue-700\",\n                            children: \"هذه صفحة عرض المنتج. لتعديل المنتج، يمكنك إنشاء نموذج تعديل مشابه لنموذج الإضافة مع تعبئة البيانات الحالية مسبقاً.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 580,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n        lineNumber: 371,\n        columnNumber: 5\n    }, this);\n}\n_s(EditProductPage, \"2oo3GQebJ9DgC9Y834lIQkV3bIE=\");\n_c = EditProductPage;\nvar _c;\n$RefreshReg$(_c, \"EditProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/[id]/edit/page.tsx\n"));

/***/ })

});