"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/settings/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/settings/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/admin/products/settings/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductSettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ProductSettingsPage() {\n    _s();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // إعدادات عامة\n        defaultCategory: \"العدسات اليومية\",\n        defaultBrand: \"\",\n        autoGenerateSku: true,\n        skuPrefix: \"PRD\",\n        requireImages: true,\n        maxImages: \"10\",\n        // إعدادات الأسعار\n        defaultCurrency: \"IQD\",\n        importCurrency: \"USD\",\n        autoConvertPrices: true,\n        taxRate: \"0\",\n        includeTaxInPrice: false,\n        allowNegativeStock: false,\n        lowStockThreshold: \"10\",\n        showPriceInMultipleCurrencies: false,\n        // إعدادات الصور\n        imageQuality: \"high\",\n        maxImageSize: 5,\n        allowedFormats: [\n            \"jpg\",\n            \"jpeg\",\n            \"png\",\n            \"webp\"\n        ],\n        autoResize: true,\n        watermark: false,\n        // إعدادات المخزون\n        trackInventory: true,\n        autoUpdateStock: true,\n        stockAlerts: true,\n        reserveStock: true,\n        backorderEnabled: false,\n        // إعدادات SEO\n        autoGenerateSlug: true,\n        slugFormat: \"name-sku\",\n        metaTitleFormat: \"{name} - {brand}\",\n        metaDescriptionLength: 160,\n        // إعدادات الأمان\n        requireApproval: false,\n        auditChanges: true,\n        backupFrequency: \"daily\",\n        dataRetention: 365,\n        // إعدادات الإشعارات\n        notifyLowStock: true,\n        notifyNewProduct: true,\n        notifyPriceChange: true,\n        notifyStockUpdate: true,\n        // إعدادات الأداء\n        enableCaching: true,\n        cacheExpiry: 3600,\n        enableCompression: true,\n        lazyLoadImages: true\n    });\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            // محاكاة حفظ الإعدادات\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            console.log(\"Saving settings:\", settings);\n            alert(\"تم حفظ الإعدادات بنجاح!\");\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"حدث خطأ أثناء حفظ الإعدادات\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const resetToDefaults = ()=>{\n        if (confirm(\"هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\")) {\n            // إعادة تعيين الإعدادات\n            setSettings({\n                defaultCategory: \"العدسات اليومية\",\n                defaultBrand: \"\",\n                autoGenerateSku: true,\n                skuPrefix: \"PRD\",\n                requireImages: true,\n                maxImages: 10,\n                defaultCurrency: \"SAR\",\n                taxRate: 15,\n                includeTaxInPrice: true,\n                allowNegativeStock: false,\n                lowStockThreshold: 10,\n                imageQuality: \"high\",\n                maxImageSize: 5,\n                allowedFormats: [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"webp\"\n                ],\n                autoResize: true,\n                watermark: false,\n                trackInventory: true,\n                autoUpdateStock: true,\n                stockAlerts: true,\n                reserveStock: true,\n                backorderEnabled: false,\n                autoGenerateSlug: true,\n                slugFormat: \"name-sku\",\n                metaTitleFormat: \"{name} - {brand}\",\n                metaDescriptionLength: 160,\n                requireApproval: false,\n                auditChanges: true,\n                backupFrequency: \"daily\",\n                dataRetention: 365,\n                notifyLowStock: true,\n                notifyNewProduct: true,\n                notifyPriceChange: true,\n                notifyStockUpdate: true,\n                enableCaching: true,\n                cacheExpiry: 3600,\n                enableCompression: true,\n                lazyLoadImages: true\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إعدادات المنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"تخصيص إعدادات إدارة المنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: resetToDefaults,\n                                children: \"إعادة تعيين\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSave,\n                                disabled: isSaving,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    isSaving ? \"جاري الحفظ...\" : \"حفظ الإعدادات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الإعدادات العامة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الفئة الافتراضية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.defaultCategory,\n                                                onChange: (e)=>handleInputChange(\"defaultCategory\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات اليومية\",\n                                                        children: \"العدسات اليومية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات الشهرية\",\n                                                        children: \"العدسات الشهرية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات الملونة\",\n                                                        children: \"العدسات الملونة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"النظارات الطبية\",\n                                                        children: \"النظارات الطبية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"بادئة رمز المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                value: settings.skuPrefix,\n                                                onChange: (e)=>handleInputChange(\"skuPrefix\", e.target.value),\n                                                placeholder: \"PRD\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoGenerateSku\",\n                                                checked: settings.autoGenerateSku,\n                                                onChange: (e)=>handleInputChange(\"autoGenerateSku\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoGenerateSku\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"توليد رمز المنتج تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"requireImages\",\n                                                checked: settings.requireImages,\n                                                onChange: (e)=>handleInputChange(\"requireImages\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"requireImages\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"إجبار رفع صورة واحدة على الأقل\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الحد الأقصى للصور\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.maxImages,\n                                                onChange: (e)=>handleInputChange(\"maxImages\", parseInt(e.target.value)),\n                                                min: \"1\",\n                                                max: \"20\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأسعار\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"العملة الافتراضية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.defaultCurrency,\n                                                onChange: (e)=>handleInputChange(\"defaultCurrency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"IQD\",\n                                                    children: \"دينار عراقي (IQD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"الدينار العراقي هو العملة الأساسية للمتجر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"عملة الاستيراد (للمدير فقط)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.importCurrency,\n                                                onChange: (e)=>handleInputChange(\"importCurrency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"دولار أمريكي (USD)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"IQD\",\n                                                        children: \"دينار عراقي (IQD)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"العملة المستخدمة عند استيراد المنتجات من الخارج\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoConvertPrices\",\n                                                checked: settings.autoConvertPrices,\n                                                onChange: (e)=>handleInputChange(\"autoConvertPrices\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoConvertPrices\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحويل الأسعار تلقائياً إلى الدينار العراقي\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"معدل الضريبة (%)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.taxRate,\n                                                onChange: (e)=>handleInputChange(\"taxRate\", parseFloat(e.target.value)),\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"0.1\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"العراق لا يطبق ضريبة على المنتجات الطبية عادة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"includeTaxInPrice\",\n                                                checked: settings.includeTaxInPrice,\n                                                onChange: (e)=>handleInputChange(\"includeTaxInPrice\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"includeTaxInPrice\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تضمين الضريبة في السعر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"حد تنبيه المخزون المنخفض\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.lowStockThreshold,\n                                                onChange: (e)=>handleInputChange(\"lowStockThreshold\", parseInt(e.target.value)),\n                                                min: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"allowNegativeStock\",\n                                                checked: settings.allowNegativeStock,\n                                                onChange: (e)=>handleInputChange(\"allowNegativeStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"allowNegativeStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"السماح بالمخزون السالب\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الصور\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"جودة الصور\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.imageQuality,\n                                                onChange: (e)=>handleInputChange(\"imageQuality\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"low\",\n                                                        children: \"منخفضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"medium\",\n                                                        children: \"متوسطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"high\",\n                                                        children: \"عالية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"original\",\n                                                        children: \"أصلية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الحد الأقصى لحجم الصورة (MB)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.maxImageSize,\n                                                onChange: (e)=>handleInputChange(\"maxImageSize\", parseInt(e.target.value)),\n                                                min: \"1\",\n                                                max: \"50\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoResize\",\n                                                checked: settings.autoResize,\n                                                onChange: (e)=>handleInputChange(\"autoResize\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoResize\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تغيير حجم الصور تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"watermark\",\n                                                checked: settings.watermark,\n                                                onChange: (e)=>handleInputChange(\"watermark\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"watermark\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"إضافة علامة مائية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات المخزون\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"trackInventory\",\n                                                checked: settings.trackInventory,\n                                                onChange: (e)=>handleInputChange(\"trackInventory\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"trackInventory\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تتبع المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoUpdateStock\",\n                                                checked: settings.autoUpdateStock,\n                                                onChange: (e)=>handleInputChange(\"autoUpdateStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoUpdateStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحديث المخزون تلقائياً عند البيع\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"stockAlerts\",\n                                                checked: settings.stockAlerts,\n                                                onChange: (e)=>handleInputChange(\"stockAlerts\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"stockAlerts\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيهات المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"reserveStock\",\n                                                checked: settings.reserveStock,\n                                                onChange: (e)=>handleInputChange(\"reserveStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"reserveStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"حجز المخزون عند الطلب\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"backorderEnabled\",\n                                                checked: settings.backorderEnabled,\n                                                onChange: (e)=>handleInputChange(\"backorderEnabled\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"backorderEnabled\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"السماح بالطلب المسبق\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات SEO\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoGenerateSlug\",\n                                                checked: settings.autoGenerateSlug,\n                                                onChange: (e)=>handleInputChange(\"autoGenerateSlug\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoGenerateSlug\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"توليد الرابط تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تنسيق الرابط\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.slugFormat,\n                                                onChange: (e)=>handleInputChange(\"slugFormat\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: \"الاسم فقط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name-sku\",\n                                                        children: \"الاسم + رمز المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sku-name\",\n                                                        children: \"رمز المنتج + الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"category-name\",\n                                                        children: \"الفئة + الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تنسيق عنوان الصفحة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                value: settings.metaTitleFormat,\n                                                onChange: (e)=>handleInputChange(\"metaTitleFormat\", e.target.value),\n                                                placeholder: \"{name} - {brand}\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"طول وصف الصفحة (حرف)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.metaDescriptionLength,\n                                                onChange: (e)=>handleInputChange(\"metaDescriptionLength\", parseInt(e.target.value)),\n                                                min: \"50\",\n                                                max: \"300\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأمان\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"requireApproval\",\n                                                checked: settings.requireApproval,\n                                                onChange: (e)=>handleInputChange(\"requireApproval\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"requireApproval\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"مطالبة بالموافقة على المنتجات الجديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"auditChanges\",\n                                                checked: settings.auditChanges,\n                                                onChange: (e)=>handleInputChange(\"auditChanges\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"auditChanges\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تسجيل جميع التغييرات\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تكرار النسخ الاحتياطي\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.backupFrequency,\n                                                onChange: (e)=>handleInputChange(\"backupFrequency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"hourly\",\n                                                        children: \"كل ساعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"daily\",\n                                                        children: \"يومياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"weekly\",\n                                                        children: \"أسبوعياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"monthly\",\n                                                        children: \"شهرياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"مدة الاحتفاظ بالبيانات (يوم)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.dataRetention,\n                                                onChange: (e)=>handleInputChange(\"dataRetention\", parseInt(e.target.value)),\n                                                min: \"30\",\n                                                max: \"3650\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الإشعارات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyLowStock\",\n                                                checked: settings.notifyLowStock,\n                                                onChange: (e)=>handleInputChange(\"notifyLowStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyLowStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه المخزون المنخفض\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyNewProduct\",\n                                                checked: settings.notifyNewProduct,\n                                                onChange: (e)=>handleInputChange(\"notifyNewProduct\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyNewProduct\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه المنتج الجديد\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyPriceChange\",\n                                                checked: settings.notifyPriceChange,\n                                                onChange: (e)=>handleInputChange(\"notifyPriceChange\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyPriceChange\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه تغيير السعر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyStockUpdate\",\n                                                checked: settings.notifyStockUpdate,\n                                                onChange: (e)=>handleInputChange(\"notifyStockUpdate\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyStockUpdate\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه تحديث المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأداء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"enableCaching\",\n                                                checked: settings.enableCaching,\n                                                onChange: (e)=>handleInputChange(\"enableCaching\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"enableCaching\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تفعيل التخزين المؤقت\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"مدة انتهاء التخزين المؤقت (ثانية)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.cacheExpiry,\n                                                onChange: (e)=>handleInputChange(\"cacheExpiry\", parseInt(e.target.value)),\n                                                min: \"60\",\n                                                max: \"86400\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"enableCompression\",\n                                                checked: settings.enableCompression,\n                                                onChange: (e)=>handleInputChange(\"enableCompression\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"enableCompression\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تفعيل ضغط البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"lazyLoadImages\",\n                                                checked: settings.lazyLoadImages,\n                                                onChange: (e)=>handleInputChange(\"lazyLoadImages\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"lazyLoadImages\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحميل الصور عند الحاجة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"border-blue-200 bg-blue-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"ملاحظة:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                            lineNumber: 716,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-blue-700\",\n                            children: \"بعض الإعدادات قد تحتاج إلى إعادة تشغيل النظام لتصبح فعالة. تأكد من حفظ الإعدادات قبل إجراء أي تغييرات أخرى.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                    lineNumber: 715,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 714,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductSettingsPage, \"5q6rrRhdDnWBQKEHtXokUgAcuxw=\");\n_c = ProductSettingsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductSettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/settings/page.tsx\n"));

/***/ })

});