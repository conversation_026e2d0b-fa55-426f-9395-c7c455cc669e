"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/categories/[slug]/page",{

/***/ "(app-pages-browser)/./src/app/categories/[slug]/page.tsx":
/*!********************************************!*\
  !*** ./src/app/categories/[slug]/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_layout_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/footer */ \"(app-pages-browser)/./src/components/layout/footer.tsx\");\n/* harmony import */ var _components_product_product_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/product/product-card */ \"(app-pages-browser)/./src/components/product/product-card.tsx\");\n/* harmony import */ var _lib_products_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/products-store */ \"(app-pages-browser)/./src/lib/products-store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// استيراد المنتجات من النظام المركزي\n\n// بيانات الفئات (يمكن نقلها لاحقاً إلى قاعدة البيانات)\nconst categoryData = {\n    \"daily-lenses\": {\n        name: \"العدسات اليومية\",\n        description: \"عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي. تتميز بسهولة الاستخدام والنظافة المضمونة.\",\n        image: \"https://picsum.photos/1200/400?random=20\",\n        benefits: [\n            \"راحة طوال اليوم\",\n            \"سهولة الاستخدام\",\n            \"نظافة مضمونة\",\n            \"لا تحتاج تنظيف\",\n            \"مناسبة للمبتدئين\",\n            \"تقليل خطر العدوى\"\n        ]\n    },\n    \"monthly-lenses\": {\n        name: \"العدسات الشهرية\",\n        description: \"عدسات لاصقة شهرية اقتصادية وعملية توفر راحة طويلة المدى مع جودة عالية.\",\n        image: \"https://picsum.photos/1200/400?random=21\",\n        benefits: [\n            \"اقتصادية\",\n            \"جودة عالية\",\n            \"مقاومة للترسبات\",\n            \"راحة طويلة المدى\",\n            \"سهولة العناية\",\n            \"متانة عالية\"\n        ]\n    },\n    \"colored-lenses\": {\n        name: \"العدسات الملونة\",\n        description: \"عدسات ملونة لإطلالة مميزة وجذابة مع ألوان طبيعية وتغطية ممتازة.\",\n        image: \"https://picsum.photos/1200/400?random=22\",\n        benefits: [\n            \"ألوان طبيعية\",\n            \"تغطية ممتازة\",\n            \"آمنة ومريحة\",\n            \"تصاميم متنوعة\",\n            \"مناسبة للمناسبات\",\n            \"جودة الألوان\"\n        ]\n    },\n    \"glasses\": {\n        name: \"النظارات الطبية\",\n        description: \"نظارات طبية عالية الجودة من أفضل العلامات التجارية العالمية.\",\n        image: \"https://picsum.photos/1200/400?random=23\",\n        benefits: [\n            \"جودة عالية\",\n            \"تصاميم عصرية\",\n            \"راحة في الارتداء\",\n            \"مقاومة للخدش\",\n            \"ضمان شامل\",\n            \"خدمة ما بعد البيع\"\n        ]\n    }\n};\nconst priceRanges = [\n    {\n        label: \"الكل\",\n        min: 0,\n        max: Infinity\n    },\n    {\n        label: \"أقل من 100,000 د.ع\",\n        min: 0,\n        max: 100000\n    },\n    {\n        label: \"100,000 - 200,000 د.ع\",\n        min: 100000,\n        max: 200000\n    },\n    {\n        label: \"أكثر من 200,000 د.ع\",\n        min: 200000,\n        max: Infinity\n    }\n];\nfunction CategoryPage(param) {\n    let { params } = param;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedBrand, setSelectedBrand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الكل\");\n    const [selectedPriceRange, setSelectedPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(priceRanges[0]);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الأحدث\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // فك تشفير params للتوافق مع Next.js 15\n    const resolvedParams = react__WEBPACK_IMPORTED_MODULE_1___default().use(params);\n    const category = categoryData[resolvedParams.slug];\n    // الحصول على المنتجات من النظام المركزي وفلترتها حسب الفئة\n    const allProducts = _lib_products_store__WEBPACK_IMPORTED_MODULE_10__.ProductsStore.getAll();\n    const categoryProducts = allProducts.filter((product)=>{\n        // تطابق الفئة مع slug\n        const categorySlugMap = {\n            \"daily-lenses\": \"العدسات اليومية\",\n            \"monthly-lenses\": \"العدسات الشهرية\",\n            \"colored-lenses\": \"العدسات الملونة\",\n            \"glasses\": \"النظارات الطبية\"\n        };\n        return product.category === categorySlugMap[resolvedParams.slug];\n    });\n    const brands = [\n        \"الكل\",\n        ...Array.from(new Set(categoryProducts.map((p)=>p.brand)))\n    ];\n    if (!category) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 py-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold mb-4\",\n                            children: \"الفئة غير موجودة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8\",\n                            children: \"عذراً، الفئة التي تبحث عنها غير متوفرة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/categories\",\n                                children: \"العودة للفئات\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this);\n    }\n    const filteredProducts = categoryProducts.filter((product)=>{\n        const matchesSearch = product.nameAr.toLowerCase().includes(searchTerm.toLowerCase()) || product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.brand.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesBrand = selectedBrand === \"الكل\" || product.brand === selectedBrand;\n        const matchesPrice = product.price >= selectedPriceRange.min && product.price <= selectedPriceRange.max;\n        return matchesSearch && matchesBrand && matchesPrice;\n    });\n    const sortedProducts = [\n        ...filteredProducts\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case \"السعر: من الأقل للأعلى\":\n                return a.price - b.price;\n            case \"السعر: من الأعلى للأقل\":\n                return b.price - a.price;\n            case \"الأعلى تقييماً\":\n                return (b.rating || 0) - (a.rating || 0);\n            case \"الأكثر مبيعاً\":\n                return (b.reviewCount || 0) - (a.reviewCount || 0);\n            default:\n                return 0;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"hover:text-primary\",\n                                    children: \"الرئيسية\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/categories\",\n                                    className: \"hover:text-primary\",\n                                    children: \"الفئات\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-900\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative h-80 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: category.image,\n                                alt: category.name,\n                                fill: true,\n                                className: \"object-cover\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black/50\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative container mx-auto px-4 h-full flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white max-w-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl opacity-90\",\n                                            children: category.description\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-12 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-center mb-8\",\n                                    children: [\n                                        \"مميزات \",\n                                        category.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: category.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 bg-white p-4 rounded-lg shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-primary rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: benefit\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"container mx-auto px-4 py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    placeholder: \"ابحث في المنتجات...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"فلاتر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setViewMode(viewMode === \"grid\" ? \"list\" : \"grid\"),\n                                                        children: viewMode === \"grid\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 42\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 73\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this),\n                                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: selectedBrand,\n                                                                onChange: (e)=>setSelectedBrand(e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: brand,\n                                                                        children: brand\n                                                                    }, brand, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"نطاق السعر\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: selectedPriceRange.label,\n                                                                onChange: (e)=>{\n                                                                    const range = priceRanges.find((r)=>r.label === e.target.value);\n                                                                    if (range) setSelectedPriceRange(range);\n                                                                },\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: priceRanges.map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: range.label,\n                                                                        children: range.label\n                                                                    }, range.label, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"ترتيب حسب\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: sortBy,\n                                                                onChange: (e)=>setSortBy(e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأحدث\",\n                                                                        children: \"الأحدث\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"السعر: من الأقل للأعلى\",\n                                                                        children: \"السعر: من الأقل للأعلى\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"السعر: من الأعلى للأقل\",\n                                                                        children: \"السعر: من الأعلى للأقل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأعلى تقييماً\",\n                                                                        children: \"الأعلى تقييماً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأكثر مبيعاً\",\n                                                                        children: \"الأكثر مبيعاً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"عرض \",\n                                                sortedProducts.length,\n                                                \" من \",\n                                                categoryProducts.length,\n                                                \" منتج\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            sortedProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-6 \".concat(viewMode === \"grid\" ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\" : \"grid-cols-1\"),\n                                children: sortedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_product_card__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        product: product\n                                    }, product.id, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-lg\",\n                                        children: \"لا توجد منتجات تطابق البحث\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"mt-4\",\n                                        onClick: ()=>{\n                                            setSearchTerm(\"\");\n                                            setSelectedBrand(\"الكل\");\n                                            setSelectedPriceRange(priceRanges[0]);\n                                        },\n                                        children: \"إعادة تعيين الفلاتر\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoryPage, \"Mj6KKTNG1PfwUpjbb8iP4f1P8Js=\");\n_c = CategoryPage;\nvar _c;\n$RefreshReg$(_c, \"CategoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/categories/[slug]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/products-store.ts":
/*!***********************************!*\
  !*** ./src/lib/products-store.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductsStore: () => (/* binding */ ProductsStore)\n/* harmony export */ });\n// نظام إدارة حالة المنتجات المركزي\n// قائمة المنتجات - تبدأ فارغة للعمل الجدي\nlet products = [];\n// دوال إدارة المنتجات\nconst ProductsStore = {\n    // الحصول على جميع المنتجات\n    getAll: ()=>{\n        return [\n            ...products\n        ];\n    },\n    // الحصول على منتج بالمعرف\n    getById: (id)=>{\n        return products.find((p)=>p.id === id);\n    },\n    // الحصول على منتج بالـ SKU\n    getBySku: (sku)=>{\n        return products.find((p)=>p.sku === sku);\n    },\n    // إضافة منتج جديد\n    add: (productData)=>{\n        const newProduct = {\n            ...productData,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        products.push(newProduct);\n        return newProduct;\n    },\n    // تحديث منتج\n    update: (id, updates)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        products[index] = {\n            ...products[index],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        return products[index];\n    },\n    // حذف منتج\n    delete: (id)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        products.splice(index, 1);\n        return true;\n    },\n    // حذف منتجات متعددة\n    deleteMultiple: (ids)=>{\n        let deletedCount = 0;\n        ids.forEach((id)=>{\n            if (ProductsStore.delete(id)) {\n                deletedCount++;\n            }\n        });\n        return deletedCount;\n    },\n    // البحث في المنتجات\n    search: (query)=>{\n        const lowerQuery = query.toLowerCase();\n        return products.filter((product)=>product.name.toLowerCase().includes(lowerQuery) || product.nameEn.toLowerCase().includes(lowerQuery) || product.sku.toLowerCase().includes(lowerQuery) || product.brand.toLowerCase().includes(lowerQuery) || product.category.toLowerCase().includes(lowerQuery));\n    },\n    // فلترة المنتجات\n    filter: (filters)=>{\n        return products.filter((product)=>{\n            if (filters.category && filters.category !== \"الكل\" && product.category !== filters.category) {\n                return false;\n            }\n            if (filters.brand && filters.brand !== \"الكل\" && product.brand !== filters.brand) {\n                return false;\n            }\n            if (filters.status && filters.status !== \"الكل\" && product.status !== filters.status) {\n                return false;\n            }\n            if (filters.inStock !== undefined) {\n                const hasStock = product.stock > 0;\n                if (filters.inStock !== hasStock) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    },\n    // الحصول على الإحصائيات\n    getStats: ()=>{\n        const total = products.length;\n        const active = products.filter((p)=>p.status === \"active\").length;\n        const draft = products.filter((p)=>p.status === \"draft\").length;\n        const outOfStock = products.filter((p)=>p.stock === 0).length;\n        const totalStock = products.reduce((sum, p)=>sum + p.stock, 0);\n        const lowStock = products.filter((p)=>p.stock > 0 && p.stock <= 10).length;\n        return {\n            total,\n            active,\n            draft,\n            outOfStock,\n            totalStock,\n            lowStock\n        };\n    },\n    // توليد SKU تلقائي\n    generateSku: function() {\n        let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"PRD\";\n        let counter = 1;\n        let sku;\n        do {\n            sku = \"\".concat(prefix, \"-\").concat(counter.toString().padStart(3, '0'));\n            counter++;\n        }while (ProductsStore.getBySku(sku));\n        return sku;\n    },\n    // توليد slug تلقائي\n    generateSlug: (name, sku)=>{\n        let baseSlug = name.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n        if (sku) {\n            baseSlug += \"-\".concat(sku.toLowerCase());\n        }\n        let slug = baseSlug;\n        let counter = 1;\n        while(products.some((p)=>p.slug === slug)){\n            slug = \"\".concat(baseSlug, \"-\").concat(counter);\n            counter++;\n        }\n        return slug;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/products-store.ts\n"));

/***/ })

});