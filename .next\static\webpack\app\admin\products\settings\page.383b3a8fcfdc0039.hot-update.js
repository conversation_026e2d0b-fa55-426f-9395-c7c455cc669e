"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/settings/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/settings/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/admin/products/settings/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductSettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ProductSettingsPage() {\n    _s();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // إعدادات عامة\n        defaultCategory: \"العدسات اليومية\",\n        defaultBrand: \"\",\n        autoGenerateSku: true,\n        skuPrefix: \"PRD\",\n        requireImages: true,\n        maxImages: \"10\",\n        // إعدادات الأسعار\n        defaultCurrency: \"IQD\",\n        importCurrency: \"USD\",\n        autoConvertPrices: true,\n        taxRate: \"0\",\n        includeTaxInPrice: false,\n        allowNegativeStock: false,\n        lowStockThreshold: \"10\",\n        showPriceInMultipleCurrencies: false,\n        // إعدادات الصور\n        imageQuality: \"high\",\n        maxImageSize: \"5\",\n        allowedFormats: [\n            \"jpg\",\n            \"jpeg\",\n            \"png\",\n            \"webp\"\n        ],\n        autoResize: true,\n        watermark: false,\n        // إعدادات المخزون\n        trackInventory: true,\n        autoUpdateStock: true,\n        stockAlerts: true,\n        reserveStock: true,\n        backorderEnabled: false,\n        // إعدادات SEO\n        autoGenerateSlug: true,\n        slugFormat: \"name-sku\",\n        metaTitleFormat: \"{name} - {brand}\",\n        metaDescriptionLength: 160,\n        // إعدادات الأمان\n        requireApproval: false,\n        auditChanges: true,\n        backupFrequency: \"daily\",\n        dataRetention: 365,\n        // إعدادات الإشعارات\n        notifyLowStock: true,\n        notifyNewProduct: true,\n        notifyPriceChange: true,\n        notifyStockUpdate: true,\n        // إعدادات الأداء\n        enableCaching: true,\n        cacheExpiry: 3600,\n        enableCompression: true,\n        lazyLoadImages: true\n    });\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            // محاكاة حفظ الإعدادات\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            console.log(\"Saving settings:\", settings);\n            alert(\"تم حفظ الإعدادات بنجاح!\");\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"حدث خطأ أثناء حفظ الإعدادات\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const resetToDefaults = ()=>{\n        if (confirm(\"هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\")) {\n            // إعادة تعيين الإعدادات\n            setSettings({\n                defaultCategory: \"العدسات اليومية\",\n                defaultBrand: \"\",\n                autoGenerateSku: true,\n                skuPrefix: \"PRD\",\n                requireImages: true,\n                maxImages: 10,\n                defaultCurrency: \"SAR\",\n                taxRate: 15,\n                includeTaxInPrice: true,\n                allowNegativeStock: false,\n                lowStockThreshold: 10,\n                imageQuality: \"high\",\n                maxImageSize: 5,\n                allowedFormats: [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"webp\"\n                ],\n                autoResize: true,\n                watermark: false,\n                trackInventory: true,\n                autoUpdateStock: true,\n                stockAlerts: true,\n                reserveStock: true,\n                backorderEnabled: false,\n                autoGenerateSlug: true,\n                slugFormat: \"name-sku\",\n                metaTitleFormat: \"{name} - {brand}\",\n                metaDescriptionLength: 160,\n                requireApproval: false,\n                auditChanges: true,\n                backupFrequency: \"daily\",\n                dataRetention: 365,\n                notifyLowStock: true,\n                notifyNewProduct: true,\n                notifyPriceChange: true,\n                notifyStockUpdate: true,\n                enableCaching: true,\n                cacheExpiry: 3600,\n                enableCompression: true,\n                lazyLoadImages: true\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إعدادات المنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"تخصيص إعدادات إدارة المنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: resetToDefaults,\n                                children: \"إعادة تعيين\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSave,\n                                disabled: isSaving,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    isSaving ? \"جاري الحفظ...\" : \"حفظ الإعدادات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الإعدادات العامة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الفئة الافتراضية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.defaultCategory,\n                                                onChange: (e)=>handleInputChange(\"defaultCategory\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات اليومية\",\n                                                        children: \"العدسات اليومية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات الشهرية\",\n                                                        children: \"العدسات الشهرية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات الملونة\",\n                                                        children: \"العدسات الملونة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"النظارات الطبية\",\n                                                        children: \"النظارات الطبية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"بادئة رمز المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                value: settings.skuPrefix,\n                                                onChange: (e)=>handleInputChange(\"skuPrefix\", e.target.value),\n                                                placeholder: \"PRD\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoGenerateSku\",\n                                                checked: settings.autoGenerateSku,\n                                                onChange: (e)=>handleInputChange(\"autoGenerateSku\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoGenerateSku\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"توليد رمز المنتج تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"requireImages\",\n                                                checked: settings.requireImages,\n                                                onChange: (e)=>handleInputChange(\"requireImages\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"requireImages\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"إجبار رفع صورة واحدة على الأقل\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الحد الأقصى للصور\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.maxImages,\n                                                onChange: (e)=>handleInputChange(\"maxImages\", parseInt(e.target.value)),\n                                                min: \"1\",\n                                                max: \"20\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأسعار\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"العملة الافتراضية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.defaultCurrency,\n                                                onChange: (e)=>handleInputChange(\"defaultCurrency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"IQD\",\n                                                    children: \"دينار عراقي (IQD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"الدينار العراقي هو العملة الأساسية للمتجر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"عملة الاستيراد (للمدير فقط)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.importCurrency,\n                                                onChange: (e)=>handleInputChange(\"importCurrency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"دولار أمريكي (USD)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"IQD\",\n                                                        children: \"دينار عراقي (IQD)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"العملة المستخدمة عند استيراد المنتجات من الخارج\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoConvertPrices\",\n                                                checked: settings.autoConvertPrices,\n                                                onChange: (e)=>handleInputChange(\"autoConvertPrices\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoConvertPrices\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحويل الأسعار تلقائياً إلى الدينار العراقي\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"معدل الضريبة (%)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.taxRate,\n                                                onChange: (e)=>handleInputChange(\"taxRate\", parseFloat(e.target.value)),\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"0.1\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"العراق لا يطبق ضريبة على المنتجات الطبية عادة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"includeTaxInPrice\",\n                                                checked: settings.includeTaxInPrice,\n                                                onChange: (e)=>handleInputChange(\"includeTaxInPrice\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"includeTaxInPrice\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تضمين الضريبة في السعر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"حد تنبيه المخزون المنخفض\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.lowStockThreshold,\n                                                onChange: (e)=>handleInputChange(\"lowStockThreshold\", parseInt(e.target.value)),\n                                                min: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"allowNegativeStock\",\n                                                checked: settings.allowNegativeStock,\n                                                onChange: (e)=>handleInputChange(\"allowNegativeStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"allowNegativeStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"السماح بالمخزون السالب\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الصور\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"جودة الصور\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.imageQuality,\n                                                onChange: (e)=>handleInputChange(\"imageQuality\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"low\",\n                                                        children: \"منخفضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"medium\",\n                                                        children: \"متوسطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"high\",\n                                                        children: \"عالية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"original\",\n                                                        children: \"أصلية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الحد الأقصى لحجم الصورة (MB)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.maxImageSize,\n                                                onChange: (e)=>handleInputChange(\"maxImageSize\", parseInt(e.target.value)),\n                                                min: \"1\",\n                                                max: \"50\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoResize\",\n                                                checked: settings.autoResize,\n                                                onChange: (e)=>handleInputChange(\"autoResize\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoResize\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تغيير حجم الصور تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"watermark\",\n                                                checked: settings.watermark,\n                                                onChange: (e)=>handleInputChange(\"watermark\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"watermark\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"إضافة علامة مائية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات المخزون\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"trackInventory\",\n                                                checked: settings.trackInventory,\n                                                onChange: (e)=>handleInputChange(\"trackInventory\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"trackInventory\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تتبع المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoUpdateStock\",\n                                                checked: settings.autoUpdateStock,\n                                                onChange: (e)=>handleInputChange(\"autoUpdateStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoUpdateStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحديث المخزون تلقائياً عند البيع\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"stockAlerts\",\n                                                checked: settings.stockAlerts,\n                                                onChange: (e)=>handleInputChange(\"stockAlerts\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"stockAlerts\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيهات المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"reserveStock\",\n                                                checked: settings.reserveStock,\n                                                onChange: (e)=>handleInputChange(\"reserveStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"reserveStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"حجز المخزون عند الطلب\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"backorderEnabled\",\n                                                checked: settings.backorderEnabled,\n                                                onChange: (e)=>handleInputChange(\"backorderEnabled\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"backorderEnabled\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"السماح بالطلب المسبق\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات SEO\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoGenerateSlug\",\n                                                checked: settings.autoGenerateSlug,\n                                                onChange: (e)=>handleInputChange(\"autoGenerateSlug\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoGenerateSlug\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"توليد الرابط تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تنسيق الرابط\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.slugFormat,\n                                                onChange: (e)=>handleInputChange(\"slugFormat\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: \"الاسم فقط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name-sku\",\n                                                        children: \"الاسم + رمز المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sku-name\",\n                                                        children: \"رمز المنتج + الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"category-name\",\n                                                        children: \"الفئة + الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تنسيق عنوان الصفحة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                value: settings.metaTitleFormat,\n                                                onChange: (e)=>handleInputChange(\"metaTitleFormat\", e.target.value),\n                                                placeholder: \"{name} - {brand}\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"طول وصف الصفحة (حرف)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.metaDescriptionLength,\n                                                onChange: (e)=>handleInputChange(\"metaDescriptionLength\", parseInt(e.target.value)),\n                                                min: \"50\",\n                                                max: \"300\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأمان\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"requireApproval\",\n                                                checked: settings.requireApproval,\n                                                onChange: (e)=>handleInputChange(\"requireApproval\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"requireApproval\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"مطالبة بالموافقة على المنتجات الجديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"auditChanges\",\n                                                checked: settings.auditChanges,\n                                                onChange: (e)=>handleInputChange(\"auditChanges\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"auditChanges\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تسجيل جميع التغييرات\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تكرار النسخ الاحتياطي\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.backupFrequency,\n                                                onChange: (e)=>handleInputChange(\"backupFrequency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"hourly\",\n                                                        children: \"كل ساعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"daily\",\n                                                        children: \"يومياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"weekly\",\n                                                        children: \"أسبوعياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"monthly\",\n                                                        children: \"شهرياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"مدة الاحتفاظ بالبيانات (يوم)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.dataRetention,\n                                                onChange: (e)=>handleInputChange(\"dataRetention\", parseInt(e.target.value)),\n                                                min: \"30\",\n                                                max: \"3650\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الإشعارات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyLowStock\",\n                                                checked: settings.notifyLowStock,\n                                                onChange: (e)=>handleInputChange(\"notifyLowStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyLowStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه المخزون المنخفض\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyNewProduct\",\n                                                checked: settings.notifyNewProduct,\n                                                onChange: (e)=>handleInputChange(\"notifyNewProduct\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyNewProduct\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه المنتج الجديد\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyPriceChange\",\n                                                checked: settings.notifyPriceChange,\n                                                onChange: (e)=>handleInputChange(\"notifyPriceChange\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyPriceChange\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه تغيير السعر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyStockUpdate\",\n                                                checked: settings.notifyStockUpdate,\n                                                onChange: (e)=>handleInputChange(\"notifyStockUpdate\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyStockUpdate\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه تحديث المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 596,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأداء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"enableCaching\",\n                                                checked: settings.enableCaching,\n                                                onChange: (e)=>handleInputChange(\"enableCaching\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"enableCaching\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تفعيل التخزين المؤقت\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"مدة انتهاء التخزين المؤقت (ثانية)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.cacheExpiry,\n                                                onChange: (e)=>handleInputChange(\"cacheExpiry\", parseInt(e.target.value)),\n                                                min: \"60\",\n                                                max: \"86400\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"enableCompression\",\n                                                checked: settings.enableCompression,\n                                                onChange: (e)=>handleInputChange(\"enableCompression\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"enableCompression\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تفعيل ضغط البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"lazyLoadImages\",\n                                                checked: settings.lazyLoadImages,\n                                                onChange: (e)=>handleInputChange(\"lazyLoadImages\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"lazyLoadImages\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحميل الصور عند الحاجة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"border-blue-200 bg-blue-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"ملاحظة:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                            lineNumber: 716,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-blue-700\",\n                            children: \"بعض الإعدادات قد تحتاج إلى إعادة تشغيل النظام لتصبح فعالة. تأكد من حفظ الإعدادات قبل إجراء أي تغييرات أخرى.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                    lineNumber: 715,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 714,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductSettingsPage, \"GWGexQNc6VBIodPng2IYhMEgQ2U=\");\n_c = ProductSettingsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductSettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/settings/page.tsx\n"));

/***/ })

});