"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/categories/[slug]/page",{

/***/ "(app-pages-browser)/./src/app/categories/[slug]/page.tsx":
/*!********************************************!*\
  !*** ./src/app/categories/[slug]/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Grid,List,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_layout_footer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/footer */ \"(app-pages-browser)/./src/components/layout/footer.tsx\");\n/* harmony import */ var _components_product_product_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/product/product-card */ \"(app-pages-browser)/./src/components/product/product-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// بيانات الفئات (يمكن نقلها لاحقاً إلى قاعدة البيانات)\nconst categoryData = {\n    \"daily-lenses\": {\n        name: \"العدسات اليومية\",\n        description: \"عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي. تتميز بسهولة الاستخدام والنظافة المضمونة.\",\n        image: \"https://picsum.photos/1200/400?random=20\",\n        benefits: [\n            \"راحة طوال اليوم\",\n            \"سهولة الاستخدام\",\n            \"نظافة مضمونة\",\n            \"لا تحتاج تنظيف\",\n            \"مناسبة للمبتدئين\",\n            \"تقليل خطر العدوى\"\n        ]\n    },\n    \"monthly-lenses\": {\n        name: \"العدسات الشهرية\",\n        description: \"عدسات لاصقة شهرية اقتصادية وعملية توفر راحة طويلة المدى مع جودة عالية.\",\n        image: \"https://picsum.photos/1200/400?random=21\",\n        benefits: [\n            \"اقتصادية\",\n            \"جودة عالية\",\n            \"مقاومة للترسبات\",\n            \"راحة طويلة المدى\",\n            \"سهولة العناية\",\n            \"متانة عالية\"\n        ]\n    },\n    \"colored-lenses\": {\n        name: \"العدسات الملونة\",\n        description: \"عدسات ملونة لإطلالة مميزة وجذابة مع ألوان طبيعية وتغطية ممتازة.\",\n        image: \"https://picsum.photos/1200/400?random=22\",\n        benefits: [\n            \"ألوان طبيعية\",\n            \"تغطية ممتازة\",\n            \"آمنة ومريحة\",\n            \"تصاميم متنوعة\",\n            \"مناسبة للمناسبات\",\n            \"جودة الألوان\"\n        ]\n    },\n    \"glasses\": {\n        name: \"النظارات الطبية\",\n        description: \"نظارات طبية عالية الجودة من أفضل العلامات التجارية العالمية.\",\n        image: \"https://picsum.photos/1200/400?random=23\",\n        benefits: [\n            \"جودة عالية\",\n            \"تصاميم عصرية\",\n            \"راحة في الارتداء\",\n            \"مقاومة للخدش\",\n            \"ضمان شامل\",\n            \"خدمة ما بعد البيع\"\n        ]\n    }\n};\n// منتجات وهمية للفئة\nconst categoryProducts = [\n    {\n        id: \"1\",\n        name: \"Acuvue Oasys Daily\",\n        nameAr: \"عدسات أكيوفيو أوازيس اليومية\",\n        slug: \"acuvue-oasys-daily\",\n        price: 120,\n        comparePrice: 150,\n        image: \"https://picsum.photos/400/400?random=1\",\n        brand: \"Johnson & Johnson\",\n        rating: 4.8,\n        reviewCount: 124,\n        isNew: true,\n        isFeatured: true,\n        inStock: true\n    },\n    {\n        id: \"4\",\n        name: \"Dailies Total 1\",\n        nameAr: \"عدسات ديليز توتال ون\",\n        slug: \"dailies-total-1\",\n        price: 140,\n        comparePrice: 160,\n        image: \"https://picsum.photos/400/400?random=4\",\n        brand: \"Alcon\",\n        rating: 4.9,\n        reviewCount: 203,\n        isNew: true,\n        inStock: true\n    },\n    {\n        id: \"9\",\n        name: \"Acuvue Moist Daily\",\n        nameAr: \"عدسات أكيوفيو مويست اليومية\",\n        slug: \"acuvue-moist-daily\",\n        price: 95,\n        comparePrice: 110,\n        image: \"https://picsum.photos/400/400?random=9\",\n        brand: \"Johnson & Johnson\",\n        rating: 4.6,\n        reviewCount: 156,\n        inStock: true\n    },\n    {\n        id: \"10\",\n        name: \"Biotrue ONEday\",\n        nameAr: \"عدسات بايوترو ون داي\",\n        slug: \"biotrue-oneday\",\n        price: 105,\n        image: \"https://picsum.photos/400/400?random=10\",\n        brand: \"Bausch & Lomb\",\n        rating: 4.5,\n        reviewCount: 89,\n        inStock: true\n    },\n    {\n        id: \"11\",\n        name: \"Clariti 1 day\",\n        nameAr: \"عدسات كلاريتي ون داي\",\n        slug: \"clariti-1-day\",\n        price: 85,\n        comparePrice: 100,\n        image: \"https://picsum.photos/400/400?random=11\",\n        brand: \"CooperVision\",\n        rating: 4.4,\n        reviewCount: 67,\n        inStock: true\n    },\n    {\n        id: \"12\",\n        name: \"MyDay Daily\",\n        nameAr: \"عدسات ماي داي اليومية\",\n        slug: \"myday-daily\",\n        price: 115,\n        image: \"https://picsum.photos/400/400?random=12\",\n        brand: \"CooperVision\",\n        rating: 4.7,\n        reviewCount: 134,\n        inStock: false\n    }\n];\nconst brands = [\n    \"الكل\",\n    ...Array.from(new Set(categoryProducts.map((p)=>p.brand)))\n];\nconst priceRanges = [\n    {\n        label: \"الكل\",\n        min: 0,\n        max: Infinity\n    },\n    {\n        label: \"أقل من 100 ريال\",\n        min: 0,\n        max: 100\n    },\n    {\n        label: \"100 - 150 ريال\",\n        min: 100,\n        max: 150\n    },\n    {\n        label: \"أكثر من 150 ريال\",\n        min: 150,\n        max: Infinity\n    }\n];\nfunction CategoryPage(param) {\n    let { params } = param;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedBrand, setSelectedBrand] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الكل\");\n    const [selectedPriceRange, setSelectedPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(priceRanges[0]);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الأحدث\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const category = categoryData[params.slug];\n    if (!category) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"container mx-auto px-4 py-16 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold mb-4\",\n                            children: \"الفئة غير موجودة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8\",\n                            children: \"عذراً، الفئة التي تبحث عنها غير متوفرة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/categories\",\n                                children: \"العودة للفئات\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    const filteredProducts = categoryProducts.filter((product)=>{\n        const matchesSearch = product.nameAr.toLowerCase().includes(searchTerm.toLowerCase()) || product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.brand.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesBrand = selectedBrand === \"الكل\" || product.brand === selectedBrand;\n        const matchesPrice = product.price >= selectedPriceRange.min && product.price <= selectedPriceRange.max;\n        return matchesSearch && matchesBrand && matchesPrice;\n    });\n    const sortedProducts = [\n        ...filteredProducts\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case \"السعر: من الأقل للأعلى\":\n                return a.price - b.price;\n            case \"السعر: من الأعلى للأقل\":\n                return b.price - a.price;\n            case \"الأعلى تقييماً\":\n                return (b.rating || 0) - (a.rating || 0);\n            case \"الأكثر مبيعاً\":\n                return (b.reviewCount || 0) - (a.reviewCount || 0);\n            default:\n                return 0;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex items-center gap-2 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"hover:text-primary\",\n                                    children: \"الرئيسية\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/categories\",\n                                    className: \"hover:text-primary\",\n                                    children: \"الفئات\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-900\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative h-80 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: category.image,\n                                alt: category.name,\n                                fill: true,\n                                className: \"object-cover\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black/50\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative container mx-auto px-4 h-full flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white max-w-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl md:text-5xl font-bold mb-4\",\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl opacity-90\",\n                                            children: category.description\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-12 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-center mb-8\",\n                                    children: [\n                                        \"مميزات \",\n                                        category.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: category.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 bg-white p-4 rounded-lg shadow-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-primary rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: benefit\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"container mx-auto px-4 py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    placeholder: \"ابحث في المنتجات...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"فلاتر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setViewMode(viewMode === \"grid\" ? \"list\" : \"grid\"),\n                                                        children: viewMode === \"grid\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 42\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Grid_List_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 73\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this),\n                                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: selectedBrand,\n                                                                onChange: (e)=>setSelectedBrand(e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: brand,\n                                                                        children: brand\n                                                                    }, brand, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"نطاق السعر\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: selectedPriceRange.label,\n                                                                onChange: (e)=>{\n                                                                    const range = priceRanges.find((r)=>r.label === e.target.value);\n                                                                    if (range) setSelectedPriceRange(range);\n                                                                },\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: priceRanges.map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: range.label,\n                                                                        children: range.label\n                                                                    }, range.label, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"ترتيب حسب\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: sortBy,\n                                                                onChange: (e)=>setSortBy(e.target.value),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأحدث\",\n                                                                        children: \"الأحدث\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"السعر: من الأقل للأعلى\",\n                                                                        children: \"السعر: من الأقل للأعلى\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"السعر: من الأعلى للأقل\",\n                                                                        children: \"السعر: من الأعلى للأقل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأعلى تقييماً\",\n                                                                        children: \"الأعلى تقييماً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأكثر مبيعاً\",\n                                                                        children: \"الأكثر مبيعاً\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"عرض \",\n                                                sortedProducts.length,\n                                                \" من \",\n                                                categoryProducts.length,\n                                                \" منتج\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this),\n                            sortedProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-6 \".concat(viewMode === \"grid\" ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\" : \"grid-cols-1\"),\n                                children: sortedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_product_card__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        product: product\n                                    }, product.id, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-lg\",\n                                        children: \"لا توجد منتجات تطابق البحث\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        className: \"mt-4\",\n                                        onClick: ()=>{\n                                            setSearchTerm(\"\");\n                                            setSelectedBrand(\"الكل\");\n                                            setSelectedPriceRange(priceRanges[0]);\n                                        },\n                                        children: \"إعادة تعيين الفلاتر\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\categories\\\\[slug]\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(CategoryPage, \"Mj6KKTNG1PfwUpjbb8iP4f1P8Js=\");\n_c = CategoryPage;\nvar _c;\n$RefreshReg$(_c, \"CategoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/categories/[slug]/page.tsx\n"));

/***/ })

});