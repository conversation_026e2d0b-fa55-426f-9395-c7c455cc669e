import React from "react";
import {
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  TrendingUp,
  TrendingDown,
  Eye,
  Plus,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ProductsStore } from "@/lib/products-store";
import { formatCurrency } from "@/lib/currency";

export default function AdminDashboard() {
  // الحصول على إحصائيات حقيقية من النظام
  const allProducts = ProductsStore.getAll();
  const totalProducts = allProducts.length;
  const activeProducts = allProducts.filter(p => p.status === "active").length;
  const lowStockProducts = allProducts.filter(p => p.stock <= p.minQuantity).length;

  // إحصائيات ديناميكية
  const stats = [
    {
      title: "إجمالي المنتجات",
      value: totalProducts.toString(),
      change: "0%",
      changeType: "increase" as const,
      icon: Package,
    },
    {
      title: "المنتجات النشطة",
      value: activeProducts.toString(),
      change: "0%",
      changeType: "increase" as const,
      icon: Eye,
    },
    {
      title: "مخزون منخفض",
      value: lowStockProducts.toString(),
      change: "0%",
      changeType: lowStockProducts > 0 ? "decrease" as const : "increase" as const,
      icon: TrendingDown,
    },
    {
      title: "الطلبات",
      value: "0",
      change: "0%",
      changeType: "increase" as const,
      icon: ShoppingCart,
    },
  ];

  // لا توجد طلبات بعد - سيتم ربطها بنظام الطلبات لاحقاً
  const recentOrders: any[] = [];

  // أحدث المنتجات المضافة
  const topProducts = allProducts
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 4)
    .map(product => ({
      name: product.name,
      stock: product.stock,
      price: formatCurrency(product.price, product.priceCurrency),
      status: product.status
    }));

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار السريعة */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="text-gray-600 mt-1">مرحباً بك في لوحة تحكم VisionLens</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/admin/products/new">
              <Plus className="h-4 w-4 mr-2" />
              إضافة منتج
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/orders">
              <Eye className="h-4 w-4 mr-2" />
              عرض الطلبات
            </Link>
          </Button>
        </div>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-gray-600">
                {stat.changeType === "increase" ? (
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                )}
                <span
                  className={
                    stat.changeType === "increase"
                      ? "text-green-500"
                      : "text-red-500"
                  }
                >
                  {stat.change}
                </span>
                <span className="mr-1">من الشهر الماضي</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الطلبات الأخيرة */}
        <Card>
          <CardHeader>
            <CardTitle>الطلبات الأخيرة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders.length > 0 ? (
                recentOrders.map((order) => (
                  <div
                    key={order.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">{order.id}</span>
                        <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-800">
                          {order.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{order.customer}</p>
                      <p className="text-xs text-gray-500">{order.product}</p>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-sm">{order.amount}</p>
                      <p className="text-xs text-gray-500">{order.date}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <ShoppingCart className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>لا توجد طلبات بعد</p>
                  <p className="text-sm mt-1">ستظهر الطلبات الجديدة هنا</p>
                </div>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full" asChild>
                <Link href="/admin/orders">عرض جميع الطلبات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* أحدث المنتجات */}
        <Card>
          <CardHeader>
            <CardTitle>أحدث المنتجات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.length > 0 ? (
                topProducts.map((product, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <p className="font-medium text-sm">{product.name}</p>
                      <p className="text-xs text-gray-500">
                        المخزون: {product.stock} قطعة
                      </p>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-sm">{product.price}</p>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        product.status === "active"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}>
                        {product.status === "active" ? "نشط" : "غير نشط"}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>لا توجد منتجات بعد</p>
                  <p className="text-sm mt-1">ابدأ بإضافة منتجات جديدة</p>
                </div>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full" asChild>
                <Link href="/admin/products">إدارة المنتجات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* رسم بياني للمبيعات (يمكن إضافة مكتبة رسوم بيانية لاحقاً) */}
      <Card>
        <CardHeader>
          <CardTitle>نظرة عامة على المبيعات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <p className="text-gray-500">سيتم إضافة الرسوم البيانية قريباً</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
