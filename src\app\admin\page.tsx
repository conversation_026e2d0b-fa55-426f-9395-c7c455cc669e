import React from "react";
import {
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  TrendingUp,
  TrendingDown,
  Eye,
  Plus,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";

// بيانات وهمية للإحصائيات
const stats = [
  {
    title: "إجمالي المبيعات",
    value: "45,231 ريال",
    change: "+20.1%",
    changeType: "increase" as const,
    icon: DollarSign,
  },
  {
    title: "الطلبات الجديدة",
    value: "156",
    change: "+12.5%",
    changeType: "increase" as const,
    icon: ShoppingCart,
  },
  {
    title: "العملاء الجدد",
    value: "89",
    change: "-2.3%",
    changeType: "decrease" as const,
    icon: Users,
  },
  {
    title: "المنتجات",
    value: "234",
    change: "+5.2%",
    changeType: "increase" as const,
    icon: Package,
  },
];

const recentOrders = [
  {
    id: "ORD-001",
    customer: "أحمد محمد",
    product: "عدسات أكيوفيو اليومية",
    amount: "120 ريال",
    status: "مكتمل",
    date: "2024-01-15",
  },
  {
    id: "ORD-002",
    customer: "فاطمة أحمد",
    product: "عدسات بايوفينيتي الشهرية",
    amount: "85 ريال",
    status: "قيد المعالجة",
    date: "2024-01-15",
  },
  {
    id: "ORD-003",
    customer: "محمد علي",
    product: "نظارات طبية",
    amount: "350 ريال",
    status: "تم الشحن",
    date: "2024-01-14",
  },
  {
    id: "ORD-004",
    customer: "سارة خالد",
    product: "عدسات ملونة",
    amount: "95 ريال",
    status: "مكتمل",
    date: "2024-01-14",
  },
];

const topProducts = [
  {
    name: "عدسات أكيوفيو اليومية",
    sales: 45,
    revenue: "5,400 ريال",
  },
  {
    name: "عدسات بايوفينيتي الشهرية",
    sales: 32,
    revenue: "2,720 ريال",
  },
  {
    name: "عدسات إير أوبتكس الملونة",
    sales: 28,
    revenue: "2,660 ريال",
  },
  {
    name: "نظارات طبية كلاسيكية",
    sales: 15,
    revenue: "5,250 ريال",
  },
];

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      {/* العنوان والأزرار السريعة */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
          <p className="text-gray-600 mt-1">مرحباً بك في لوحة تحكم VisionLens</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/admin/products/new">
              <Plus className="h-4 w-4 mr-2" />
              إضافة منتج
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/orders">
              <Eye className="h-4 w-4 mr-2" />
              عرض الطلبات
            </Link>
          </Button>
        </div>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-gray-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-gray-600">
                {stat.changeType === "increase" ? (
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                )}
                <span
                  className={
                    stat.changeType === "increase"
                      ? "text-green-500"
                      : "text-red-500"
                  }
                >
                  {stat.change}
                </span>
                <span className="mr-1">من الشهر الماضي</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الطلبات الأخيرة */}
        <Card>
          <CardHeader>
            <CardTitle>الطلبات الأخيرة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders.map((order) => (
                <div
                  key={order.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">{order.id}</span>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          order.status === "مكتمل"
                            ? "bg-green-100 text-green-800"
                            : order.status === "قيد المعالجة"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {order.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{order.customer}</p>
                    <p className="text-xs text-gray-500">{order.product}</p>
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-sm">{order.amount}</p>
                    <p className="text-xs text-gray-500">{order.date}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full" asChild>
                <Link href="/admin/orders">عرض جميع الطلبات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* أفضل المنتجات مبيعاً */}
        <Card>
          <CardHeader>
            <CardTitle>أفضل المنتجات مبيعاً</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex-1">
                    <p className="font-medium text-sm">{product.name}</p>
                    <p className="text-xs text-gray-500">
                      {product.sales} مبيعة
                    </p>
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-sm">{product.revenue}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full" asChild>
                <Link href="/admin/products">إدارة المنتجات</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* رسم بياني للمبيعات (يمكن إضافة مكتبة رسوم بيانية لاحقاً) */}
      <Card>
        <CardHeader>
          <CardTitle>نظرة عامة على المبيعات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <p className="text-gray-500">سيتم إضافة الرسوم البيانية قريباً</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
