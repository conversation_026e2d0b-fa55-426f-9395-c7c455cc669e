"use client";

import React, { useState } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Copy,
  Calendar,
  Percent,
  DollarSign,
  Users,
  Tag,
  Eye,
  MoreHorizontal,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatPrice } from "@/lib/utils";

// بيانات وهمية للكوبونات
const coupons = [
  {
    id: "1",
    code: "WELCOME20",
    name: "خصم الترحيب",
    description: "خصم 20% للعملاء الجدد",
    type: "percentage",
    value: 20,
    minOrderAmount: 100,
    maxDiscount: 50,
    usageLimit: 100,
    usedCount: 45,
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    isActive: true,
    applicableProducts: "all",
    applicableCategories: ["العدسات اليومية", "العدسات الشهرية"],
  },
  {
    id: "2",
    code: "SAVE50",
    name: "خصم ثابت 50 ريال",
    description: "خصم 50 ريال على الطلبات أكثر من 200 ريال",
    type: "fixed",
    value: 50,
    minOrderAmount: 200,
    maxDiscount: 50,
    usageLimit: 50,
    usedCount: 12,
    startDate: "2024-01-15",
    endDate: "2024-02-15",
    isActive: true,
    applicableProducts: "all",
    applicableCategories: [],
  },
  {
    id: "3",
    code: "GLASSES15",
    name: "خصم النظارات",
    description: "خصم 15% على جميع النظارات الطبية",
    type: "percentage",
    value: 15,
    minOrderAmount: 0,
    maxDiscount: 100,
    usageLimit: 200,
    usedCount: 78,
    startDate: "2024-01-01",
    endDate: "2024-03-31",
    isActive: true,
    applicableProducts: "category",
    applicableCategories: ["النظارات الطبية"],
  },
  {
    id: "4",
    code: "EXPIRED10",
    name: "خصم منتهي الصلاحية",
    description: "خصم 10% منتهي الصلاحية",
    type: "percentage",
    value: 10,
    minOrderAmount: 50,
    maxDiscount: 30,
    usageLimit: 30,
    usedCount: 30,
    startDate: "2023-12-01",
    endDate: "2023-12-31",
    isActive: false,
    applicableProducts: "all",
    applicableCategories: [],
  },
];

export default function CouponsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("الكل");
  const [selectedType, setSelectedType] = useState("الكل");

  const statuses = ["الكل", "نشط", "غير نشط", "منتهي الصلاحية"];
  const types = ["الكل", "نسبة مئوية", "مبلغ ثابت"];

  const filteredCoupons = coupons.filter(coupon => {
    const matchesSearch = 
      coupon.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      coupon.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesStatus = true;
    if (selectedStatus === "نشط") {
      matchesStatus = coupon.isActive && new Date(coupon.endDate) > new Date();
    } else if (selectedStatus === "غير نشط") {
      matchesStatus = !coupon.isActive;
    } else if (selectedStatus === "منتهي الصلاحية") {
      matchesStatus = new Date(coupon.endDate) <= new Date();
    }
    
    let matchesType = true;
    if (selectedType === "نسبة مئوية") {
      matchesType = coupon.type === "percentage";
    } else if (selectedType === "مبلغ ثابت") {
      matchesType = coupon.type === "fixed";
    }
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (coupon: any) => {
    if (!coupon.isActive) return "bg-gray-100 text-gray-800";
    if (new Date(coupon.endDate) <= new Date()) return "bg-red-100 text-red-800";
    return "bg-green-100 text-green-800";
  };

  const getStatusText = (coupon: any) => {
    if (!coupon.isActive) return "غير نشط";
    if (new Date(coupon.endDate) <= new Date()) return "منتهي الصلاحية";
    return "نشط";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("ar-SA");
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert(`تم نسخ الكود: ${text}`);
  };

  const activeCoupons = coupons.filter(c => c.isActive && new Date(c.endDate) > new Date()).length;
  const totalUsage = coupons.reduce((sum, c) => sum + c.usedCount, 0);
  const totalSavings = coupons.reduce((sum, c) => {
    return sum + (c.type === "fixed" ? c.usedCount * c.value : c.usedCount * 25); // تقدير للنسبة المئوية
  }, 0);

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إدارة الكوبونات والعروض</h1>
          <p className="text-gray-600 mt-1">إنشاء وإدارة كوبونات الخصم والعروض الترويجية</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          إضافة كوبون جديد
        </Button>
      </div>

      {/* إحصائيات الكوبونات */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الكوبونات</p>
                <p className="text-3xl font-bold">{coupons.length}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Tag className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">كوبونات نشطة</p>
                <p className="text-3xl font-bold text-green-600">{activeCoupons}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Percent className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الاستخدام</p>
                <p className="text-3xl font-bold text-purple-600">{totalUsage}</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي التوفير</p>
                <p className="text-3xl font-bold text-orange-600">
                  {formatPrice(totalSavings)}
                </p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والفلترة */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الكوبونات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {statuses.map((status) => (
                  <option key={status} value={status}>
                    {status}
                  </option>
                ))}
              </select>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {types.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول الكوبونات */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة الكوبونات ({filteredCoupons.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right py-3 px-4 font-medium">الكوبون</th>
                  <th className="text-right py-3 px-4 font-medium">النوع والقيمة</th>
                  <th className="text-right py-3 px-4 font-medium">الشروط</th>
                  <th className="text-right py-3 px-4 font-medium">الاستخدام</th>
                  <th className="text-right py-3 px-4 font-medium">صالح حتى</th>
                  <th className="text-right py-3 px-4 font-medium">الحالة</th>
                  <th className="text-right py-3 px-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredCoupons.map((coupon) => (
                  <tr key={coupon.id} className="border-b hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="flex items-center gap-2">
                          <p className="font-medium font-mono bg-gray-100 px-2 py-1 rounded text-sm">
                            {coupon.code}
                          </p>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => copyToClipboard(coupon.code)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                        <p className="text-sm font-medium mt-1">{coupon.name}</p>
                        <p className="text-xs text-gray-500">{coupon.description}</p>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        {coupon.type === "percentage" ? (
                          <Percent className="h-4 w-4 text-blue-500" />
                        ) : (
                          <DollarSign className="h-4 w-4 text-green-500" />
                        )}
                        <span className="font-medium">
                          {coupon.type === "percentage" ? `${coupon.value}%` : formatPrice(coupon.value)}
                        </span>
                      </div>
                      {coupon.maxDiscount > 0 && coupon.type === "percentage" && (
                        <p className="text-xs text-gray-500">
                          حد أقصى: {formatPrice(coupon.maxDiscount)}
                        </p>
                      )}
                    </td>
                    <td className="py-4 px-4 text-sm">
                      <div>
                        {coupon.minOrderAmount > 0 && (
                          <p>حد أدنى: {formatPrice(coupon.minOrderAmount)}</p>
                        )}
                        {coupon.applicableProducts === "category" && (
                          <p className="text-xs text-gray-500">
                            فئات محددة
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div>
                        <p className="font-medium">
                          {coupon.usedCount} / {coupon.usageLimit}
                        </p>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{
                              width: `${(coupon.usedCount / coupon.usageLimit) * 100}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span>{formatDate(coupon.endDate)}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(coupon)}`}>
                        {getStatusText(coupon)}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredCoupons.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">لا توجد كوبونات تطابق البحث</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نصائح سريعة */}
      <Card>
        <CardHeader>
          <CardTitle>نصائح لإدارة الكوبونات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">أفضل الممارسات:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• استخدم أكواد واضحة وسهلة التذكر</li>
                <li>• حدد تاريخ انتهاء واضح</li>
                <li>• ضع حد أدنى للطلب لتجنب الخسائر</li>
                <li>• راقب معدل الاستخدام بانتظام</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">أنواع العروض الفعالة:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• خصم الترحيب للعملاء الجدد</li>
                <li>• عروض موسمية ومناسبات</li>
                <li>• خصومات على فئات محددة</li>
                <li>• برامج الولاء والإحالة</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
