"use client";

import React, { useState, useEffect } from "react";
import {
  DollarSign,
  RefreshCw,
  Save,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Clock,
  Globe,
  Calculator,
  Eye,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  SUPPORTED_CURRENCIES,
  updateExchangeRate,
  fetchExchangeRates,
  convertCurrency,
  formatCurrency,
  autoUpdateExchangeRates,
} from "@/lib/currency";

export default function CurrencyManagementPage() {
  const [currencies, setCurrencies] = useState(SUPPORTED_CURRENCIES);
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date().toISOString());
  const [newRate, setNewRate] = useState("");
  const [selectedCurrency, setSelectedCurrency] = useState("USD");
  const [conversionAmount, setConversionAmount] = useState("100");
  const [conversionFrom, setConversionFrom] = useState("USD");
  const [conversionTo, setConversionTo] = useState("IQD");

  useEffect(() => {
    // تحديث أسعار الصرف عند تحميل الصفحة
    handleAutoUpdate();
  }, []);

  const handleManualUpdate = async (currencyCode: string, rate: number) => {
    setIsUpdating(true);
    
    try {
      const success = await updateExchangeRate(currencyCode, rate);
      if (success) {
        // تحديث الحالة المحلية
        setCurrencies(prev => 
          prev.map(curr => 
            curr.code === currencyCode 
              ? { ...curr, exchangeRate: rate, lastUpdated: new Date().toISOString() }
              : curr
          )
        );
        setLastUpdate(new Date().toISOString());
        alert("تم تحديث سعر الصرف بنجاح!");
      }
    } catch (error) {
      console.error("خطأ في تحديث سعر الصرف:", error);
      alert("حدث خطأ أثناء تحديث سعر الصرف");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleAutoUpdate = async () => {
    setIsUpdating(true);
    
    try {
      await autoUpdateExchangeRates();
      // تحديث الحالة المحلية
      setCurrencies([...SUPPORTED_CURRENCIES]);
      setLastUpdate(new Date().toISOString());
    } catch (error) {
      console.error("خطأ في التحديث التلقائي:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleQuickUpdate = () => {
    if (!newRate || !selectedCurrency) return;
    
    const rate = parseFloat(newRate);
    if (isNaN(rate) || rate <= 0) {
      alert("يرجى إدخال سعر صرف صحيح");
      return;
    }
    
    handleManualUpdate(selectedCurrency, rate);
    setNewRate("");
  };

  const calculateConversion = () => {
    try {
      const amount = parseFloat(conversionAmount);
      if (isNaN(amount)) return 0;
      
      return convertCurrency(amount, conversionFrom, conversionTo);
    } catch (error) {
      return 0;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("ar-IQ", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getChangeIndicator = (currency: any) => {
    // محاكاة تغيير السعر (في التطبيق الحقيقي سيتم حسابه من البيانات التاريخية)
    const change = Math.random() > 0.5 ? 1 : -1;
    const changePercent = (Math.random() * 2).toFixed(2);
    
    return {
      direction: change,
      percent: changePercent,
    };
  };

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إدارة أسعار الصرف</h1>
          <p className="text-gray-600 mt-1">إدارة العملات وأسعار الصرف للمتجر</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleAutoUpdate} disabled={isUpdating}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isUpdating ? 'animate-spin' : ''}`} />
            تحديث تلقائي
          </Button>
        </div>
      </div>

      {/* معلومات آخر تحديث */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-blue-800">
            <Clock className="h-5 w-5" />
            <span className="font-medium">آخر تحديث:</span>
            <span>{formatDate(lastUpdate)}</span>
          </div>
          <p className="text-sm text-blue-700 mt-2">
            يتم تحديث أسعار الصرف تلقائياً كل ساعة من البنك المركزي العراقي
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* العمود الرئيسي - أسعار الصرف */}
        <div className="lg:col-span-2 space-y-6">
          {/* أسعار الصرف الحالية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                أسعار الصرف الحالية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currencies.map((currency) => {
                  const change = getChangeIndicator(currency);
                  
                  return (
                    <div key={currency.code} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                          <span className="font-bold text-lg">{currency.symbol}</span>
                        </div>
                        <div>
                          <h3 className="font-semibold">{currency.nameAr}</h3>
                          <p className="text-sm text-gray-600">{currency.name} ({currency.code})</p>
                          {currency.isDefault && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
                              العملة الأساسية
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="text-left">
                        {currency.code === "IQD" ? (
                          <div>
                            <p className="text-2xl font-bold">1.00</p>
                            <p className="text-sm text-gray-600">دينار عراقي</p>
                          </div>
                        ) : (
                          <div>
                            <p className="text-2xl font-bold">
                              {formatCurrency(currency.exchangeRate, "IQD")}
                            </p>
                            <div className="flex items-center gap-1 mt-1">
                              {change.direction > 0 ? (
                                <TrendingUp className="h-4 w-4 text-green-500" />
                              ) : (
                                <TrendingDown className="h-4 w-4 text-red-500" />
                              )}
                              <span className={`text-sm ${change.direction > 0 ? 'text-green-500' : 'text-red-500'}`}>
                                {change.percent}%
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* تحديث سريع */}
          <Card>
            <CardHeader>
              <CardTitle>تحديث سريع لسعر الصرف</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">العملة</label>
                  <select
                    value={selectedCurrency}
                    onChange={(e) => setSelectedCurrency(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    {currencies.filter(c => c.code !== "IQD").map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.nameAr} ({currency.code})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">السعر الجديد (بالدينار)</label>
                  <Input
                    type="number"
                    value={newRate}
                    onChange={(e) => setNewRate(e.target.value)}
                    placeholder="1310"
                    step="0.01"
                  />
                </div>
                
                <div className="flex items-end">
                  <Button 
                    onClick={handleQuickUpdate} 
                    disabled={isUpdating || !newRate}
                    className="w-full"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    تحديث
                  </Button>
                </div>
              </div>
              
              <div className="p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-800">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm font-medium">تنبيه:</span>
                </div>
                <p className="text-sm text-yellow-700 mt-1">
                  تأكد من دقة سعر الصرف قبل التحديث. سيؤثر هذا على جميع أسعار المنتجات المستوردة.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* العمود الجانبي */}
        <div className="lg:col-span-1 space-y-6">
          {/* حاسبة تحويل العملات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                حاسبة تحويل العملات
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">المبلغ</label>
                <Input
                  type="number"
                  value={conversionAmount}
                  onChange={(e) => setConversionAmount(e.target.value)}
                  placeholder="100"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">من</label>
                <select
                  value={conversionFrom}
                  onChange={(e) => setConversionFrom(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  {currencies.map((currency) => (
                    <option key={currency.code} value={currency.code}>
                      {currency.nameAr} ({currency.code})
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">إلى</label>
                <select
                  value={conversionTo}
                  onChange={(e) => setConversionTo(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  {currencies.map((currency) => (
                    <option key={currency.code} value={currency.code}>
                      {currency.nameAr} ({currency.code})
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-1">النتيجة</p>
                  <p className="text-2xl font-bold text-green-800">
                    {formatCurrency(calculateConversion(), conversionTo)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* معلومات العملة الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                العملة الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-3">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl font-bold text-green-800">د.ع</span>
                </div>
                <div>
                  <h3 className="font-semibold text-lg">الدينار العراقي</h3>
                  <p className="text-sm text-gray-600">Iraqi Dinar (IQD)</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-xs text-gray-600">
                    العملة الأساسية للمتجر. جميع الأسعار والحسابات تتم بالدينار العراقي.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* إحصائيات سريعة */}
          <Card>
            <CardHeader>
              <CardTitle>إحصائيات العملات</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">العملات المدعومة</span>
                <span className="font-semibold">{currencies.length}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">العملات النشطة</span>
                <span className="font-semibold">{currencies.filter(c => c.isActive).length}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">آخر تحديث</span>
                <span className="font-semibold text-xs">
                  {new Date(lastUpdate).toLocaleTimeString("ar-IQ")}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* تنبيه مهم */}
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-orange-800">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">ملاحظة مهمة:</span>
          </div>
          <ul className="mt-2 text-sm text-orange-700 space-y-1">
            <li>• الدينار العراقي هو العملة الأساسية للمتجر</li>
            <li>• الدولار الأمريكي متاح للمدير فقط عند استيراد المنتجات</li>
            <li>• يتم تحويل جميع الأسعار تلقائياً إلى الدينار العراقي</li>
            <li>• تأكد من تحديث أسعار الصرف بانتظام للحصول على أسعار دقيقة</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
