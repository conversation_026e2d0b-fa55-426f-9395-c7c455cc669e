"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/settings/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/settings/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/admin/products/settings/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductSettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bell,Database,DollarSign,Image,Package,Save,Settings,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ProductSettingsPage() {\n    _s();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // إعدادات عامة\n        defaultCategory: \"العدسات اليومية\",\n        defaultBrand: \"\",\n        autoGenerateSku: true,\n        skuPrefix: \"PRD\",\n        requireImages: true,\n        maxImages: \"10\",\n        // إعدادات الأسعار\n        defaultCurrency: \"IQD\",\n        importCurrency: \"USD\",\n        autoConvertPrices: true,\n        taxRate: \"0\",\n        includeTaxInPrice: false,\n        allowNegativeStock: false,\n        lowStockThreshold: \"10\",\n        showPriceInMultipleCurrencies: false,\n        // إعدادات الصور\n        imageQuality: \"high\",\n        maxImageSize: \"5\",\n        allowedFormats: [\n            \"jpg\",\n            \"jpeg\",\n            \"png\",\n            \"webp\"\n        ],\n        autoResize: true,\n        watermark: false,\n        // إعدادات المخزون\n        trackInventory: true,\n        autoUpdateStock: true,\n        stockAlerts: true,\n        reserveStock: true,\n        backorderEnabled: false,\n        // إعدادات SEO\n        autoGenerateSlug: true,\n        slugFormat: \"name-sku\",\n        metaTitleFormat: \"{name} - {brand}\",\n        metaDescriptionLength: 160,\n        // إعدادات الأمان\n        requireApproval: false,\n        auditChanges: true,\n        backupFrequency: \"daily\",\n        dataRetention: \"365\",\n        // إعدادات الإشعارات\n        notifyLowStock: true,\n        notifyNewProduct: true,\n        notifyPriceChange: true,\n        notifyStockUpdate: true,\n        // إعدادات الأداء\n        enableCaching: true,\n        cacheExpiry: \"3600\",\n        enableCompression: true,\n        lazyLoadImages: true\n    });\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            // محاكاة حفظ الإعدادات\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            console.log(\"Saving settings:\", settings);\n            alert(\"تم حفظ الإعدادات بنجاح!\");\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"حدث خطأ أثناء حفظ الإعدادات\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const resetToDefaults = ()=>{\n        if (confirm(\"هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\")) {\n            // إعادة تعيين الإعدادات\n            setSettings({\n                defaultCategory: \"العدسات اليومية\",\n                defaultBrand: \"\",\n                autoGenerateSku: true,\n                skuPrefix: \"PRD\",\n                requireImages: true,\n                maxImages: \"10\",\n                defaultCurrency: \"IQD\",\n                importCurrency: \"USD\",\n                autoConvertPrices: true,\n                taxRate: \"0\",\n                includeTaxInPrice: false,\n                allowNegativeStock: false,\n                lowStockThreshold: \"10\",\n                showPriceInMultipleCurrencies: false,\n                imageQuality: \"high\",\n                maxImageSize: \"5\",\n                allowedFormats: [\n                    \"jpg\",\n                    \"jpeg\",\n                    \"png\",\n                    \"webp\"\n                ],\n                autoResize: true,\n                watermark: false,\n                trackInventory: true,\n                autoUpdateStock: true,\n                stockAlerts: true,\n                reserveStock: true,\n                backorderEnabled: false,\n                autoGenerateSlug: true,\n                slugFormat: \"name-sku\",\n                metaTitleFormat: \"{name} - {brand}\",\n                metaDescriptionLength: \"160\",\n                requireApproval: false,\n                auditChanges: true,\n                backupFrequency: \"daily\",\n                dataRetention: \"365\",\n                notifyLowStock: true,\n                notifyNewProduct: true,\n                notifyPriceChange: true,\n                notifyStockUpdate: true,\n                enableCaching: true,\n                cacheExpiry: \"3600\",\n                enableCompression: true,\n                lazyLoadImages: true\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إعدادات المنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"تخصيص إعدادات إدارة المنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: resetToDefaults,\n                                children: \"إعادة تعيين\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSave,\n                                disabled: isSaving,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    isSaving ? \"جاري الحفظ...\" : \"حفظ الإعدادات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الإعدادات العامة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الفئة الافتراضية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.defaultCategory,\n                                                onChange: (e)=>handleInputChange(\"defaultCategory\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات اليومية\",\n                                                        children: \"العدسات اليومية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات الشهرية\",\n                                                        children: \"العدسات الشهرية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"العدسات الملونة\",\n                                                        children: \"العدسات الملونة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"النظارات الطبية\",\n                                                        children: \"النظارات الطبية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"بادئة رمز المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                value: settings.skuPrefix,\n                                                onChange: (e)=>handleInputChange(\"skuPrefix\", e.target.value),\n                                                placeholder: \"PRD\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoGenerateSku\",\n                                                checked: settings.autoGenerateSku,\n                                                onChange: (e)=>handleInputChange(\"autoGenerateSku\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoGenerateSku\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"توليد رمز المنتج تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"requireImages\",\n                                                checked: settings.requireImages,\n                                                onChange: (e)=>handleInputChange(\"requireImages\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"requireImages\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"إجبار رفع صورة واحدة على الأقل\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الحد الأقصى للصور\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.maxImages,\n                                                onChange: (e)=>handleInputChange(\"maxImages\", parseInt(e.target.value)),\n                                                min: \"1\",\n                                                max: \"20\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأسعار\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"العملة الافتراضية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.defaultCurrency,\n                                                onChange: (e)=>handleInputChange(\"defaultCurrency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"IQD\",\n                                                    children: \"دينار عراقي (IQD)\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"الدينار العراقي هو العملة الأساسية للمتجر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"عملة الاستيراد (للمدير فقط)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.importCurrency,\n                                                onChange: (e)=>handleInputChange(\"importCurrency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"USD\",\n                                                        children: \"دولار أمريكي (USD)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"IQD\",\n                                                        children: \"دينار عراقي (IQD)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"العملة المستخدمة عند استيراد المنتجات من الخارج\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoConvertPrices\",\n                                                checked: settings.autoConvertPrices,\n                                                onChange: (e)=>handleInputChange(\"autoConvertPrices\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoConvertPrices\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحويل الأسعار تلقائياً إلى الدينار العراقي\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"معدل الضريبة (%)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.taxRate,\n                                                onChange: (e)=>handleInputChange(\"taxRate\", parseFloat(e.target.value)),\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"0.1\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"العراق لا يطبق ضريبة على المنتجات الطبية عادة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"includeTaxInPrice\",\n                                                checked: settings.includeTaxInPrice,\n                                                onChange: (e)=>handleInputChange(\"includeTaxInPrice\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"includeTaxInPrice\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تضمين الضريبة في السعر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"حد تنبيه المخزون المنخفض\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.lowStockThreshold,\n                                                onChange: (e)=>handleInputChange(\"lowStockThreshold\", parseInt(e.target.value)),\n                                                min: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"allowNegativeStock\",\n                                                checked: settings.allowNegativeStock,\n                                                onChange: (e)=>handleInputChange(\"allowNegativeStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"allowNegativeStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"السماح بالمخزون السالب\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الصور\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"جودة الصور\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.imageQuality,\n                                                onChange: (e)=>handleInputChange(\"imageQuality\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"low\",\n                                                        children: \"منخفضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"medium\",\n                                                        children: \"متوسطة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"high\",\n                                                        children: \"عالية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"original\",\n                                                        children: \"أصلية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"الحد الأقصى لحجم الصورة (MB)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.maxImageSize,\n                                                onChange: (e)=>handleInputChange(\"maxImageSize\", parseInt(e.target.value)),\n                                                min: \"1\",\n                                                max: \"50\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoResize\",\n                                                checked: settings.autoResize,\n                                                onChange: (e)=>handleInputChange(\"autoResize\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoResize\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تغيير حجم الصور تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"watermark\",\n                                                checked: settings.watermark,\n                                                onChange: (e)=>handleInputChange(\"watermark\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"watermark\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"إضافة علامة مائية\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات المخزون\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"trackInventory\",\n                                                checked: settings.trackInventory,\n                                                onChange: (e)=>handleInputChange(\"trackInventory\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"trackInventory\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تتبع المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoUpdateStock\",\n                                                checked: settings.autoUpdateStock,\n                                                onChange: (e)=>handleInputChange(\"autoUpdateStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoUpdateStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحديث المخزون تلقائياً عند البيع\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"stockAlerts\",\n                                                checked: settings.stockAlerts,\n                                                onChange: (e)=>handleInputChange(\"stockAlerts\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"stockAlerts\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيهات المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"reserveStock\",\n                                                checked: settings.reserveStock,\n                                                onChange: (e)=>handleInputChange(\"reserveStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"reserveStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"حجز المخزون عند الطلب\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"backorderEnabled\",\n                                                checked: settings.backorderEnabled,\n                                                onChange: (e)=>handleInputChange(\"backorderEnabled\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"backorderEnabled\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"السماح بالطلب المسبق\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات SEO\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"autoGenerateSlug\",\n                                                checked: settings.autoGenerateSlug,\n                                                onChange: (e)=>handleInputChange(\"autoGenerateSlug\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"autoGenerateSlug\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"توليد الرابط تلقائياً\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تنسيق الرابط\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.slugFormat,\n                                                onChange: (e)=>handleInputChange(\"slugFormat\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: \"الاسم فقط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name-sku\",\n                                                        children: \"الاسم + رمز المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sku-name\",\n                                                        children: \"رمز المنتج + الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"category-name\",\n                                                        children: \"الفئة + الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تنسيق عنوان الصفحة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                value: settings.metaTitleFormat,\n                                                onChange: (e)=>handleInputChange(\"metaTitleFormat\", e.target.value),\n                                                placeholder: \"{name} - {brand}\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"طول وصف الصفحة (حرف)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.metaDescriptionLength,\n                                                onChange: (e)=>handleInputChange(\"metaDescriptionLength\", parseInt(e.target.value)),\n                                                min: \"50\",\n                                                max: \"300\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأمان\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"requireApproval\",\n                                                checked: settings.requireApproval,\n                                                onChange: (e)=>handleInputChange(\"requireApproval\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"requireApproval\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"مطالبة بالموافقة على المنتجات الجديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"auditChanges\",\n                                                checked: settings.auditChanges,\n                                                onChange: (e)=>handleInputChange(\"auditChanges\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"auditChanges\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تسجيل جميع التغييرات\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"تكرار النسخ الاحتياطي\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: settings.backupFrequency,\n                                                onChange: (e)=>handleInputChange(\"backupFrequency\", e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"hourly\",\n                                                        children: \"كل ساعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"daily\",\n                                                        children: \"يومياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"weekly\",\n                                                        children: \"أسبوعياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"monthly\",\n                                                        children: \"شهرياً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"مدة الاحتفاظ بالبيانات (يوم)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.dataRetention,\n                                                onChange: (e)=>handleInputChange(\"dataRetention\", parseInt(e.target.value)),\n                                                min: \"30\",\n                                                max: \"3650\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الإشعارات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyLowStock\",\n                                                checked: settings.notifyLowStock,\n                                                onChange: (e)=>handleInputChange(\"notifyLowStock\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyLowStock\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه المخزون المنخفض\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyNewProduct\",\n                                                checked: settings.notifyNewProduct,\n                                                onChange: (e)=>handleInputChange(\"notifyNewProduct\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyNewProduct\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه المنتج الجديد\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyPriceChange\",\n                                                checked: settings.notifyPriceChange,\n                                                onChange: (e)=>handleInputChange(\"notifyPriceChange\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyPriceChange\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه تغيير السعر\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"notifyStockUpdate\",\n                                                checked: settings.notifyStockUpdate,\n                                                onChange: (e)=>handleInputChange(\"notifyStockUpdate\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"notifyStockUpdate\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تنبيه تحديث المخزون\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إعدادات الأداء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"enableCaching\",\n                                                checked: settings.enableCaching,\n                                                onChange: (e)=>handleInputChange(\"enableCaching\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"enableCaching\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تفعيل التخزين المؤقت\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-2\",\n                                                children: \"مدة انتهاء التخزين المؤقت (ثانية)\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                type: \"number\",\n                                                value: settings.cacheExpiry,\n                                                onChange: (e)=>handleInputChange(\"cacheExpiry\", parseInt(e.target.value)),\n                                                min: \"60\",\n                                                max: \"86400\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"enableCompression\",\n                                                checked: settings.enableCompression,\n                                                onChange: (e)=>handleInputChange(\"enableCompression\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"enableCompression\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تفعيل ضغط البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"lazyLoadImages\",\n                                                checked: settings.lazyLoadImages,\n                                                onChange: (e)=>handleInputChange(\"lazyLoadImages\", e.target.checked)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"lazyLoadImages\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"تحميل الصور عند الحاجة\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"border-blue-200 bg-blue-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bell_Database_DollarSign_Image_Package_Save_Settings_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"ملاحظة:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-blue-700\",\n                            children: \"بعض الإعدادات قد تحتاج إلى إعادة تشغيل النظام لتصبح فعالة. تأكد من حفظ الإعدادات قبل إجراء أي تغييرات أخرى.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                    lineNumber: 718,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n                lineNumber: 717,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\settings\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductSettingsPage, \"PNj1zEmHg+ueVnXahfNCYDB2ozI=\");\n_c = ProductSettingsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductSettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/settings/page.tsx\n"));

/***/ })

});