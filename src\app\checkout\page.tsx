"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { 
  CreditCard, 
  MapPin, 
  User, 
  Phone, 
  Mail, 
  Lock,
  ChevronLeft,
  Check
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import { formatPrice } from "@/lib/utils";

// بيانات وهمية للطلب
const orderItems = [
  {
    id: "1",
    name: "عدسات أكيوفيو أوازيس اليومية",
    price: 120,
    quantity: 2,
    image: "https://picsum.photos/80/80?random=1",
  },
  {
    id: "2",
    name: "عدسات بايوفينيتي الشهرية",
    price: 85,
    quantity: 1,
    image: "https://picsum.photos/80/80?random=2",
  },
];

export default function CheckoutPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // معلومات الشحن
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    postalCode: "",
    // معلومات الدفع
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    cardName: "",
    paymentMethod: "card",
  });

  const subtotal = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const shipping = 25;
  const tax = subtotal * 0.15; // ضريبة القيمة المضافة 15%
  const total = subtotal + shipping + tax;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // معالجة الطلب
      console.log("Processing order...", formData);
      alert("تم تأكيد طلبك بنجاح!");
    }
  };

  const steps = [
    { id: 1, title: "معلومات الشحن", icon: MapPin },
    { id: 2, title: "طريقة الدفع", icon: CreditCard },
    { id: 3, title: "مراجعة الطلب", icon: Check },
  ];

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* مسار التنقل */}
        <nav className="flex items-center gap-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-primary">الرئيسية</Link>
          <ChevronLeft className="h-4 w-4" />
          <Link href="/cart" className="hover:text-primary">السلة</Link>
          <ChevronLeft className="h-4 w-4" />
          <span className="text-gray-900">الدفع</span>
        </nav>

        {/* مؤشر الخطوات */}
        <div className="mb-8">
          <div className="flex items-center justify-center">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id 
                    ? "bg-primary border-primary text-white" 
                    : "border-gray-300 text-gray-400"
                }`}>
                  <step.icon className="h-5 w-5" />
                </div>
                <span className={`mr-2 text-sm font-medium ${
                  currentStep >= step.id ? "text-primary" : "text-gray-400"
                }`}>
                  {step.title}
                </span>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? "bg-primary" : "bg-gray-300"
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* النموذج */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit}>
              {/* الخطوة 1: معلومات الشحن */}
              {currentStep === 1 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      معلومات الشحن
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">الاسم الأول</label>
                        <Input
                          required
                          value={formData.firstName}
                          onChange={(e) => handleInputChange("firstName", e.target.value)}
                          placeholder="أدخل الاسم الأول"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">الاسم الأخير</label>
                        <Input
                          required
                          value={formData.lastName}
                          onChange={(e) => handleInputChange("lastName", e.target.value)}
                          placeholder="أدخل الاسم الأخير"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">البريد الإلكتروني</label>
                      <Input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">رقم الهاتف</label>
                      <Input
                        type="tel"
                        required
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        placeholder="+966 50 123 4567"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">العنوان</label>
                      <Input
                        required
                        value={formData.address}
                        onChange={(e) => handleInputChange("address", e.target.value)}
                        placeholder="الشارع والحي"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">المدينة</label>
                        <select
                          required
                          value={formData.city}
                          onChange={(e) => handleInputChange("city", e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        >
                          <option value="">اختر المدينة</option>
                          <option value="riyadh">الرياض</option>
                          <option value="jeddah">جدة</option>
                          <option value="dammam">الدمام</option>
                          <option value="mecca">مكة</option>
                          <option value="medina">المدينة</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">الرمز البريدي</label>
                        <Input
                          required
                          value={formData.postalCode}
                          onChange={(e) => handleInputChange("postalCode", e.target.value)}
                          placeholder="12345"
                        />
                      </div>
                    </div>

                    <Button type="submit" className="w-full">
                      متابعة لطريقة الدفع
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* الخطوة 2: طريقة الدفع */}
              {currentStep === 2 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      طريقة الدفع
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* اختيار طريقة الدفع */}
                    <div className="space-y-3">
                      <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="card"
                          checked={formData.paymentMethod === "card"}
                          onChange={(e) => handleInputChange("paymentMethod", e.target.value)}
                          className="mr-3"
                        />
                        <CreditCard className="h-5 w-5 mr-2" />
                        <span>بطاقة ائتمان / خصم</span>
                      </label>
                      
                      <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="cod"
                          checked={formData.paymentMethod === "cod"}
                          onChange={(e) => handleInputChange("paymentMethod", e.target.value)}
                          className="mr-3"
                        />
                        <span>الدفع عند الاستلام</span>
                      </label>
                    </div>

                    {/* تفاصيل البطاقة */}
                    {formData.paymentMethod === "card" && (
                      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                        <div>
                          <label className="block text-sm font-medium mb-2">رقم البطاقة</label>
                          <Input
                            required
                            value={formData.cardNumber}
                            onChange={(e) => handleInputChange("cardNumber", e.target.value)}
                            placeholder="1234 5678 9012 3456"
                            maxLength={19}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium mb-2">تاريخ الانتهاء</label>
                            <Input
                              required
                              value={formData.expiryDate}
                              onChange={(e) => handleInputChange("expiryDate", e.target.value)}
                              placeholder="MM/YY"
                              maxLength={5}
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-2">CVV</label>
                            <Input
                              required
                              value={formData.cvv}
                              onChange={(e) => handleInputChange("cvv", e.target.value)}
                              placeholder="123"
                              maxLength={4}
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">اسم حامل البطاقة</label>
                          <Input
                            required
                            value={formData.cardName}
                            onChange={(e) => handleInputChange("cardName", e.target.value)}
                            placeholder="الاسم كما هو مكتوب على البطاقة"
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex gap-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setCurrentStep(1)}
                        className="flex-1"
                      >
                        السابق
                      </Button>
                      <Button type="submit" className="flex-1">
                        مراجعة الطلب
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* الخطوة 3: مراجعة الطلب */}
              {currentStep === 3 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Check className="h-5 w-5" />
                      مراجعة الطلب
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* معلومات الشحن */}
                    <div>
                      <h3 className="font-semibold mb-3">معلومات الشحن</h3>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p>{formData.firstName} {formData.lastName}</p>
                        <p>{formData.email}</p>
                        <p>{formData.phone}</p>
                        <p>{formData.address}</p>
                        <p>{formData.city}, {formData.postalCode}</p>
                      </div>
                    </div>

                    {/* طريقة الدفع */}
                    <div>
                      <h3 className="font-semibold mb-3">طريقة الدفع</h3>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        {formData.paymentMethod === "card" ? (
                          <p>بطاقة ائتمان منتهية بـ {formData.cardNumber.slice(-4)}</p>
                        ) : (
                          <p>الدفع عند الاستلام</p>
                        )}
                      </div>
                    </div>

                    <div className="flex gap-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setCurrentStep(2)}
                        className="flex-1"
                      >
                        السابق
                      </Button>
                      <Button type="submit" className="flex-1">
                        <Lock className="h-4 w-4 mr-2" />
                        تأكيد الطلب
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </form>
          </div>

          {/* ملخص الطلب */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>ملخص الطلب</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* المنتجات */}
                <div className="space-y-3">
                  {orderItems.map((item) => (
                    <div key={item.id} className="flex items-center gap-3">
                      <div className="relative w-12 h-12 rounded-lg overflow-hidden">
                        <Image
                          src={item.image}
                          alt={item.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{item.name}</p>
                        <p className="text-xs text-gray-600">الكمية: {item.quantity}</p>
                      </div>
                      <span className="text-sm font-medium">
                        {formatPrice(item.price * item.quantity)}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>المجموع الفرعي:</span>
                    <span>{formatPrice(subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>الشحن:</span>
                    <span>{formatPrice(shipping)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>{formatPrice(tax)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-semibold pt-2 border-t">
                    <span>المجموع:</span>
                    <span className="text-primary">{formatPrice(total)}</span>
                  </div>
                </div>

                {/* معلومات الأمان */}
                <div className="bg-green-50 p-3 rounded-lg text-sm text-green-800">
                  <div className="flex items-center gap-2 mb-1">
                    <Lock className="h-4 w-4" />
                    <span className="font-medium">دفع آمن ومضمون</span>
                  </div>
                  <p className="text-xs">معلوماتك محمية بتشفير SSL</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
