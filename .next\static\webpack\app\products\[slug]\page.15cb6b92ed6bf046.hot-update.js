"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/[slug]/page",{

/***/ "(app-pages-browser)/./src/app/products/[slug]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/products/[slug]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_layout_footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/footer */ \"(app-pages-browser)/./src/components/layout/footer.tsx\");\n/* harmony import */ var _components_product_product_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/product/product-card */ \"(app-pages-browser)/./src/components/product/product-card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// بيانات وهمية للمنتج\nconst product = {\n    id: \"1\",\n    name: \"Acuvue Oasys Daily\",\n    nameAr: \"عدسات أكيوفيو أوازيس اليومية\",\n    slug: \"acuvue-oasys-daily\",\n    price: 120,\n    comparePrice: 150,\n    images: [\n        \"https://picsum.photos/600/600?random=1\",\n        \"https://picsum.photos/600/600?random=11\",\n        \"https://picsum.photos/600/600?random=12\",\n        \"https://picsum.photos/600/600?random=13\"\n    ],\n    brand: \"Johnson & Johnson\",\n    category: \"العدسات اليومية\",\n    rating: 4.8,\n    reviewCount: 124,\n    inStock: true,\n    stockCount: 45,\n    description: \"عدسات لاصقة يومية متطورة توفر راحة استثنائية طوال اليوم. مصنوعة من مادة السيليكون هيدروجيل التي تسمح بمرور الأكسجين بشكل ممتاز، مما يحافظ على صحة العين ونعومتها.\",\n    features: [\n        \"تقنية HydraLuxe للترطيب المستمر\",\n        \"حماية من الأشعة فوق البنفسجية\",\n        \"سهولة في الارتداء والإزالة\",\n        \"مناسبة للاستخدام اليومي\",\n        \"معتمدة من إدارة الغذاء والدواء الأمريكية\"\n    ],\n    specifications: {\n        \"نوع العدسة\": \"يومية\",\n        \"المادة\": \"سيليكون هيدروجيل\",\n        \"نفاذية الأكسجين\": \"عالية\",\n        \"محتوى الماء\": \"38%\",\n        \"الحماية من الأشعة\": \"نعم\",\n        \"العبوة\": \"30 عدسة\"\n    }\n};\n// منتجات مشابهة\nconst relatedProducts = [\n    {\n        id: \"2\",\n        name: \"Biofinity Monthly\",\n        nameAr: \"عدسات بايوفينيتي الشهرية\",\n        slug: \"biofinity-monthly\",\n        price: 85,\n        comparePrice: 100,\n        image: \"https://picsum.photos/400/400?random=2\",\n        brand: \"CooperVision\",\n        rating: 4.6,\n        reviewCount: 89,\n        inStock: true\n    },\n    {\n        id: \"3\",\n        name: \"Air Optix Colors\",\n        nameAr: \"عدسات إير أوبتكس الملونة\",\n        slug: \"air-optix-colors\",\n        price: 95,\n        image: \"https://picsum.photos/400/400?random=3\",\n        brand: \"Alcon\",\n        rating: 4.7,\n        reviewCount: 156,\n        inStock: true\n    },\n    {\n        id: \"4\",\n        name: \"Dailies Total 1\",\n        nameAr: \"عدسات ديليز توتال ون\",\n        slug: \"dailies-total-1\",\n        price: 140,\n        comparePrice: 160,\n        image: \"https://picsum.photos/400/400?random=4\",\n        brand: \"Alcon\",\n        rating: 4.9,\n        reviewCount: 203,\n        inStock: true\n    }\n];\n// تقييمات وهمية\nconst reviews = [\n    {\n        id: 1,\n        user: \"أحمد محمد\",\n        rating: 5,\n        date: \"2024-01-10\",\n        comment: \"عدسات ممتازة وراحة طوال اليوم. أنصح بها بشدة!\"\n    },\n    {\n        id: 2,\n        user: \"فاطمة أحمد\",\n        rating: 4,\n        date: \"2024-01-08\",\n        comment: \"جودة عالية وسعر مناسب. التوصيل كان سريع.\"\n    },\n    {\n        id: 3,\n        user: \"محمد علي\",\n        rating: 5,\n        date: \"2024-01-05\",\n        comment: \"أفضل عدسات جربتها. لا أشعر بها في عيني.\"\n    }\n];\nfunction ProductDetailPage() {\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isWishlisted, setIsWishlisted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const handleAddToCart = ()=>{\n        console.log(\"Added \".concat(quantity, \" items to cart\"));\n    };\n    const handleWishlistToggle = ()=>{\n        setIsWishlisted(!isWishlisted);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center gap-2 text-sm text-gray-600 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                className: \"hover:text-primary\",\n                                children: \"الرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/products\",\n                                className: \"hover:text-primary\",\n                                children: \"المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-900\",\n                                children: product.nameAr\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-square overflow-hidden rounded-lg border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: product.images[selectedImage],\n                                            alt: product.nameAr,\n                                            width: 600,\n                                            height: 600,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-2\",\n                                        children: product.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedImage(index),\n                                                className: \"aspect-square overflow-hidden rounded-lg border-2 \".concat(selectedImage === index ? \"border-primary\" : \"border-gray-200\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: image,\n                                                    alt: \"\".concat(product.nameAr, \" \").concat(index + 1),\n                                                    width: 150,\n                                                    height: 150,\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2\",\n                                                children: product.brand\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold mb-2\",\n                                                children: product.nameAr\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-600\",\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 \".concat(i < Math.floor(product.rating) ? \"fill-yellow-400 text-yellow-400\" : \"text-gray-300\")\n                                                    }, i, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    product.rating,\n                                                    \" (\",\n                                                    product.reviewCount,\n                                                    \" تقييم)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-3xl font-bold text-primary\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatPrice)(product.price)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            product.comparePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl text-gray-500 line-through\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatPrice)(product.comparePrice)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.comparePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-red-100 text-red-800 text-sm px-2 py-1 rounded\",\n                                                children: [\n                                                    \"وفر \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatPrice)(product.comparePrice - product.price)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"الكمية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                                                                disabled: quantity <= 1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-4 py-2 min-w-[60px] text-center\",\n                                                                children: quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>setQuantity(quantity + 1),\n                                                                disabled: quantity >= product.stockCount,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"متوفر \",\n                                                            product.stockCount,\n                                                            \" قطعة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"flex-1\",\n                                                        onClick: handleAddToCart,\n                                                        disabled: !product.inStock,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"أضف للسلة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: handleWishlistToggle,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(isWishlisted ? \"fill-red-500 text-red-500\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"شحن مجاني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"ضمان الجودة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"إرجاع مجاني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"description\",\n                                            label: \"الوصف\"\n                                        },\n                                        {\n                                            id: \"specifications\",\n                                            label: \"المواصفات\"\n                                        },\n                                        {\n                                            id: \"reviews\",\n                                            label: \"التقييمات\"\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary text-primary\" : \"border-transparent text-gray-500 hover:text-gray-700\"),\n                                            children: tab.label\n                                        }, tab.id, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: [\n                                    activeTab === \"description\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-3\",\n                                                        children: \"المميزات:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: product.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"specifications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: Object.entries(product.specifications).map((param)=>{\n                                            let [key, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between py-2 border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            key,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: value\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"reviews\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: [\n                                                            \"التقييمات (\",\n                                                            reviews.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        children: \"اكتب تقييم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: review.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: review.date\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex mb-2\",\n                                                                    children: [\n                                                                        ...Array(5)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 \".concat(i < review.rating ? \"fill-yellow-400 text-yellow-400\" : \"text-gray-300\")\n                                                                        }, i, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: review.comment\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, review.id, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-6\",\n                                children: \"منتجات مشابهة\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                children: relatedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_product_card__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        product: product\n                                    }, product.id, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailPage, \"F4aWEDo/DA7ySJGIUxp9SAp64wE=\");\n_c = ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/[slug]/page.tsx\n"));

/***/ })

});