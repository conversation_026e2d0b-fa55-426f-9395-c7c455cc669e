"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  ArrowLeft,
  Upload,
  X,
  Plus,
  Save,
  Eye,
  AlertCircle,
  Package,
  DollarSign,
  Tag,
  FileText,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function NewProductPage() {
  const [formData, setFormData] = useState({
    // معلومات أساسية
    name: "",
    nameEn: "",
    description: "",
    shortDescription: "",
    sku: "",
    barcode: "",
    
    // التصنيف
    category: "",
    brand: "",
    tags: [] as string[],
    
    // الأسعار
    price: "",
    comparePrice: "",
    cost: "",
    
    // المخزون
    trackQuantity: true,
    quantity: "",
    minQuantity: "",
    maxQuantity: "",
    location: "",
    
    // الشحن
    weight: "",
    dimensions: {
      length: "",
      width: "",
      height: "",
    },
    
    // SEO
    metaTitle: "",
    metaDescription: "",
    slug: "",
    
    // الحالة
    status: "draft",
    featured: false,
    allowBackorder: false,
    
    // المواصفات
    specifications: [] as { key: string; value: string }[],
    
    // الصور
    images: [] as string[],
  });

  const [currentTag, setCurrentTag] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingImages, setUploadingImages] = useState(false);
  const [previewImages, setPreviewImages] = useState<string[]>([]);

  const categories = [
    "العدسات اليومية",
    "العدسات الشهرية", 
    "العدسات الملونة",
    "العدسات الأسبوعية",
    "النظارات الطبية",
    "النظارات الشمسية",
    "الإكسسوارات",
  ];

  const brands = [
    "Johnson & Johnson",
    "Alcon",
    "CooperVision", 
    "Bausch & Lomb",
    "Ray-Ban",
    "Oakley",
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
    
    // توليد slug تلقائياً من الاسم
    if (field === "name" && value) {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\u0600-\u06FF\s-]/g, "")
        .replace(/\s+/g, "-")
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  };

  const handleDimensionChange = (dimension: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      dimensions: { ...prev.dimensions, [dimension]: value }
    }));
  };

  const addTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }));
      setCurrentTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addSpecification = () => {
    setFormData(prev => ({
      ...prev,
      specifications: [...prev.specifications, { key: "", value: "" }]
    }));
  };

  const updateSpecification = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      specifications: prev.specifications.map((spec, i) => 
        i === index ? { ...spec, [field]: value } : spec
      )
    }));
  };

  const removeSpecification = (index: number) => {
    setFormData(prev => ({
      ...prev,
      specifications: prev.specifications.filter((_, i) => i !== index)
    }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    setUploadingImages(true);
    const newImages: string[] = [];
    const newPreviews: string[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // إنشاء معاينة محلية
        const preview = URL.createObjectURL(file);
        newPreviews.push(preview);

        // محاكاة رفع الصورة
        await new Promise(resolve => setTimeout(resolve, 1000));

        // في التطبيق الحقيقي، سيتم رفع الصورة إلى الخادم
        const imageUrl = `https://picsum.photos/400/400?random=${Date.now()}-${i}`;
        newImages.push(imageUrl);
      }

      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...newImages]
      }));

      setPreviewImages(prev => [...prev, ...newPreviews]);

    } catch (error) {
      console.error("Error uploading images:", error);
      alert("حدث خطأ أثناء رفع الصور");
    } finally {
      setUploadingImages(false);
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));

    setPreviewImages(prev => {
      const newPreviews = prev.filter((_, i) => i !== index);
      // تنظيف URL المؤقت
      if (prev[index]) {
        URL.revokeObjectURL(prev[index]);
      }
      return newPreviews;
    });
  };

  const duplicateProduct = () => {
    const duplicatedData = {
      ...formData,
      name: `${formData.name} - نسخة`,
      nameEn: `${formData.nameEn} - Copy`,
      sku: `${formData.sku}-COPY`,
      slug: `${formData.slug}-copy`,
    };
    setFormData(duplicatedData);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = "اسم المنتج مطلوب";
    if (!formData.nameEn.trim()) newErrors.nameEn = "الاسم الإنجليزي مطلوب";
    if (!formData.description.trim()) newErrors.description = "الوصف مطلوب";
    if (!formData.sku.trim()) newErrors.sku = "رمز المنتج مطلوب";
    if (!formData.category) newErrors.category = "الفئة مطلوبة";
    if (!formData.brand) newErrors.brand = "العلامة التجارية مطلوبة";
    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = "السعر مطلوب ويجب أن يكون أكبر من صفر";
    }

    if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {
      newErrors.quantity = "الكمية مطلوبة";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // هنا سيتم إرسال البيانات إلى API
      console.log("Product data:", formData);
      
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert("تم إضافة المنتج بنجاح!");
      // إعادة توجيه إلى صفحة المنتجات
      // router.push("/admin/products");
      
    } catch (error) {
      console.error("Error creating product:", error);
      alert("حدث خطأ أثناء إضافة المنتج");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveAsDraft = () => {
    setFormData(prev => ({ ...prev, status: "draft" }));
    handleSubmit(new Event("submit") as any);
  };

  const handlePublish = () => {
    setFormData(prev => ({ ...prev, status: "active" }));
    handleSubmit(new Event("submit") as any);
  };

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إضافة منتج جديد</h1>
            <p className="text-gray-600 mt-1">إنشاء منتج جديد في المتجر</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={duplicateProduct} disabled={isSubmitting}>
            <Package className="h-4 w-4 mr-2" />
            نسخ المنتج
          </Button>
          <Button variant="outline" onClick={handleSaveAsDraft} disabled={isSubmitting}>
            <Save className="h-4 w-4 mr-2" />
            حفظ كمسودة
          </Button>
          <Button onClick={handlePublish} disabled={isSubmitting}>
            <Eye className="h-4 w-4 mr-2" />
            نشر المنتج
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* العمود الرئيسي */}
        <div className="lg:col-span-2 space-y-6">
          {/* المعلومات الأساسية */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                المعلومات الأساسية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    اسم المنتج (عربي) *
                  </label>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="أدخل اسم المنتج"
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && (
                    <p className="text-red-500 text-xs mt-1">{errors.name}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    اسم المنتج (إنجليزي) *
                  </label>
                  <Input
                    value={formData.nameEn}
                    onChange={(e) => handleInputChange("nameEn", e.target.value)}
                    placeholder="Product Name in English"
                    className={errors.nameEn ? "border-red-500" : ""}
                  />
                  {errors.nameEn && (
                    <p className="text-red-500 text-xs mt-1">{errors.nameEn}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  الوصف المختصر
                </label>
                <Input
                  value={formData.shortDescription}
                  onChange={(e) => handleInputChange("shortDescription", e.target.value)}
                  placeholder="وصف مختصر للمنتج"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  الوصف التفصيلي *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="وصف تفصيلي للمنتج..."
                  rows={4}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                    errors.description ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.description && (
                  <p className="text-red-500 text-xs mt-1">{errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    رمز المنتج (SKU) *
                  </label>
                  <Input
                    value={formData.sku}
                    onChange={(e) => handleInputChange("sku", e.target.value)}
                    placeholder="PRD-001"
                    className={errors.sku ? "border-red-500" : ""}
                  />
                  {errors.sku && (
                    <p className="text-red-500 text-xs mt-1">{errors.sku}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    الباركود
                  </label>
                  <Input
                    value={formData.barcode}
                    onChange={(e) => handleInputChange("barcode", e.target.value)}
                    placeholder="1234567890123"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* التصنيف */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                التصنيف
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    الفئة *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange("category", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                      errors.category ? "border-red-500" : "border-gray-300"
                    }`}
                  >
                    <option value="">اختر الفئة</option>
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                  {errors.category && (
                    <p className="text-red-500 text-xs mt-1">{errors.category}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    العلامة التجارية *
                  </label>
                  <select
                    value={formData.brand}
                    onChange={(e) => handleInputChange("brand", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                      errors.brand ? "border-red-500" : "border-gray-300"
                    }`}
                  >
                    <option value="">اختر العلامة التجارية</option>
                    {brands.map((brand) => (
                      <option key={brand} value={brand}>
                        {brand}
                      </option>
                    ))}
                  </select>
                  {errors.brand && (
                    <p className="text-red-500 text-xs mt-1">{errors.brand}</p>
                  )}
                </div>
              </div>

              {/* العلامات */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  العلامات (Tags)
                </label>
                <div className="flex gap-2 mb-2">
                  <Input
                    value={currentTag}
                    onChange={(e) => setCurrentTag(e.target.value)}
                    placeholder="أضف علامة"
                    onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-sm"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الأسعار */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                الأسعار
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    سعر البيع *
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) => handleInputChange("price", e.target.value)}
                    placeholder="0.00"
                    className={errors.price ? "border-red-500" : ""}
                  />
                  {errors.price && (
                    <p className="text-red-500 text-xs mt-1">{errors.price}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    السعر المقارن
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={formData.comparePrice}
                    onChange={(e) => handleInputChange("comparePrice", e.target.value)}
                    placeholder="0.00"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    سعر التكلفة
                  </label>
                  <Input
                    type="number"
                    step="0.01"
                    value={formData.cost}
                    onChange={(e) => handleInputChange("cost", e.target.value)}
                    placeholder="0.00"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* المخزون */}
          <Card>
            <CardHeader>
              <CardTitle>إدارة المخزون</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="trackQuantity"
                  checked={formData.trackQuantity}
                  onChange={(e) => handleInputChange("trackQuantity", e.target.checked)}
                />
                <label htmlFor="trackQuantity" className="text-sm font-medium">
                  تتبع الكمية
                </label>
              </div>

              {formData.trackQuantity && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      الكمية الحالية *
                    </label>
                    <Input
                      type="number"
                      value={formData.quantity}
                      onChange={(e) => handleInputChange("quantity", e.target.value)}
                      placeholder="0"
                      className={errors.quantity ? "border-red-500" : ""}
                    />
                    {errors.quantity && (
                      <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      الحد الأدنى
                    </label>
                    <Input
                      type="number"
                      value={formData.minQuantity}
                      onChange={(e) => handleInputChange("minQuantity", e.target.value)}
                      placeholder="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      الحد الأقصى
                    </label>
                    <Input
                      type="number"
                      value={formData.maxQuantity}
                      onChange={(e) => handleInputChange("maxQuantity", e.target.value)}
                      placeholder="100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      الموقع
                    </label>
                    <Input
                      value={formData.location}
                      onChange={(e) => handleInputChange("location", e.target.value)}
                      placeholder="A1-B2"
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="allowBackorder"
                  checked={formData.allowBackorder}
                  onChange={(e) => handleInputChange("allowBackorder", e.target.checked)}
                />
                <label htmlFor="allowBackorder" className="text-sm font-medium">
                  السماح بالطلب المسبق عند نفاد المخزون
                </label>
              </div>
            </CardContent>
          </Card>

          {/* المواصفات */}
          <Card>
            <CardHeader>
              <CardTitle>المواصفات التقنية</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {formData.specifications.map((spec, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="المواصفة"
                      value={spec.key}
                      onChange={(e) => updateSpecification(index, "key", e.target.value)}
                      className="flex-1"
                    />
                    <Input
                      placeholder="القيمة"
                      value={spec.value}
                      onChange={(e) => updateSpecification(index, "value", e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeSpecification(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>

              <Button
                type="button"
                variant="outline"
                onClick={addSpecification}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                إضافة مواصفة
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* العمود الجانبي */}
        <div className="lg:col-span-1 space-y-6">
          {/* حالة المنتج */}
          <Card>
            <CardHeader>
              <CardTitle>حالة المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">الحالة</label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange("status", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="draft">مسودة</option>
                  <option value="active">نشط</option>
                  <option value="inactive">غير نشط</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.featured}
                  onChange={(e) => handleInputChange("featured", e.target.checked)}
                />
                <label htmlFor="featured" className="text-sm font-medium">
                  منتج مميز
                </label>
              </div>
            </CardContent>
          </Card>

          {/* الصور */}
          <Card>
            <CardHeader>
              <CardTitle>صور المنتج</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-sm text-gray-600 mb-2">
                  اسحب الصور هنا أو انقر للتحديد
                </p>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('image-upload')?.click()}
                  disabled={uploadingImages}
                >
                  {uploadingImages ? "جاري الرفع..." : "اختيار الصور"}
                </Button>
              </div>

              {/* معاينة الصور */}
              {(formData.images.length > 0 || previewImages.length > 0) && (
                <div className="grid grid-cols-2 gap-2">
                  {formData.images.map((image, index) => (
                    <div key={index} className="relative group">
                      <div className="aspect-square relative rounded-lg overflow-hidden">
                        <Image
                          src={previewImages[index] || image}
                          alt={`صورة ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-3 w-3" />
                      </button>
                      {index === 0 && (
                        <div className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                          الصورة الرئيسية
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* الشحن */}
          <Card>
            <CardHeader>
              <CardTitle>معلومات الشحن</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  الوزن (جرام)
                </label>
                <Input
                  type="number"
                  value={formData.weight}
                  onChange={(e) => handleInputChange("weight", e.target.value)}
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  الأبعاد (سم)
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <Input
                    type="number"
                    value={formData.dimensions.length}
                    onChange={(e) => handleDimensionChange("length", e.target.value)}
                    placeholder="طول"
                  />
                  <Input
                    type="number"
                    value={formData.dimensions.width}
                    onChange={(e) => handleDimensionChange("width", e.target.value)}
                    placeholder="عرض"
                  />
                  <Input
                    type="number"
                    value={formData.dimensions.height}
                    onChange={(e) => handleDimensionChange("height", e.target.value)}
                    placeholder="ارتفاع"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SEO */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                تحسين محركات البحث
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  الرابط (Slug)
                </label>
                <Input
                  value={formData.slug}
                  onChange={(e) => handleInputChange("slug", e.target.value)}
                  placeholder="product-slug"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  عنوان الصفحة
                </label>
                <Input
                  value={formData.metaTitle}
                  onChange={(e) => handleInputChange("metaTitle", e.target.value)}
                  placeholder="عنوان الصفحة"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  وصف الصفحة
                </label>
                <textarea
                  value={formData.metaDescription}
                  onChange={(e) => handleInputChange("metaDescription", e.target.value)}
                  placeholder="وصف الصفحة لمحركات البحث"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </form>

      {/* رسالة تحذيرية */}
      {Object.keys(errors).length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">يرجى تصحيح الأخطاء التالية:</span>
            </div>
            <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
              {Object.values(errors).map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
