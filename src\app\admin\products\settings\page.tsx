"use client";

import React, { useState } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Save,
  Settings,
  Package,
  DollarSign,
  Image as ImageIcon,
  Database,
  Shield,
  Bell,
  Zap,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function ProductSettingsPage() {
  const [settings, setSettings] = useState({
    // إعدادات عامة
    defaultCategory: "العدسات اليومية",
    defaultBrand: "",
    autoGenerateSku: true,
    skuPrefix: "PRD",
    requireImages: true,
    maxImages: "10",
    
    // إعدادات الأسعار
    defaultCurrency: "IQD",
    importCurrency: "USD",
    autoConvertPrices: true,
    taxRate: "0", // العراق لا يطبق ضريبة على المنتجات الطبية
    includeTaxInPrice: false,
    allowNegativeStock: false,
    lowStockThreshold: "10",
    showPriceInMultipleCurrencies: false,
    
    // إعدادات الصور
    imageQuality: "high",
    maxImageSize: "5", // MB
    allowedFormats: ["jpg", "jpeg", "png", "webp"],
    autoResize: true,
    watermark: false,
    
    // إعدادات المخزون
    trackInventory: true,
    autoUpdateStock: true,
    stockAlerts: true,
    reserveStock: true,
    backorderEnabled: false,
    
    // إعدادات SEO
    autoGenerateSlug: true,
    slugFormat: "name-sku",
    metaTitleFormat: "{name} - {brand}",
    metaDescriptionLength: 160,
    
    // إعدادات الأمان
    requireApproval: false,
    auditChanges: true,
    backupFrequency: "daily",
    dataRetention: "365", // days
    
    // إعدادات الإشعارات
    notifyLowStock: true,
    notifyNewProduct: true,
    notifyPriceChange: true,
    notifyStockUpdate: true,
    
    // إعدادات الأداء
    enableCaching: true,
    cacheExpiry: "3600", // seconds
    enableCompression: true,
    lazyLoadImages: true,
  });

  const [isSaving, setIsSaving] = useState(false);

  const handleInputChange = (field: string, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // محاكاة حفظ الإعدادات
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log("Saving settings:", settings);
      alert("تم حفظ الإعدادات بنجاح!");
      
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("حدث خطأ أثناء حفظ الإعدادات");
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = () => {
    if (confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟")) {
      // إعادة تعيين الإعدادات
      setSettings({
        defaultCategory: "العدسات اليومية",
        defaultBrand: "",
        autoGenerateSku: true,
        skuPrefix: "PRD",
        requireImages: true,
        maxImages: "10",
        defaultCurrency: "IQD",
        importCurrency: "USD",
        autoConvertPrices: true,
        taxRate: "0",
        includeTaxInPrice: false,
        allowNegativeStock: false,
        lowStockThreshold: "10",
        showPriceInMultipleCurrencies: false,
        imageQuality: "high",
        maxImageSize: "5",
        allowedFormats: ["jpg", "jpeg", "png", "webp"],
        autoResize: true,
        watermark: false,
        trackInventory: true,
        autoUpdateStock: true,
        stockAlerts: true,
        reserveStock: true,
        backorderEnabled: false,
        autoGenerateSlug: true,
        slugFormat: "name-sku",
        metaTitleFormat: "{name} - {brand}",
        metaDescriptionLength: "160",
        requireApproval: false,
        auditChanges: true,
        backupFrequency: "daily",
        dataRetention: "365",
        notifyLowStock: true,
        notifyNewProduct: true,
        notifyPriceChange: true,
        notifyStockUpdate: true,
        enableCaching: true,
        cacheExpiry: "3600",
        enableCompression: true,
        lazyLoadImages: true,
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إعدادات المنتجات</h1>
            <p className="text-gray-600 mt-1">تخصيص إعدادات إدارة المنتجات</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            إعادة تعيين
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? "جاري الحفظ..." : "حفظ الإعدادات"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الإعدادات العامة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              الإعدادات العامة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">الفئة الافتراضية</label>
              <select
                value={settings.defaultCategory}
                onChange={(e) => handleInputChange("defaultCategory", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="العدسات اليومية">العدسات اليومية</option>
                <option value="العدسات الشهرية">العدسات الشهرية</option>
                <option value="العدسات الملونة">العدسات الملونة</option>
                <option value="النظارات الطبية">النظارات الطبية</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">بادئة رمز المنتج</label>
              <Input
                value={settings.skuPrefix}
                onChange={(e) => handleInputChange("skuPrefix", e.target.value)}
                placeholder="PRD"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="autoGenerateSku"
                checked={settings.autoGenerateSku}
                onChange={(e) => handleInputChange("autoGenerateSku", e.target.checked)}
              />
              <label htmlFor="autoGenerateSku" className="text-sm font-medium">
                توليد رمز المنتج تلقائياً
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="requireImages"
                checked={settings.requireImages}
                onChange={(e) => handleInputChange("requireImages", e.target.checked)}
              />
              <label htmlFor="requireImages" className="text-sm font-medium">
                إجبار رفع صورة واحدة على الأقل
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">الحد الأقصى للصور</label>
              <Input
                type="number"
                value={settings.maxImages}
                onChange={(e) => handleInputChange("maxImages", e.target.value)}
                min="1"
                max="20"
              />
            </div>
          </CardContent>
        </Card>

        {/* إعدادات الأسعار */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              إعدادات الأسعار
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">العملة الافتراضية</label>
              <select
                value={settings.defaultCurrency}
                onChange={(e) => handleInputChange("defaultCurrency", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="IQD">دينار عراقي (IQD)</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                الدينار العراقي هو العملة الأساسية للمتجر
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">عملة الاستيراد (للمدير فقط)</label>
              <select
                value={settings.importCurrency}
                onChange={(e) => handleInputChange("importCurrency", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="USD">دولار أمريكي (USD)</option>
                <option value="IQD">دينار عراقي (IQD)</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                العملة المستخدمة عند استيراد المنتجات من الخارج
              </p>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="autoConvertPrices"
                checked={settings.autoConvertPrices}
                onChange={(e) => handleInputChange("autoConvertPrices", e.target.checked)}
              />
              <label htmlFor="autoConvertPrices" className="text-sm font-medium">
                تحويل الأسعار تلقائياً إلى الدينار العراقي
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">معدل الضريبة (%)</label>
              <Input
                type="number"
                value={settings.taxRate}
                onChange={(e) => handleInputChange("taxRate", e.target.value)}
                min="0"
                max="100"
                step="0.1"
              />
              <p className="text-xs text-gray-500 mt-1">
                العراق لا يطبق ضريبة على المنتجات الطبية عادة
              </p>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="includeTaxInPrice"
                checked={settings.includeTaxInPrice}
                onChange={(e) => handleInputChange("includeTaxInPrice", e.target.checked)}
              />
              <label htmlFor="includeTaxInPrice" className="text-sm font-medium">
                تضمين الضريبة في السعر
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">حد تنبيه المخزون المنخفض</label>
              <Input
                type="number"
                value={settings.lowStockThreshold}
                onChange={(e) => handleInputChange("lowStockThreshold", e.target.value)}
                min="0"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="allowNegativeStock"
                checked={settings.allowNegativeStock}
                onChange={(e) => handleInputChange("allowNegativeStock", e.target.checked)}
              />
              <label htmlFor="allowNegativeStock" className="text-sm font-medium">
                السماح بالمخزون السالب
              </label>
            </div>
          </CardContent>
        </Card>

        {/* إعدادات الصور */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              إعدادات الصور
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">جودة الصور</label>
              <select
                value={settings.imageQuality}
                onChange={(e) => handleInputChange("imageQuality", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="low">منخفضة</option>
                <option value="medium">متوسطة</option>
                <option value="high">عالية</option>
                <option value="original">أصلية</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">الحد الأقصى لحجم الصورة (MB)</label>
              <Input
                type="number"
                value={settings.maxImageSize}
                onChange={(e) => handleInputChange("maxImageSize", e.target.value)}
                min="1"
                max="50"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="autoResize"
                checked={settings.autoResize}
                onChange={(e) => handleInputChange("autoResize", e.target.checked)}
              />
              <label htmlFor="autoResize" className="text-sm font-medium">
                تغيير حجم الصور تلقائياً
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="watermark"
                checked={settings.watermark}
                onChange={(e) => handleInputChange("watermark", e.target.checked)}
              />
              <label htmlFor="watermark" className="text-sm font-medium">
                إضافة علامة مائية
              </label>
            </div>
          </CardContent>
        </Card>

        {/* إعدادات المخزون */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              إعدادات المخزون
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="trackInventory"
                checked={settings.trackInventory}
                onChange={(e) => handleInputChange("trackInventory", e.target.checked)}
              />
              <label htmlFor="trackInventory" className="text-sm font-medium">
                تتبع المخزون
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="autoUpdateStock"
                checked={settings.autoUpdateStock}
                onChange={(e) => handleInputChange("autoUpdateStock", e.target.checked)}
              />
              <label htmlFor="autoUpdateStock" className="text-sm font-medium">
                تحديث المخزون تلقائياً عند البيع
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="stockAlerts"
                checked={settings.stockAlerts}
                onChange={(e) => handleInputChange("stockAlerts", e.target.checked)}
              />
              <label htmlFor="stockAlerts" className="text-sm font-medium">
                تنبيهات المخزون
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="reserveStock"
                checked={settings.reserveStock}
                onChange={(e) => handleInputChange("reserveStock", e.target.checked)}
              />
              <label htmlFor="reserveStock" className="text-sm font-medium">
                حجز المخزون عند الطلب
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="backorderEnabled"
                checked={settings.backorderEnabled}
                onChange={(e) => handleInputChange("backorderEnabled", e.target.checked)}
              />
              <label htmlFor="backorderEnabled" className="text-sm font-medium">
                السماح بالطلب المسبق
              </label>
            </div>
          </CardContent>
        </Card>

        {/* إعدادات SEO */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              إعدادات SEO
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="autoGenerateSlug"
                checked={settings.autoGenerateSlug}
                onChange={(e) => handleInputChange("autoGenerateSlug", e.target.checked)}
              />
              <label htmlFor="autoGenerateSlug" className="text-sm font-medium">
                توليد الرابط تلقائياً
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">تنسيق الرابط</label>
              <select
                value={settings.slugFormat}
                onChange={(e) => handleInputChange("slugFormat", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="name">الاسم فقط</option>
                <option value="name-sku">الاسم + رمز المنتج</option>
                <option value="sku-name">رمز المنتج + الاسم</option>
                <option value="category-name">الفئة + الاسم</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">تنسيق عنوان الصفحة</label>
              <Input
                value={settings.metaTitleFormat}
                onChange={(e) => handleInputChange("metaTitleFormat", e.target.value)}
                placeholder="{name} - {brand}"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">طول وصف الصفحة (حرف)</label>
              <Input
                type="number"
                value={settings.metaDescriptionLength}
                onChange={(e) => handleInputChange("metaDescriptionLength", e.target.value)}
                min="50"
                max="300"
              />
            </div>
          </CardContent>
        </Card>

        {/* إعدادات الأمان */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              إعدادات الأمان
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="requireApproval"
                checked={settings.requireApproval}
                onChange={(e) => handleInputChange("requireApproval", e.target.checked)}
              />
              <label htmlFor="requireApproval" className="text-sm font-medium">
                مطالبة بالموافقة على المنتجات الجديدة
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="auditChanges"
                checked={settings.auditChanges}
                onChange={(e) => handleInputChange("auditChanges", e.target.checked)}
              />
              <label htmlFor="auditChanges" className="text-sm font-medium">
                تسجيل جميع التغييرات
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">تكرار النسخ الاحتياطي</label>
              <select
                value={settings.backupFrequency}
                onChange={(e) => handleInputChange("backupFrequency", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="hourly">كل ساعة</option>
                <option value="daily">يومياً</option>
                <option value="weekly">أسبوعياً</option>
                <option value="monthly">شهرياً</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">مدة الاحتفاظ بالبيانات (يوم)</label>
              <Input
                type="number"
                value={settings.dataRetention}
                onChange={(e) => handleInputChange("dataRetention", e.target.value)}
                min="30"
                max="3650"
              />
            </div>
          </CardContent>
        </Card>

        {/* إعدادات الإشعارات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              إعدادات الإشعارات
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="notifyLowStock"
                checked={settings.notifyLowStock}
                onChange={(e) => handleInputChange("notifyLowStock", e.target.checked)}
              />
              <label htmlFor="notifyLowStock" className="text-sm font-medium">
                تنبيه المخزون المنخفض
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="notifyNewProduct"
                checked={settings.notifyNewProduct}
                onChange={(e) => handleInputChange("notifyNewProduct", e.target.checked)}
              />
              <label htmlFor="notifyNewProduct" className="text-sm font-medium">
                تنبيه المنتج الجديد
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="notifyPriceChange"
                checked={settings.notifyPriceChange}
                onChange={(e) => handleInputChange("notifyPriceChange", e.target.checked)}
              />
              <label htmlFor="notifyPriceChange" className="text-sm font-medium">
                تنبيه تغيير السعر
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="notifyStockUpdate"
                checked={settings.notifyStockUpdate}
                onChange={(e) => handleInputChange("notifyStockUpdate", e.target.checked)}
              />
              <label htmlFor="notifyStockUpdate" className="text-sm font-medium">
                تنبيه تحديث المخزون
              </label>
            </div>
          </CardContent>
        </Card>

        {/* إعدادات الأداء */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              إعدادات الأداء
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="enableCaching"
                checked={settings.enableCaching}
                onChange={(e) => handleInputChange("enableCaching", e.target.checked)}
              />
              <label htmlFor="enableCaching" className="text-sm font-medium">
                تفعيل التخزين المؤقت
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">مدة انتهاء التخزين المؤقت (ثانية)</label>
              <Input
                type="number"
                value={settings.cacheExpiry}
                onChange={(e) => handleInputChange("cacheExpiry", e.target.value)}
                min="60"
                max="86400"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="enableCompression"
                checked={settings.enableCompression}
                onChange={(e) => handleInputChange("enableCompression", e.target.checked)}
              />
              <label htmlFor="enableCompression" className="text-sm font-medium">
                تفعيل ضغط البيانات
              </label>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="lazyLoadImages"
                checked={settings.lazyLoadImages}
                onChange={(e) => handleInputChange("lazyLoadImages", e.target.checked)}
              />
              <label htmlFor="lazyLoadImages" className="text-sm font-medium">
                تحميل الصور عند الحاجة
              </label>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ملاحظة */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-blue-800">
            <Settings className="h-5 w-5" />
            <span className="font-medium">ملاحظة:</span>
          </div>
          <p className="mt-2 text-sm text-blue-700">
            بعض الإعدادات قد تحتاج إلى إعادة تشغيل النظام لتصبح فعالة. 
            تأكد من حفظ الإعدادات قبل إجراء أي تغييرات أخرى.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
