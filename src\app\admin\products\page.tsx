"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  Download,
  Package,
  Settings,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/currency";
import { PriceDisplay } from "@/components/ui/price-display";

// بيانات وهمية للمنتجات
const products = [
  {
    id: "1",
    name: "عدسات أكيوفيو اليومية",
    nameEn: "Acuvue Oasys Daily",
    sku: "ACU-001",
    category: "العدسات اليومية",
    brand: "Johnson & Johnson",
    price: 120,
    stock: 45,
    status: "متوفر",
    image: "https://picsum.photos/100/100?random=1",
    createdAt: "2024-01-10",
  },
  {
    id: "2",
    name: "عدسات بايوفينيتي الشهرية",
    nameEn: "Biofinity Monthly",
    sku: "BIO-002",
    category: "العدسات الشهرية",
    brand: "CooperVision",
    price: 85,
    stock: 32,
    status: "متوفر",
    image: "https://picsum.photos/100/100?random=2",
    createdAt: "2024-01-08",
  },
  {
    id: "3",
    name: "عدسات إير أوبتكس الملونة",
    nameEn: "Air Optix Colors",
    sku: "AIR-003",
    category: "العدسات الملونة",
    brand: "Alcon",
    price: 95,
    stock: 0,
    status: "نفد المخزون",
    image: "https://picsum.photos/100/100?random=3",
    createdAt: "2024-01-05",
  },
  {
    id: "4",
    name: "نظارات طبية كلاسيكية",
    nameEn: "Classic Medical Glasses",
    sku: "GLS-004",
    category: "النظارات الطبية",
    brand: "Ray-Ban",
    price: 350,
    stock: 15,
    status: "متوفر",
    image: "https://picsum.photos/100/100?random=4",
    createdAt: "2024-01-03",
  },
  {
    id: "5",
    name: "عدسات ديليز توتال ون",
    nameEn: "Dailies Total 1",
    sku: "DAI-005",
    category: "العدسات اليومية",
    brand: "Alcon",
    price: 140,
    stock: 28,
    status: "متوفر",
    image: "https://picsum.photos/100/100?random=5",
    createdAt: "2024-01-01",
  },
];

export default function ProductsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("الكل");

  const filteredProducts = products.filter((product) => {
    const matchesSearch = product.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase()) ||
      product.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === "الكل" || product.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const categories = ["الكل", ...Array.from(new Set(products.map(p => p.category)))];

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">إدارة المنتجات</h1>
          <p className="text-gray-600 mt-1">إدارة وتحديث منتجات المتجر</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/admin/products/settings">
              <Settings className="h-4 w-4 mr-2" />
              الإعدادات
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/products/import-export">
              <Download className="h-4 w-4 mr-2" />
              استيراد/تصدير
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/admin/products/bulk-actions">
              <Package className="h-4 w-4 mr-2" />
              عمليات متعددة
            </Link>
          </Button>
          <Button asChild>
            <Link href="/admin/products/new">
              <Plus className="h-4 w-4 mr-2" />
              إضافة منتج جديد
            </Link>
          </Button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{products.length}</div>
            <p className="text-sm text-gray-600">إجمالي المنتجات</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {products.filter(p => p.status === "متوفر").length}
            </div>
            <p className="text-sm text-gray-600">متوفر</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">
              {products.filter(p => p.status === "نفد المخزون").length}
            </div>
            <p className="text-sm text-gray-600">نفد المخزون</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {products.reduce((sum, p) => sum + p.stock, 0)}
            </div>
            <p className="text-sm text-gray-600">إجمالي المخزون</p>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والفلترة */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المنتجات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                فلترة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* جدول المنتجات */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة المنتجات ({filteredProducts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right py-3 px-4 font-medium">المنتج</th>
                  <th className="text-right py-3 px-4 font-medium">رمز المنتج</th>
                  <th className="text-right py-3 px-4 font-medium">الفئة</th>
                  <th className="text-right py-3 px-4 font-medium">السعر</th>
                  <th className="text-right py-3 px-4 font-medium">المخزون</th>
                  <th className="text-right py-3 px-4 font-medium">الحالة</th>
                  <th className="text-right py-3 px-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-3">
                        <Image
                          src={product.image}
                          alt={product.name}
                          width={40}
                          height={40}
                          className="rounded-lg object-cover"
                        />
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-500">{product.brand}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-sm">{product.sku}</td>
                    <td className="py-3 px-4 text-sm">{product.category}</td>
                    <td className="py-3 px-4 text-sm font-medium">{formatCurrency(product.price, "IQD")}</td>
                    <td className="py-3 px-4 text-sm">{product.stock}</td>
                    <td className="py-3 px-4">
                      <span
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          product.status === "متوفر"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {product.status}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" asChild>
                          <Link href={`/admin/products/${product.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="icon" asChild>
                          <Link href={`/admin/products/${product.id}/edit`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredProducts.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">لا توجد منتجات تطابق البحث</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
