"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/products/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Package,Plus,Search,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/currency */ \"(app-pages-browser)/./src/lib/currency.ts\");\n/* harmony import */ var _lib_products_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/products-store */ \"(app-pages-browser)/./src/lib/products-store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// استخدام نظام إدارة المنتجات المركزي\nfunction ProductsPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الكل\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // تحميل المنتجات عند تحميل الصفحة\n    useEffect({\n        \"ProductsPage.useEffect\": ()=>{\n            loadProducts();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    const loadProducts = ()=>{\n        setIsLoading(true);\n        // محاكاة تحميل البيانات\n        setTimeout(()=>{\n            const allProducts = _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.getAll();\n            setProducts(allProducts);\n            setIsLoading(false);\n        }, 500);\n    };\n    const handleDeleteProduct = async (id)=>{\n        if (!confirm(\"هل أنت متأكد من حذف هذا المنتج؟\")) {\n            return;\n        }\n        setDeletingId(id);\n        try {\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const success = _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.delete(id);\n            if (success) {\n                setProducts((prev)=>prev.filter((p)=>p.id !== id));\n                alert(\"تم حذف المنتج بنجاح!\");\n            } else {\n                alert(\"فشل في حذف المنتج\");\n            }\n        } catch (error) {\n            console.error(\"خطأ في حذف المنتج:\", error);\n            alert(\"حدث خطأ أثناء حذف المنتج\");\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = searchTerm === \"\" || product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) || product.sku.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === \"الكل\" || product.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const categories = [\n        \"الكل\",\n        ...Array.from(new Set(products.map((p)=>p.category)))\n    ];\n    const stats = _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.getStats();\n    const getProductStatus = (product)=>{\n        if (product.status === \"draft\") return \"مسودة\";\n        if (product.status === \"inactive\") return \"غير نشط\";\n        if (product.stock === 0) return \"نفد المخزون\";\n        return \"متوفر\";\n    };\n    const getStatusColor = (product)=>{\n        if (product.status === \"draft\") return \"bg-yellow-100 text-yellow-800\";\n        if (product.status === \"inactive\") return \"bg-gray-100 text-gray-800\";\n        if (product.stock === 0) return \"bg-red-100 text-red-800\";\n        return \"bg-green-100 text-green-800\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"إدارة المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"إدارة وتحديث منتجات المتجر\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/admin/products/settings\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الإعدادات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/admin/products/import-export\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"استيراد/تصدير\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/admin/products/bulk-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"عمليات متعددة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/admin/products/new\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إضافة منتج جديد\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-5 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: stats.total\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"إجمالي المنتجات\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: stats.active\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"نشط\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: stats.draft\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"مسودة\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-red-600\",\n                                    children: stats.outOfStock\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"نفد المخزون\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: stats.totalStock\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"إجمالي المخزون\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            placeholder: \"البحث في المنتجات...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category,\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"فلترة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                            children: [\n                                \"قائمة المنتجات (\",\n                                filteredProducts.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4 font-medium\",\n                                                        children: \"المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4 font-medium\",\n                                                        children: \"رمز المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4 font-medium\",\n                                                        children: \"الفئة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4 font-medium\",\n                                                        children: \"السعر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4 font-medium\",\n                                                        children: \"المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4 font-medium\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4 font-medium\",\n                                                        children: \"الإجراءات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: product.image,\n                                                                        alt: product.name,\n                                                                        width: 40,\n                                                                        height: 40,\n                                                                        className: \"rounded-lg object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: product.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 241,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: product.brand\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 242,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm\",\n                                                            children: product.sku\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm\",\n                                                            children: product.category\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm font-medium\",\n                                                            children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(product.price, product.priceCurrency)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm\",\n                                                            children: product.stock\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(product)),\n                                                                children: getProductStatus(product)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                            href: \"/admin/products/\".concat(product.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                            href: \"/admin/products/\".concat(product.id, \"/edit\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        onClick: ()=>handleDeleteProduct(product.id),\n                                                                        disabled: deletingId === product.id,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-red-500 \".concat(deletingId === product.id ? 'animate-spin' : '')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Package_Plus_Search_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, product.id, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"جاري تحميل المنتجات...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this),\n                            !isLoading && filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: searchTerm || selectedCategory !== \"الكل\" ? \"لا توجد منتجات تطابق البحث\" : \"لا توجد منتجات. ابدأ بإضافة منتج جديد!\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"oPhMP6/+FDMKEC6uE07sUtbiX/A=\");\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/page.tsx\n"));

/***/ })

});