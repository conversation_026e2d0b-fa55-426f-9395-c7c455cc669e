"use client";

import React, { useState, useEffect } from "react";
import { ProductsStore } from "@/lib/products-store";
import { initSampleProducts, cleanDuplicateProducts } from "@/lib/init-sample-products";

export default function DebugProductsPage() {
  const [products, setProducts] = useState<any[]>([]);

  useEffect(() => {
    // تنظيف المنتجات المكررة أولاً
    cleanDuplicateProducts();

    // إضافة منتجات تجريبية
    initSampleProducts();

    // تحميل المنتجات
    const allProducts = ProductsStore.getAll();
    setProducts(allProducts);
  }, []);

  const clearProducts = () => {
    ProductsStore.clear();
    setProducts([]);
  };

  const addSampleProducts = () => {
    ProductsStore.clear(); // مسح المنتجات الموجودة
    initSampleProducts(); // إضافة منتجات جديدة
    const allProducts = ProductsStore.getAll();
    setProducts(allProducts);
  };

  const cleanDuplicates = () => {
    cleanDuplicateProducts();
    const allProducts = ProductsStore.getAll();
    setProducts(allProducts);
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">تصحيح أخطاء المنتجات</h1>
      
      <div className="mb-4 space-x-2">
        <button
          onClick={addSampleProducts}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          إضافة منتجات تجريبية
        </button>
        <button
          onClick={cleanDuplicates}
          className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600"
        >
          تنظيف المنتجات المكررة
        </button>
        <button
          onClick={clearProducts}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          مسح جميع المنتجات
        </button>
      </div>

      <div className="bg-gray-100 p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">المنتجات الحالية ({products.length}):</h2>
        {products.length === 0 ? (
          <p>لا توجد منتجات</p>
        ) : (
          <div className="space-y-2">
            {products.map((product, index) => (
              <div key={product.id} className="bg-white p-3 rounded border">
                <p><strong>#{index + 1}</strong></p>
                <p><strong>المعرف:</strong> {product.id}</p>
                <p><strong>الاسم:</strong> {product.name}</p>
                <p><strong>SKU:</strong> {product.sku}</p>
                <p><strong>رابط التعديل:</strong> 
                  <a 
                    href={`/admin/products/${product.id}/edit`}
                    className="text-blue-500 hover:underline ml-2"
                    target="_blank"
                  >
                    /admin/products/{product.id}/edit
                  </a>
                </p>
                <p><strong>رابط العرض:</strong> 
                  <a 
                    href={`/admin/products/${product.id}`}
                    className="text-green-500 hover:underline ml-2"
                    target="_blank"
                  >
                    /admin/products/{product.id}
                  </a>
                </p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
