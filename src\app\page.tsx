import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ArrowR<PERSON>, Star, Truck, Shield, Clock, CreditCard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import ProductCard from "@/components/product/product-card";

// بيانات وهمية للمنتجات
const featuredProducts = [
  {
    id: "1",
    name: "Acuvue Oasys Daily",
    nameAr: "عدسات أكيوفيو أوازيس اليومية",
    slug: "acuvue-oasys-daily",
    price: 120,
    comparePrice: 150,
    image: "https://picsum.photos/400/400?random=1",
    brand: "Johnson & Johnson",
    rating: 4.8,
    reviewCount: 124,
    isNew: true,
    isFeatured: true,
    inStock: true,
  },
  {
    id: "2",
    name: "Biofinity Monthly",
    nameAr: "عدسات بايوفينيتي الشهرية",
    slug: "biofinity-monthly",
    price: 85,
    comparePrice: 100,
    image: "https://picsum.photos/400/400?random=2",
    brand: "CooperVision",
    rating: 4.6,
    reviewCount: 89,
    isFeatured: true,
    inStock: true,
  },
  {
    id: "3",
    name: "Air Optix Colors",
    nameAr: "عدسات إير أوبتكس الملونة",
    slug: "air-optix-colors",
    price: 95,
    image: "https://picsum.photos/400/400?random=3",
    brand: "Alcon",
    rating: 4.7,
    reviewCount: 156,
    inStock: true,
  },
  {
    id: "4",
    name: "Dailies Total 1",
    nameAr: "عدسات ديليز توتال ون",
    slug: "dailies-total-1",
    price: 140,
    comparePrice: 160,
    image: "https://picsum.photos/400/400?random=4",
    brand: "Alcon",
    rating: 4.9,
    reviewCount: 203,
    isNew: true,
    inStock: true,
  },
];

const categories = [
  {
    id: "1",
    name: "العدسات اليومية",
    image: "https://picsum.photos/300/300?random=5",
    href: "/categories/daily-lenses",
    count: 45,
  },
  {
    id: "2",
    name: "العدسات الشهرية",
    image: "https://picsum.photos/300/300?random=6",
    href: "/categories/monthly-lenses",
    count: 32,
  },
  {
    id: "3",
    name: "العدسات الملونة",
    image: "https://picsum.photos/300/300?random=7",
    href: "/categories/colored-lenses",
    count: 28,
  },
  {
    id: "4",
    name: "النظارات الطبية",
    image: "https://picsum.photos/300/300?random=8",
    href: "/categories/glasses",
    count: 67,
  },
];

export default function Home() {
  return (
    <div className="min-h-screen">
      <Header />

      <main>
        {/* البانر الرئيسي */}
        <section className="relative h-[500px] md:h-[600px] overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/90 to-primary/70 z-10" />
          <Image
            src="https://picsum.photos/1920/600?random=9"
            alt="VisionLens Hero Banner"
            fill
            className="object-cover"
            priority
          />
          <div className="relative z-20 container mx-auto px-4 h-full flex items-center">
            <div className="max-w-2xl text-white">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                عدسات لاصقة
                <br />
                <span className="text-yellow-400">عالية الجودة</span>
              </h1>
              <p className="text-xl mb-8 opacity-90">
                اكتشف مجموعتنا الواسعة من العدسات اللاصقة والنظارات الطبية
                من أفضل العلامات التجارية العالمية
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" variant="secondary" asChild>
                  <Link href="/products">
                    تسوق الآن
                    <ArrowRight className="mr-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-primary" asChild>
                  <Link href="/categories">
                    تصفح الفئات
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* المميزات */}
        <section className="py-16 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 mx-auto mb-4">
                  <Truck className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold mb-2">شحن مجاني</h3>
                <p className="text-muted-foreground">للطلبات أكثر من 200 ريال</p>
              </div>
              <div className="text-center">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 mx-auto mb-4">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold mb-2">ضمان الجودة</h3>
                <p className="text-muted-foreground">منتجات أصلية 100%</p>
              </div>
              <div className="text-center">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 mx-auto mb-4">
                  <Clock className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold mb-2">دعم 24/7</h3>
                <p className="text-muted-foreground">خدمة عملاء متواصلة</p>
              </div>
              <div className="text-center">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 mx-auto mb-4">
                  <CreditCard className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold mb-2">دفع آمن</h3>
                <p className="text-muted-foreground">طرق دفع متعددة</p>
              </div>
            </div>
          </div>
        </section>

        {/* الفئات الرئيسية */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">تسوق حسب الفئة</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                اختر من مجموعة واسعة من العدسات اللاصقة والنظارات الطبية
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {categories.map((category) => (
                <Link key={category.id} href={category.href}>
                  <Card className="group overflow-hidden hover:shadow-lg transition-all duration-300">
                    <CardContent className="p-0">
                      <div className="relative aspect-square overflow-hidden">
                        <Image
                          src={category.image}
                          alt={category.name}
                          fill
                          className="object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors" />
                        <div className="absolute inset-0 flex flex-col justify-end p-6 text-white">
                          <h3 className="text-xl font-semibold mb-2">{category.name}</h3>
                          <p className="text-sm opacity-90">{category.count} منتج</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </section>

        {/* المنتجات المميزة */}
        <section className="py-16 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl font-bold mb-4">المنتجات المميزة</h2>
                <p className="text-muted-foreground">
                  أفضل العدسات اللاصقة من العلامات التجارية الرائدة
                </p>
              </div>
              <Button variant="outline" asChild>
                <Link href="/products">
                  عرض الكل
                  <ArrowRight className="mr-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        </section>

        {/* قسم العروض */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-8 md:p-12 text-white text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                عروض خاصة هذا الأسبوع
              </h2>
              <p className="text-xl mb-8 opacity-90">
                خصم يصل إلى 30% على جميع العدسات اليومية
              </p>
              <Button size="lg" variant="secondary">
                اكتشف العروض
              </Button>
            </div>
          </div>
        </section>

        {/* قسم التقييمات */}
        <section className="py-16 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">ماذا يقول عملاؤنا</h2>
              <p className="text-muted-foreground">
                آراء حقيقية من عملائنا الكرام
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[1, 2, 3].map((i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="flex mb-4">
                      {[...Array(5)].map((_, j) => (
                        <Star key={j} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-muted-foreground mb-4">
                      "منتجات عالية الجودة وخدمة عملاء ممتازة. أنصح بشدة بالتسوق من VisionLens"
                    </p>
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-sm font-semibold">أ</span>
                      </div>
                      <div>
                        <p className="font-semibold">أحمد محمد</p>
                        <p className="text-sm text-muted-foreground">عميل مميز</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
