"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useTheme } from "next-themes";
import {
  Search,
  ShoppingCart,
  User,
  Heart,
  Menu,
  X,
  Sun,
  Moon,
  Phone,
  Mail,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { theme, setTheme } = useTheme();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* شريط المعلومات العلوي */}
      <div className="bg-primary text-primary-foreground">
        <div className="container mx-auto px-4">
          <div className="flex h-10 items-center justify-between text-sm">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span>+966 50 123 4567</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>
            <div className="hidden md:flex items-center gap-4">
              <span>شحن مجاني للطلبات أكثر من 200 ريال</span>
            </div>
          </div>
        </div>
      </div>

      {/* الهيدر الرئيسي */}
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* اللوجو */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <span className="text-xl font-bold">V</span>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-primary">VisionLens</h1>
              <p className="text-xs text-muted-foreground">عدسات وأكثر</p>
            </div>
          </Link>

          {/* شريط البحث */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="ابحث عن العدسات والنظارات..."
                className="w-full pr-10"
              />
            </div>
          </div>

          {/* أيقونات التنقل */}
          <div className="flex items-center gap-2">
            {/* البحث للموبايل */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={toggleSearch}
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* تبديل الثيم */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            </Button>

            {/* المفضلة */}
            <Button variant="ghost" size="icon" asChild>
              <Link href="/wishlist">
                <Heart className="h-5 w-5" />
              </Link>
            </Button>

            {/* سلة التسوق */}
            <Button variant="ghost" size="icon" className="relative" asChild>
              <Link href="/cart">
                <ShoppingCart className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-primary text-xs text-primary-foreground flex items-center justify-center">
                  3
                </span>
              </Link>
            </Button>

            {/* الحساب */}
            <Button variant="ghost" size="icon" asChild>
              <Link href="/account">
                <User className="h-5 w-5" />
              </Link>
            </Button>

            {/* لوحة المدير (مؤقت للتطوير) */}
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin">
                لوحة المدير
              </Link>
            </Button>

            {/* قائمة الموبايل */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* شريط البحث للموبايل */}
        {isSearchOpen && (
          <div className="md:hidden py-4 border-t">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="ابحث عن العدسات والنظارات..."
                className="w-full pr-10"
              />
            </div>
          </div>
        )}
      </div>

      {/* التنقل الرئيسي */}
      <nav className="border-t">
        <div className="container mx-auto px-4">
          <div className="hidden md:flex h-12 items-center justify-center space-x-8">
            <Link
              href="/"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              الرئيسية
            </Link>
            <Link
              href="/products"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              جميع المنتجات
            </Link>
            <Link
              href="/categories/contact-lenses"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              العدسات اللاصقة
            </Link>
            <Link
              href="/categories/glasses"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              النظارات الطبية
            </Link>
            <Link
              href="/categories/colored-lenses"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              العدسات الملونة
            </Link>
            <Link
              href="/brands"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              العلامات التجارية
            </Link>
            <Link
              href="/offers"
              className="text-sm font-medium transition-colors hover:text-primary text-red-600"
            >
              العروض الخاصة
            </Link>
            <Link
              href="/contact"
              className="text-sm font-medium transition-colors hover:text-primary"
            >
              تواصل معنا
            </Link>
          </div>

          {/* قائمة الموبايل */}
          {isMenuOpen && (
            <div className="md:hidden py-4 space-y-2">
              <Link
                href="/"
                className="block py-2 text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setIsMenuOpen(false)}
              >
                الرئيسية
              </Link>
              <Link
                href="/products"
                className="block py-2 text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setIsMenuOpen(false)}
              >
                جميع المنتجات
              </Link>
              <Link
                href="/categories/contact-lenses"
                className="block py-2 text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setIsMenuOpen(false)}
              >
                العدسات اللاصقة
              </Link>
              <Link
                href="/categories/glasses"
                className="block py-2 text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setIsMenuOpen(false)}
              >
                النظارات الطبية
              </Link>
              <Link
                href="/categories/colored-lenses"
                className="block py-2 text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setIsMenuOpen(false)}
              >
                العدسات الملونة
              </Link>
              <Link
                href="/brands"
                className="block py-2 text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setIsMenuOpen(false)}
              >
                العلامات التجارية
              </Link>
              <Link
                href="/offers"
                className="block py-2 text-sm font-medium transition-colors hover:text-primary text-red-600"
                onClick={() => setIsMenuOpen(false)}
              >
                العروض الخاصة
              </Link>
              <Link
                href="/contact"
                className="block py-2 text-sm font-medium transition-colors hover:text-primary"
                onClick={() => setIsMenuOpen(false)}
              >
                تواصل معنا
              </Link>
            </div>
          )}
        </div>
      </nav>
    </header>
  );
}
