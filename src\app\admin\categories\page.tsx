"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Upload,
  Tag,
  Package,
  MoreHorizontal,
  Grid,
  List,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// بيانات وهمية للفئات
const categories = [
  {
    id: "1",
    name: "العدسات اليومية",
    nameEn: "Daily Lenses",
    description: "عدسات لاصقة يومية مريحة وآمنة",
    slug: "daily-lenses",
    image: "https://picsum.photos/100/100?random=10",
    productCount: 45,
    isActive: true,
    sortOrder: 1,
    createdAt: "2024-01-01",
  },
  {
    id: "2",
    name: "العدسات الشهرية",
    nameEn: "Monthly Lenses",
    description: "عدسات لاصقة شهرية اقتصادية",
    slug: "monthly-lenses",
    image: "https://picsum.photos/100/100?random=11",
    productCount: 32,
    isActive: true,
    sortOrder: 2,
    createdAt: "2024-01-01",
  },
  {
    id: "3",
    name: "العدسات الملونة",
    nameEn: "Colored Lenses",
    description: "عدسات ملونة لإطلالة مميزة",
    slug: "colored-lenses",
    image: "https://picsum.photos/100/100?random=12",
    productCount: 28,
    isActive: true,
    sortOrder: 3,
    createdAt: "2024-01-01",
  },
  {
    id: "4",
    name: "النظارات الطبية",
    nameEn: "Medical Glasses",
    description: "نظارات طبية عالية الجودة",
    slug: "medical-glasses",
    image: "https://picsum.photos/100/100?random=13",
    productCount: 67,
    isActive: true,
    sortOrder: 4,
    createdAt: "2024-01-01",
  },
];

// بيانات وهمية للعلامات التجارية
const brands = [
  {
    id: "1",
    name: "Johnson & Johnson",
    nameAr: "جونسون آند جونسون",
    description: "شركة رائدة في مجال العدسات اللاصقة",
    logo: "https://picsum.photos/80/80?random=20",
    website: "https://www.jnj.com",
    productCount: 25,
    isActive: true,
    createdAt: "2024-01-01",
  },
  {
    id: "2",
    name: "Alcon",
    nameAr: "ألكون",
    description: "متخصصة في منتجات العناية بالعيون",
    logo: "https://picsum.photos/80/80?random=21",
    website: "https://www.alcon.com",
    productCount: 32,
    isActive: true,
    createdAt: "2024-01-01",
  },
  {
    id: "3",
    name: "CooperVision",
    nameAr: "كوبر فيجن",
    description: "مبتكرة في تقنيات العدسات اللاصقة",
    logo: "https://picsum.photos/80/80?random=22",
    website: "https://coopervision.com",
    productCount: 18,
    isActive: true,
    createdAt: "2024-01-01",
  },
  {
    id: "4",
    name: "Bausch & Lomb",
    nameAr: "بوش آند لومب",
    description: "رائدة في مجال صحة العيون",
    logo: "https://picsum.photos/80/80?random=23",
    website: "https://www.bausch.com",
    productCount: 22,
    isActive: true,
    createdAt: "2024-01-01",
  },
];

export default function CategoriesPage() {
  const [activeTab, setActiveTab] = useState("categories");
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showAddForm, setShowAddForm] = useState(false);

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.nameEn.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredBrands = brands.filter(brand =>
    brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    brand.nameAr.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">الفئات والعلامات التجارية</h1>
          <p className="text-gray-600 mt-1">إدارة فئات المنتجات والعلامات التجارية</p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          إضافة {activeTab === "categories" ? "فئة" : "علامة تجارية"}
        </Button>
      </div>

      {/* التبويبات */}
      <div className="border-b">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab("categories")}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "categories"
                ? "border-primary text-primary"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              الفئات ({categories.length})
            </div>
          </button>
          <button
            onClick={() => setActiveTab("brands")}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "brands"
                ? "border-primary text-primary"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            <div className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              العلامات التجارية ({brands.length})
            </div>
          </button>
        </nav>
      </div>

      {/* أدوات البحث والعرض */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={`البحث في ${activeTab === "categories" ? "الفئات" : "العلامات التجارية"}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
              >
                {viewMode === "grid" ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* محتوى الفئات */}
      {activeTab === "categories" && (
        <div>
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredCategories.map((category) => (
                <Card key={category.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative aspect-square">
                    <Image
                      src={category.image}
                      alt={category.name}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-2 right-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        category.isActive 
                          ? "bg-green-100 text-green-800" 
                          : "bg-gray-100 text-gray-800"
                      }`}>
                        {category.isActive ? "نشط" : "غير نشط"}
                      </span>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-1">{category.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">{category.nameEn}</p>
                    <p className="text-xs text-gray-500 mb-3 line-clamp-2">{category.description}</p>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm text-gray-600">{category.productCount} منتج</span>
                      <span className="text-xs text-gray-500">#{category.sortOrder}</span>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-3 w-3 mr-1" />
                        تعديل
                      </Button>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-3 w-3 text-red-500" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-right py-3 px-4 font-medium">الفئة</th>
                        <th className="text-right py-3 px-4 font-medium">الوصف</th>
                        <th className="text-right py-3 px-4 font-medium">المنتجات</th>
                        <th className="text-right py-3 px-4 font-medium">الترتيب</th>
                        <th className="text-right py-3 px-4 font-medium">الحالة</th>
                        <th className="text-right py-3 px-4 font-medium">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredCategories.map((category) => (
                        <tr key={category.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="flex items-center gap-3">
                              <Image
                                src={category.image}
                                alt={category.name}
                                width={40}
                                height={40}
                                className="rounded-lg object-cover"
                              />
                              <div>
                                <p className="font-medium">{category.name}</p>
                                <p className="text-sm text-gray-500">{category.nameEn}</p>
                              </div>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-sm text-gray-600 max-w-xs">
                            <p className="line-clamp-2">{category.description}</p>
                          </td>
                          <td className="py-3 px-4 text-sm">{category.productCount}</td>
                          <td className="py-3 px-4 text-sm">{category.sortOrder}</td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              category.isActive 
                                ? "bg-green-100 text-green-800" 
                                : "bg-gray-100 text-gray-800"
                            }`}>
                              {category.isActive ? "نشط" : "غير نشط"}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="icon">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* محتوى العلامات التجارية */}
      {activeTab === "brands" && (
        <div>
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredBrands.map((brand) => (
                <Card key={brand.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <Image
                        src={brand.logo}
                        alt={brand.name}
                        width={60}
                        height={60}
                        className="rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <h3 className="font-semibold">{brand.name}</h3>
                        <p className="text-sm text-gray-600">{brand.nameAr}</p>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mb-3 line-clamp-2">{brand.description}</p>
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm text-gray-600">{brand.productCount} منتج</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        brand.isActive 
                          ? "bg-green-100 text-green-800" 
                          : "bg-gray-100 text-gray-800"
                      }`}>
                        {brand.isActive ? "نشط" : "غير نشط"}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="h-3 w-3 mr-1" />
                        تعديل
                      </Button>
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-3 w-3 text-red-500" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-right py-3 px-4 font-medium">العلامة التجارية</th>
                        <th className="text-right py-3 px-4 font-medium">الوصف</th>
                        <th className="text-right py-3 px-4 font-medium">الموقع</th>
                        <th className="text-right py-3 px-4 font-medium">المنتجات</th>
                        <th className="text-right py-3 px-4 font-medium">الحالة</th>
                        <th className="text-right py-3 px-4 font-medium">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredBrands.map((brand) => (
                        <tr key={brand.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="flex items-center gap-3">
                              <Image
                                src={brand.logo}
                                alt={brand.name}
                                width={40}
                                height={40}
                                className="rounded-lg object-cover"
                              />
                              <div>
                                <p className="font-medium">{brand.name}</p>
                                <p className="text-sm text-gray-500">{brand.nameAr}</p>
                              </div>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-sm text-gray-600 max-w-xs">
                            <p className="line-clamp-2">{brand.description}</p>
                          </td>
                          <td className="py-3 px-4 text-sm">
                            <a href={brand.website} target="_blank" className="text-primary hover:underline">
                              زيارة الموقع
                            </a>
                          </td>
                          <td className="py-3 px-4 text-sm">{brand.productCount}</td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              brand.isActive 
                                ? "bg-green-100 text-green-800" 
                                : "bg-gray-100 text-gray-800"
                            }`}>
                              {brand.isActive ? "نشط" : "غير نشط"}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="icon">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6 text-center">
            <Package className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{categories.length}</div>
            <p className="text-sm text-gray-600">إجمالي الفئات</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <Tag className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{brands.length}</div>
            <p className="text-sm text-gray-600">العلامات التجارية</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {categories.reduce((sum, cat) => sum + cat.productCount, 0)}
            </div>
            <p className="text-sm text-gray-600">إجمالي المنتجات</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {categories.filter(cat => cat.isActive).length}
            </div>
            <p className="text-sm text-gray-600">فئات نشطة</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
