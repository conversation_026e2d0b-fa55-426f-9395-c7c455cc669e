"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/new/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/admin/products/new/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/currency */ \"(app-pages-browser)/./src/lib/currency.ts\");\n/* harmony import */ var _lib_products_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/products-store */ \"(app-pages-browser)/./src/lib/products-store.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NewProductPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // معلومات أساسية\n        name: \"\",\n        nameEn: \"\",\n        description: \"\",\n        shortDescription: \"\",\n        sku: \"\",\n        barcode: \"\",\n        // التصنيف\n        category: \"\",\n        brand: \"\",\n        tags: [],\n        // الأسعار\n        price: \"\",\n        priceCurrency: \"IQD\",\n        comparePrice: \"\",\n        comparePriceCurrency: \"IQD\",\n        cost: \"\",\n        costCurrency: \"USD\",\n        // المخزون\n        trackQuantity: true,\n        quantity: \"\",\n        minQuantity: \"\",\n        maxQuantity: \"\",\n        location: \"\",\n        // الشحن\n        weight: \"\",\n        dimensions: {\n            length: \"\",\n            width: \"\",\n            height: \"\"\n        },\n        // SEO\n        metaTitle: \"\",\n        metaDescription: \"\",\n        slug: \"\",\n        // الحالة\n        status: \"draft\",\n        featured: false,\n        allowBackorder: false,\n        // المواصفات\n        specifications: [],\n        // الصور\n        images: []\n    });\n    const [currentTag, setCurrentTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImages, setPreviewImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCurrencyConverter, setShowCurrencyConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profitCalculation, setProfitCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isImageUploadMode, setIsImageUploadMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const categories = [\n        \"العدسات اليومية\",\n        \"العدسات الشهرية\",\n        \"العدسات الملونة\",\n        \"العدسات الأسبوعية\",\n        \"النظارات الطبية\",\n        \"النظارات الشمسية\",\n        \"الإكسسوارات\"\n    ];\n    const brands = [\n        \"Johnson & Johnson\",\n        \"Alcon\",\n        \"CooperVision\",\n        \"Bausch & Lomb\",\n        \"Ray-Ban\",\n        \"Oakley\"\n    ];\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // إزالة الخطأ عند التعديل\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n        // توليد slug تلقائياً من الاسم\n        if (field === \"name\" && value) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n        // حساب الربح عند تغيير الأسعار\n        if (field === \"price\" || field === \"cost\" || field === \"priceCurrency\" || field === \"costCurrency\") {\n            setTimeout(()=>calculateProfit(), 0);\n        }\n    };\n    const calculateProfit = ()=>{\n        if (formData.price && formData.cost) {\n            try {\n                const profit = (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.calculateProfitInIQD)(parseFloat(formData.cost), formData.costCurrency, parseFloat(formData.price), formData.priceCurrency);\n                setProfitCalculation(profit);\n            } catch (error) {\n                console.error(\"خطأ في حساب الربح:\", error);\n                setProfitCalculation(null);\n            }\n        } else {\n            setProfitCalculation(null);\n        }\n    };\n    const handleDimensionChange = (dimension, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                dimensions: {\n                    ...prev.dimensions,\n                    [dimension]: value\n                }\n            }));\n    };\n    const addTag = ()=>{\n        if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        currentTag.trim()\n                    ]\n                }));\n            setCurrentTag(\"\");\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const addSpecification = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: [\n                    ...prev.specifications,\n                    {\n                        key: \"\",\n                        value: \"\"\n                    }\n                ]\n            }));\n    };\n    const updateSpecification = (index, field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.map((spec, i)=>i === index ? {\n                        ...spec,\n                        [field]: value\n                    } : spec)\n            }));\n    };\n    const removeSpecification = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        // التأكد من أننا في وضع رفع الصور وليس إرسال النموذج\n        setIsImageUploadMode(true);\n        setUploadingImages(true);\n        const newImages = [];\n        const newPreviews = [];\n        try {\n            for(let i = 0; i < files.length; i++){\n                const file = files[i];\n                // التحقق من نوع الملف\n                if (!file.type.startsWith('image/')) {\n                    alert(\"الملف \".concat(file.name, \" ليس صورة صالحة\"));\n                    continue;\n                }\n                // التحقق من حجم الملف (5MB كحد أقصى)\n                if (file.size > 5 * 1024 * 1024) {\n                    alert(\"الملف \".concat(file.name, \" كبير جداً. الحد الأقصى 5MB\"));\n                    continue;\n                }\n                // إنشاء معاينة محلية\n                const preview = URL.createObjectURL(file);\n                newPreviews.push(preview);\n                // محاكاة رفع الصورة\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // في التطبيق الحقيقي، سيتم رفع الصورة إلى الخادم\n                const imageUrl = \"https://picsum.photos/400/400?random=\".concat(Date.now(), \"-\").concat(i);\n                newImages.push(imageUrl);\n            }\n            setFormData((prev)=>({\n                    ...prev,\n                    images: [\n                        ...prev.images,\n                        ...newImages\n                    ]\n                }));\n            setPreviewImages((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n            if (newImages.length > 0) {\n                alert(\"تم رفع \".concat(newImages.length, \" صورة بنجاح!\"));\n            }\n        } catch (error) {\n            console.error(\"Error uploading images:\", error);\n            alert(\"حدث خطأ أثناء رفع الصور\");\n        } finally{\n            setUploadingImages(false);\n            setIsImageUploadMode(false);\n            // إعادة تعيين قيمة input\n            if (event.target) {\n                event.target.value = '';\n            }\n        }\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n        setPreviewImages((prev)=>{\n            const newPreviews = prev.filter((_, i)=>i !== index);\n            // تنظيف URL المؤقت\n            if (prev[index]) {\n                URL.revokeObjectURL(prev[index]);\n            }\n            return newPreviews;\n        });\n    };\n    const duplicateProduct = ()=>{\n        const duplicatedData = {\n            ...formData,\n            name: \"\".concat(formData.name, \" - نسخة\"),\n            nameEn: \"\".concat(formData.nameEn, \" - Copy\"),\n            sku: \"\".concat(formData.sku, \"-COPY\"),\n            slug: \"\".concat(formData.slug, \"-copy\")\n        };\n        setFormData(duplicatedData);\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) newErrors.name = \"اسم المنتج مطلوب\";\n        if (!formData.nameEn.trim()) newErrors.nameEn = \"الاسم الإنجليزي مطلوب\";\n        if (!formData.description.trim()) newErrors.description = \"الوصف مطلوب\";\n        if (!formData.sku.trim()) newErrors.sku = \"رمز المنتج مطلوب\";\n        if (!formData.category) newErrors.category = \"الفئة مطلوبة\";\n        if (!formData.brand) newErrors.brand = \"العلامة التجارية مطلوبة\";\n        if (!formData.price || parseFloat(formData.price) <= 0) {\n            newErrors.price = \"السعر مطلوب ويجب أن يكون أكبر من صفر\";\n        }\n        if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {\n            newErrors.quantity = \"الكمية مطلوبة\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async function(e) {\n        let status = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"draft\";\n        e.preventDefault();\n        // التأكد من أننا لسنا في وضع رفع الصور\n        if (isImageUploadMode) {\n            return;\n        }\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // إعداد بيانات المنتج\n            const productData = {\n                name: formData.name,\n                nameEn: formData.nameEn,\n                description: formData.description,\n                shortDescription: formData.shortDescription,\n                sku: formData.sku || _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.generateSku(),\n                barcode: formData.barcode,\n                category: formData.category,\n                brand: formData.brand,\n                tags: formData.tags,\n                price: parseFloat(formData.price),\n                priceCurrency: formData.priceCurrency,\n                comparePrice: formData.comparePrice ? parseFloat(formData.comparePrice) : undefined,\n                comparePriceCurrency: formData.comparePriceCurrency,\n                cost: formData.cost ? parseFloat(formData.cost) : undefined,\n                costCurrency: formData.costCurrency,\n                stock: formData.trackQuantity ? parseInt(formData.quantity) : 0,\n                minQuantity: formData.minQuantity ? parseInt(formData.minQuantity) : undefined,\n                maxQuantity: formData.maxQuantity ? parseInt(formData.maxQuantity) : undefined,\n                trackQuantity: formData.trackQuantity,\n                allowBackorder: formData.allowBackorder,\n                status: status,\n                image: formData.images[0] || \"https://picsum.photos/400/400?random=default\",\n                images: formData.images.length > 0 ? formData.images : [\n                    \"https://picsum.photos/400/400?random=default\"\n                ],\n                specifications: formData.specifications.filter((spec)=>spec.key && spec.value),\n                weight: formData.weight ? parseFloat(formData.weight) : undefined,\n                dimensions: formData.dimensions.length && formData.dimensions.width && formData.dimensions.height ? {\n                    length: parseFloat(formData.dimensions.length),\n                    width: parseFloat(formData.dimensions.width),\n                    height: parseFloat(formData.dimensions.height)\n                } : undefined,\n                metaTitle: formData.metaTitle,\n                metaDescription: formData.metaDescription,\n                slug: formData.slug || _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.generateSlug(formData.name, formData.sku),\n                featured: formData.featured,\n                location: formData.location\n            };\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // إضافة المنتج إلى المتجر\n            const newProduct = _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.add(productData);\n            console.log(\"Product created:\", newProduct);\n            const statusText = status === \"active\" ? \"ونشر\" : \"كمسودة\";\n            alert(\"تم إضافة المنتج \".concat(statusText, \" بنجاح!\"));\n            // إعادة توجيه إلى صفحة المنتجات\n            router.push(\"/admin/products\");\n        } catch (error) {\n            console.error(\"Error creating product:\", error);\n            alert(\"حدث خطأ أثناء إضافة المنتج\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSaveAsDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: \"draft\"\n            }));\n        handleSubmit(new Event(\"submit\"));\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: \"active\"\n            }));\n        handleSubmit(new Event(\"submit\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إضافة منتج جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"إنشاء منتج جديد في المتجر\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: duplicateProduct,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نسخ المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleSaveAsDraft,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حفظ كمسودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handlePublish,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نشر المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (عربي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.name,\n                                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                placeholder: \"أدخل اسم المنتج\",\n                                                                className: errors.name ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (إنجليزي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.nameEn,\n                                                                onChange: (e)=>handleInputChange(\"nameEn\", e.target.value),\n                                                                placeholder: \"Product Name in English\",\n                                                                className: errors.nameEn ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.nameEn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.nameEn\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف المختصر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.shortDescription,\n                                                        onChange: (e)=>handleInputChange(\"shortDescription\", e.target.value),\n                                                        placeholder: \"وصف مختصر للمنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف التفصيلي *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                        placeholder: \"وصف تفصيلي للمنتج...\",\n                                                        rows: 4,\n                                                        className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.description ? \"border-red-500\" : \"border-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-xs mt-1\",\n                                                        children: errors.description\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"رمز المنتج (SKU) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.sku,\n                                                                onChange: (e)=>handleInputChange(\"sku\", e.target.value),\n                                                                placeholder: \"PRD-001\",\n                                                                className: errors.sku ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.sku && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.sku\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الباركود\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.barcode,\n                                                                onChange: (e)=>handleInputChange(\"barcode\", e.target.value),\n                                                                placeholder: \"1234567890123\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"التصنيف\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الفئة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.category,\n                                                                onChange: (e)=>handleInputChange(\"category\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.category ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر الفئة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category,\n                                                                            children: category\n                                                                        }, category, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.category\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.brand,\n                                                                onChange: (e)=>handleInputChange(\"brand\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.brand ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر العلامة التجارية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: brand,\n                                                                            children: brand\n                                                                        }, brand, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.brand\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"العلامات (Tags)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: currentTag,\n                                                                onChange: (e)=>setCurrentTag(e.target.value),\n                                                                placeholder: \"أضف علامة\",\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && (e.preventDefault(), addTag())\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addTag,\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-sm\",\n                                                                children: [\n                                                                    tag,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>removeTag(tag),\n                                                                        className: \"hover:text-red-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الأسعار والعملات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"سعر البيع *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                value: formData.price,\n                                                                onChange: (e)=>handleInputChange(\"price\", e.target.value),\n                                                                placeholder: \"0.00\",\n                                                                className: errors.price ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.priceCurrency,\n                                                                onChange: (e)=>handleInputChange(\"priceCurrency\", e.target.value),\n                                                                className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: _lib_currency__WEBPACK_IMPORTED_MODULE_7__.SUPPORTED_CURRENCIES.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: currency.code,\n                                                                        children: [\n                                                                            currency.nameAr,\n                                                                            \" (\",\n                                                                            currency.symbolAr,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, currency.code, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 661,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-xs mt-1\",\n                                                        children: errors.price\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.price && formData.priceCurrency !== \"IQD\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-600 mt-1\",\n                                                        children: [\n                                                            \"= \",\n                                                            (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)((0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.convertCurrency)(parseFloat(formData.price), formData.priceCurrency, \"IQD\"), \"IQD\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"سعر التكلفة (للاستيراد)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                value: formData.cost,\n                                                                onChange: (e)=>handleInputChange(\"cost\", e.target.value),\n                                                                placeholder: \"0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.costCurrency,\n                                                                onChange: (e)=>handleInputChange(\"costCurrency\", e.target.value),\n                                                                className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: _lib_currency__WEBPACK_IMPORTED_MODULE_7__.SUPPORTED_CURRENCIES.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: currency.code,\n                                                                        children: [\n                                                                            currency.nameAr,\n                                                                            \" (\",\n                                                                            currency.symbolAr,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, currency.code, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    formData.cost && formData.costCurrency !== \"IQD\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-600 mt-1\",\n                                                        children: [\n                                                            \"= \",\n                                                            (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)((0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.convertCurrency)(parseFloat(formData.cost), formData.costCurrency, \"IQD\"), \"IQD\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"السعر المقارن (اختياري)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                value: formData.comparePrice,\n                                                                onChange: (e)=>handleInputChange(\"comparePrice\", e.target.value),\n                                                                placeholder: \"0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.comparePriceCurrency,\n                                                                onChange: (e)=>handleInputChange(\"comparePriceCurrency\", e.target.value),\n                                                                className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: _lib_currency__WEBPACK_IMPORTED_MODULE_7__.SUPPORTED_CURRENCIES.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: currency.code,\n                                                                        children: [\n                                                                            currency.nameAr,\n                                                                            \" (\",\n                                                                            currency.symbolAr,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, currency.code, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    formData.comparePrice && formData.comparePriceCurrency !== \"IQD\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-600 mt-1\",\n                                                        children: [\n                                                            \"= \",\n                                                            (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)((0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.convertCurrency)(parseFloat(formData.comparePrice), formData.comparePriceCurrency, \"IQD\"), \"IQD\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 15\n                                            }, this),\n                                            profitCalculation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-green-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-green-800 mb-3\",\n                                                        children: \"حساب الربح (بالدينار العراقي)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"التكلفة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 747,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(profitCalculation.costInIQD, \"IQD\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"سعر البيع\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(profitCalculation.sellingPriceInIQD, \"IQD\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"الربح\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold \".concat(profitCalculation.profitInIQD >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                                        children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(profitCalculation.profitInIQD, \"IQD\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"هامش الربح\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold \".concat(profitCalculation.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                                        children: [\n                                                                            profitCalculation.profitMargin.toFixed(2),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"إدارة المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"trackQuantity\",\n                                                        checked: formData.trackQuantity,\n                                                        onChange: (e)=>handleInputChange(\"trackQuantity\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"trackQuantity\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"تتبع الكمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.trackQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الكمية الحالية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.quantity,\n                                                                onChange: (e)=>handleInputChange(\"quantity\", e.target.value),\n                                                                placeholder: \"0\",\n                                                                className: errors.quantity ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            errors.quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأدنى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.minQuantity,\n                                                                onChange: (e)=>handleInputChange(\"minQuantity\", e.target.value),\n                                                                placeholder: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأقصى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.maxQuantity,\n                                                                onChange: (e)=>handleInputChange(\"maxQuantity\", e.target.value),\n                                                                placeholder: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الموقع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.location,\n                                                                onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                                placeholder: \"A1-B2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"allowBackorder\",\n                                                        checked: formData.allowBackorder,\n                                                        onChange: (e)=>handleInputChange(\"allowBackorder\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"allowBackorder\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"السماح بالطلب المسبق عند نفاد المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"المواصفات التقنية\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: formData.specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                placeholder: \"المواصفة\",\n                                                                value: spec.key,\n                                                                onChange: (e)=>updateSpecification(index, \"key\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                placeholder: \"القيمة\",\n                                                                value: spec.value,\n                                                                onChange: (e)=>updateSpecification(index, \"value\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>removeSpecification(index),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: addSpecification,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"إضافة مواصفة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"حالة المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"draft\",\n                                                                children: \"مسودة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"active\",\n                                                                children: \"نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"inactive\",\n                                                                children: \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"featured\",\n                                                        checked: formData.featured,\n                                                        onChange: (e)=>handleInputChange(\"featured\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"featured\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 908,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"صور المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                        children: \"اسحب الصور هنا أو انقر للتحديد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        multiple: true,\n                                                        accept: \"image/*\",\n                                                        onChange: handleImageUpload,\n                                                        className: \"hidden\",\n                                                        id: \"image-upload\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>{\n                                                            var _document_getElementById;\n                                                            return (_document_getElementById = document.getElementById('image-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                        },\n                                                        disabled: uploadingImages,\n                                                        children: uploadingImages ? \"جاري الرفع...\" : \"اختيار الصور\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.images.length > 0 || previewImages.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-2\",\n                                                children: formData.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square relative rounded-lg overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: previewImages[index] || image,\n                                                                    alt: \"صورة \".concat(index + 1),\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeImage(index),\n                                                                className: \"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded\",\n                                                                children: \"الصورة الرئيسية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 973,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 971,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 941,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"معلومات الشحن\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوزن (جرام)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        type: \"number\",\n                                                        value: formData.weight,\n                                                        onChange: (e)=>handleInputChange(\"weight\", e.target.value),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1011,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1007,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الأبعاد (سم)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1020,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.length,\n                                                                onChange: (e)=>handleDimensionChange(\"length\", e.target.value),\n                                                                placeholder: \"طول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 1024,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.width,\n                                                                onChange: (e)=>handleDimensionChange(\"width\", e.target.value),\n                                                                placeholder: \"عرض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.height,\n                                                                onChange: (e)=>handleDimensionChange(\"height\", e.target.value),\n                                                                placeholder: \"ارتفاع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 1051,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"تحسين محركات البحث\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الرابط (Slug)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.slug,\n                                                        onChange: (e)=>handleInputChange(\"slug\", e.target.value),\n                                                        placeholder: \"product-slug\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1060,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1068,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.metaTitle,\n                                                        onChange: (e)=>handleInputChange(\"metaTitle\", e.target.value),\n                                                        placeholder: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1071,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1067,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"وصف الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>handleInputChange(\"metaDescription\", e.target.value),\n                                                        placeholder: \"وصف الصفحة لمحركات البحث\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 1048,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 906,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 435,\n                columnNumber: 7\n            }, this),\n            Object.keys(errors).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"يرجى تصحيح الأخطاء التالية:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 1101,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 1099,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 text-sm text-red-700 list-disc list-inside\",\n                            children: Object.values(errors).map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: error\n                                }, index, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 1105,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 1103,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                    lineNumber: 1098,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 1097,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n        lineNumber: 404,\n        columnNumber: 5\n    }, this);\n}\n_s(NewProductPage, \"IGX/01kNojoTO2iCaCdmn70tBhg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter\n    ];\n});\n_c = NewProductPage;\nvar _c;\n$RefreshReg$(_c, \"NewProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/new/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/products-store.ts":
/*!***********************************!*\
  !*** ./src/lib/products-store.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductsStore: () => (/* binding */ ProductsStore)\n/* harmony export */ });\n// نظام إدارة حالة المنتجات المركزي\n// بيانات وهمية للمنتجات\nlet products = [\n    {\n        id: \"1\",\n        name: \"عدسات أكيوفيو اليومية\",\n        nameEn: \"Acuvue Oasys Daily\",\n        sku: \"ACU-001\",\n        category: \"العدسات اليومية\",\n        brand: \"Johnson & Johnson\",\n        price: 120000,\n        priceCurrency: \"IQD\",\n        cost: 80000,\n        costCurrency: \"IQD\",\n        stock: 45,\n        status: \"active\",\n        image: \"https://picsum.photos/400/400?random=1\",\n        images: [\n            \"https://picsum.photos/400/400?random=1\"\n        ],\n        description: \"عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي\",\n        tags: [\n            \"يومية\",\n            \"مريحة\",\n            \"آمنة\"\n        ],\n        specifications: [\n            {\n                key: \"النوع\",\n                value: \"عدسات يومية\"\n            },\n            {\n                key: \"المادة\",\n                value: \"سيليكون هيدروجيل\"\n            }\n        ],\n        slug: \"acuvue-oasys-daily\",\n        featured: true,\n        allowBackorder: false,\n        trackQuantity: true,\n        createdAt: \"2024-01-10T10:00:00Z\",\n        updatedAt: \"2024-01-10T10:00:00Z\"\n    },\n    {\n        id: \"2\",\n        name: \"عدسات بايوفينيتي الشهرية\",\n        nameEn: \"Biofinity Monthly\",\n        sku: \"BIO-002\",\n        category: \"العدسات الشهرية\",\n        brand: \"CooperVision\",\n        price: 85000,\n        priceCurrency: \"IQD\",\n        cost: 55000,\n        costCurrency: \"IQD\",\n        stock: 32,\n        status: \"active\",\n        image: \"https://picsum.photos/400/400?random=2\",\n        images: [\n            \"https://picsum.photos/400/400?random=2\"\n        ],\n        description: \"عدسات لاصقة شهرية عالية الجودة\",\n        tags: [\n            \"شهرية\",\n            \"جودة عالية\"\n        ],\n        specifications: [\n            {\n                key: \"النوع\",\n                value: \"عدسات شهرية\"\n            },\n            {\n                key: \"المادة\",\n                value: \"سيليكون هيدروجيل\"\n            }\n        ],\n        slug: \"biofinity-monthly\",\n        featured: false,\n        allowBackorder: false,\n        trackQuantity: true,\n        createdAt: \"2024-01-08T10:00:00Z\",\n        updatedAt: \"2024-01-08T10:00:00Z\"\n    },\n    {\n        id: \"3\",\n        name: \"عدسات إير أوبتكس الملونة\",\n        nameEn: \"Air Optix Colors\",\n        sku: \"AIR-003\",\n        category: \"العدسات الملونة\",\n        brand: \"Alcon\",\n        price: 95000,\n        priceCurrency: \"IQD\",\n        cost: 65000,\n        costCurrency: \"IQD\",\n        stock: 0,\n        status: \"active\",\n        image: \"https://picsum.photos/400/400?random=3\",\n        images: [\n            \"https://picsum.photos/400/400?random=3\"\n        ],\n        description: \"عدسات لاصقة ملونة آمنة وجميلة\",\n        tags: [\n            \"ملونة\",\n            \"جميلة\",\n            \"آمنة\"\n        ],\n        specifications: [\n            {\n                key: \"النوع\",\n                value: \"عدسات ملونة\"\n            },\n            {\n                key: \"المدة\",\n                value: \"شهرية\"\n            }\n        ],\n        slug: \"air-optix-colors\",\n        featured: true,\n        allowBackorder: true,\n        trackQuantity: true,\n        createdAt: \"2024-01-05T10:00:00Z\",\n        updatedAt: \"2024-01-05T10:00:00Z\"\n    }\n];\n// دوال إدارة المنتجات\nconst ProductsStore = {\n    // الحصول على جميع المنتجات\n    getAll: ()=>{\n        return [\n            ...products\n        ];\n    },\n    // الحصول على منتج بالمعرف\n    getById: (id)=>{\n        return products.find((p)=>p.id === id);\n    },\n    // الحصول على منتج بالـ SKU\n    getBySku: (sku)=>{\n        return products.find((p)=>p.sku === sku);\n    },\n    // إضافة منتج جديد\n    add: (productData)=>{\n        const newProduct = {\n            ...productData,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        products.push(newProduct);\n        return newProduct;\n    },\n    // تحديث منتج\n    update: (id, updates)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        products[index] = {\n            ...products[index],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        return products[index];\n    },\n    // حذف منتج\n    delete: (id)=>{\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        products.splice(index, 1);\n        return true;\n    },\n    // حذف منتجات متعددة\n    deleteMultiple: (ids)=>{\n        let deletedCount = 0;\n        ids.forEach((id)=>{\n            if (ProductsStore.delete(id)) {\n                deletedCount++;\n            }\n        });\n        return deletedCount;\n    },\n    // البحث في المنتجات\n    search: (query)=>{\n        const lowerQuery = query.toLowerCase();\n        return products.filter((product)=>product.name.toLowerCase().includes(lowerQuery) || product.nameEn.toLowerCase().includes(lowerQuery) || product.sku.toLowerCase().includes(lowerQuery) || product.brand.toLowerCase().includes(lowerQuery) || product.category.toLowerCase().includes(lowerQuery));\n    },\n    // فلترة المنتجات\n    filter: (filters)=>{\n        return products.filter((product)=>{\n            if (filters.category && filters.category !== \"الكل\" && product.category !== filters.category) {\n                return false;\n            }\n            if (filters.brand && filters.brand !== \"الكل\" && product.brand !== filters.brand) {\n                return false;\n            }\n            if (filters.status && filters.status !== \"الكل\" && product.status !== filters.status) {\n                return false;\n            }\n            if (filters.inStock !== undefined) {\n                const hasStock = product.stock > 0;\n                if (filters.inStock !== hasStock) {\n                    return false;\n                }\n            }\n            return true;\n        });\n    },\n    // الحصول على الإحصائيات\n    getStats: ()=>{\n        const total = products.length;\n        const active = products.filter((p)=>p.status === \"active\").length;\n        const draft = products.filter((p)=>p.status === \"draft\").length;\n        const outOfStock = products.filter((p)=>p.stock === 0).length;\n        const totalStock = products.reduce((sum, p)=>sum + p.stock, 0);\n        const lowStock = products.filter((p)=>p.stock > 0 && p.stock <= 10).length;\n        return {\n            total,\n            active,\n            draft,\n            outOfStock,\n            totalStock,\n            lowStock\n        };\n    },\n    // توليد SKU تلقائي\n    generateSku: function() {\n        let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"PRD\";\n        let counter = 1;\n        let sku;\n        do {\n            sku = \"\".concat(prefix, \"-\").concat(counter.toString().padStart(3, '0'));\n            counter++;\n        }while (ProductsStore.getBySku(sku));\n        return sku;\n    },\n    // توليد slug تلقائي\n    generateSlug: (name, sku)=>{\n        let baseSlug = name.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n        if (sku) {\n            baseSlug += \"-\".concat(sku.toLowerCase());\n        }\n        let slug = baseSlug;\n        let counter = 1;\n        while(products.some((p)=>p.slug === slug)){\n            slug = \"\".concat(baseSlug, \"-\").concat(counter);\n            counter++;\n        }\n        return slug;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/products-store.ts\n"));

/***/ })

});