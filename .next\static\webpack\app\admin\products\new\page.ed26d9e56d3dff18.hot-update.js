"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/new/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/admin/products/new/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/currency */ \"(app-pages-browser)/./src/lib/currency.ts\");\n/* harmony import */ var _lib_products_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/products-store */ \"(app-pages-browser)/./src/lib/products-store.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction NewProductPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // معلومات أساسية\n        name: \"\",\n        nameEn: \"\",\n        description: \"\",\n        shortDescription: \"\",\n        sku: \"\",\n        barcode: \"\",\n        // التصنيف\n        category: \"\",\n        brand: \"\",\n        tags: [],\n        // الأسعار\n        price: \"\",\n        priceCurrency: \"IQD\",\n        comparePrice: \"\",\n        comparePriceCurrency: \"IQD\",\n        cost: \"\",\n        costCurrency: \"USD\",\n        // المخزون\n        trackQuantity: true,\n        quantity: \"\",\n        minQuantity: \"\",\n        maxQuantity: \"\",\n        location: \"\",\n        // الشحن\n        weight: \"\",\n        dimensions: {\n            length: \"\",\n            width: \"\",\n            height: \"\"\n        },\n        // SEO\n        metaTitle: \"\",\n        metaDescription: \"\",\n        slug: \"\",\n        // الحالة\n        status: \"draft\",\n        featured: false,\n        allowBackorder: false,\n        // المواصفات\n        specifications: [],\n        // الصور\n        images: []\n    });\n    const [currentTag, setCurrentTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImages, setPreviewImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCurrencyConverter, setShowCurrencyConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profitCalculation, setProfitCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isImageUploadMode, setIsImageUploadMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const categories = [\n        \"العدسات اليومية\",\n        \"العدسات الشهرية\",\n        \"العدسات الملونة\",\n        \"العدسات الأسبوعية\",\n        \"النظارات الطبية\",\n        \"النظارات الشمسية\",\n        \"الإكسسوارات\"\n    ];\n    const brands = [\n        \"Johnson & Johnson\",\n        \"Alcon\",\n        \"CooperVision\",\n        \"Bausch & Lomb\",\n        \"Ray-Ban\",\n        \"Oakley\"\n    ];\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // إزالة الخطأ عند التعديل\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n        // توليد slug تلقائياً من الاسم\n        if (field === \"name\" && value) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n        // حساب الربح عند تغيير الأسعار\n        if (field === \"price\" || field === \"cost\" || field === \"priceCurrency\" || field === \"costCurrency\") {\n            setTimeout(()=>calculateProfit(), 0);\n        }\n    };\n    const calculateProfit = ()=>{\n        if (formData.price && formData.cost) {\n            try {\n                const profit = (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.calculateProfitInIQD)(parseFloat(formData.cost), formData.costCurrency, parseFloat(formData.price), formData.priceCurrency);\n                setProfitCalculation(profit);\n            } catch (error) {\n                console.error(\"خطأ في حساب الربح:\", error);\n                setProfitCalculation(null);\n            }\n        } else {\n            setProfitCalculation(null);\n        }\n    };\n    const handleDimensionChange = (dimension, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                dimensions: {\n                    ...prev.dimensions,\n                    [dimension]: value\n                }\n            }));\n    };\n    const addTag = ()=>{\n        if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        currentTag.trim()\n                    ]\n                }));\n            setCurrentTag(\"\");\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const addSpecification = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: [\n                    ...prev.specifications,\n                    {\n                        key: \"\",\n                        value: \"\"\n                    }\n                ]\n            }));\n    };\n    const updateSpecification = (index, field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.map((spec, i)=>i === index ? {\n                        ...spec,\n                        [field]: value\n                    } : spec)\n            }));\n    };\n    const removeSpecification = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        // التأكد من أننا في وضع رفع الصور وليس إرسال النموذج\n        setIsImageUploadMode(true);\n        setUploadingImages(true);\n        const newImages = [];\n        const newPreviews = [];\n        try {\n            for(let i = 0; i < files.length; i++){\n                const file = files[i];\n                // التحقق من نوع الملف\n                if (!file.type.startsWith('image/')) {\n                    alert(\"الملف \".concat(file.name, \" ليس صورة صالحة\"));\n                    continue;\n                }\n                // التحقق من حجم الملف (5MB كحد أقصى)\n                if (file.size > 5 * 1024 * 1024) {\n                    alert(\"الملف \".concat(file.name, \" كبير جداً. الحد الأقصى 5MB\"));\n                    continue;\n                }\n                // إنشاء معاينة محلية\n                const preview = URL.createObjectURL(file);\n                newPreviews.push(preview);\n                // محاكاة رفع الصورة\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // في التطبيق الحقيقي، سيتم رفع الصورة إلى الخادم\n                const imageUrl = \"https://picsum.photos/400/400?random=\".concat(Date.now(), \"-\").concat(i);\n                newImages.push(imageUrl);\n            }\n            setFormData((prev)=>({\n                    ...prev,\n                    images: [\n                        ...prev.images,\n                        ...newImages\n                    ]\n                }));\n            setPreviewImages((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n            if (newImages.length > 0) {\n                alert(\"تم رفع \".concat(newImages.length, \" صورة بنجاح!\"));\n            }\n        } catch (error) {\n            console.error(\"Error uploading images:\", error);\n            alert(\"حدث خطأ أثناء رفع الصور\");\n        } finally{\n            setUploadingImages(false);\n            setIsImageUploadMode(false);\n            // إعادة تعيين قيمة input\n            if (event.target) {\n                event.target.value = '';\n            }\n        }\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n        setPreviewImages((prev)=>{\n            const newPreviews = prev.filter((_, i)=>i !== index);\n            // تنظيف URL المؤقت\n            if (prev[index]) {\n                URL.revokeObjectURL(prev[index]);\n            }\n            return newPreviews;\n        });\n    };\n    const duplicateProduct = ()=>{\n        const duplicatedData = {\n            ...formData,\n            name: \"\".concat(formData.name, \" - نسخة\"),\n            nameEn: \"\".concat(formData.nameEn, \" - Copy\"),\n            sku: \"\".concat(formData.sku, \"-COPY\"),\n            slug: \"\".concat(formData.slug, \"-copy\")\n        };\n        setFormData(duplicatedData);\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) newErrors.name = \"اسم المنتج مطلوب\";\n        if (!formData.nameEn.trim()) newErrors.nameEn = \"الاسم الإنجليزي مطلوب\";\n        if (!formData.description.trim()) newErrors.description = \"الوصف مطلوب\";\n        if (!formData.sku.trim()) newErrors.sku = \"رمز المنتج مطلوب\";\n        if (!formData.category) newErrors.category = \"الفئة مطلوبة\";\n        if (!formData.brand) newErrors.brand = \"العلامة التجارية مطلوبة\";\n        if (!formData.price || parseFloat(formData.price) <= 0) {\n            newErrors.price = \"السعر مطلوب ويجب أن يكون أكبر من صفر\";\n        }\n        if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {\n            newErrors.quantity = \"الكمية مطلوبة\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async function(e) {\n        let status = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"draft\";\n        e.preventDefault();\n        // التأكد من أننا لسنا في وضع رفع الصور\n        if (isImageUploadMode) {\n            return;\n        }\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // إعداد بيانات المنتج\n            const productData = {\n                name: formData.name,\n                nameEn: formData.nameEn,\n                description: formData.description,\n                shortDescription: formData.shortDescription,\n                sku: formData.sku || _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.generateSku(),\n                barcode: formData.barcode,\n                category: formData.category,\n                brand: formData.brand,\n                tags: formData.tags,\n                price: parseFloat(formData.price),\n                priceCurrency: formData.priceCurrency,\n                comparePrice: formData.comparePrice ? parseFloat(formData.comparePrice) : undefined,\n                comparePriceCurrency: formData.comparePriceCurrency,\n                cost: formData.cost ? parseFloat(formData.cost) : undefined,\n                costCurrency: formData.costCurrency,\n                stock: formData.trackQuantity ? parseInt(formData.quantity) : 0,\n                minQuantity: formData.minQuantity ? parseInt(formData.minQuantity) : undefined,\n                maxQuantity: formData.maxQuantity ? parseInt(formData.maxQuantity) : undefined,\n                trackQuantity: formData.trackQuantity,\n                allowBackorder: formData.allowBackorder,\n                status: status,\n                image: formData.images[0] || \"https://picsum.photos/400/400?random=default\",\n                images: formData.images.length > 0 ? formData.images : [\n                    \"https://picsum.photos/400/400?random=default\"\n                ],\n                specifications: formData.specifications.filter((spec)=>spec.key && spec.value),\n                weight: formData.weight ? parseFloat(formData.weight) : undefined,\n                dimensions: formData.dimensions.length && formData.dimensions.width && formData.dimensions.height ? {\n                    length: parseFloat(formData.dimensions.length),\n                    width: parseFloat(formData.dimensions.width),\n                    height: parseFloat(formData.dimensions.height)\n                } : undefined,\n                metaTitle: formData.metaTitle,\n                metaDescription: formData.metaDescription,\n                slug: formData.slug || _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.generateSlug(formData.name, formData.sku),\n                featured: formData.featured,\n                location: formData.location\n            };\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // إضافة المنتج إلى المتجر\n            const newProduct = _lib_products_store__WEBPACK_IMPORTED_MODULE_8__.ProductsStore.add(productData);\n            console.log(\"Product created:\", newProduct);\n            const statusText = status === \"active\" ? \"ونشر\" : \"كمسودة\";\n            alert(\"تم إضافة المنتج \".concat(statusText, \" بنجاح!\"));\n            // إعادة توجيه إلى صفحة المنتجات\n            router.push(\"/admin/products\");\n        } catch (error) {\n            console.error(\"Error creating product:\", error);\n            alert(\"حدث خطأ أثناء إضافة المنتج\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSaveAsDraft = (e)=>{\n        e.preventDefault();\n        handleSubmit(e, \"draft\");\n    };\n    const handlePublish = (e)=>{\n        e.preventDefault();\n        handleSubmit(e, \"active\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إضافة منتج جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"إنشاء منتج جديد في المتجر\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: duplicateProduct,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نسخ المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleSaveAsDraft,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حفظ كمسودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handlePublish,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نشر المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 406,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: (e)=>handleSubmit(e, \"draft\"),\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (عربي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.name,\n                                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                placeholder: \"أدخل اسم المنتج\",\n                                                                className: errors.name ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (إنجليزي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.nameEn,\n                                                                onChange: (e)=>handleInputChange(\"nameEn\", e.target.value),\n                                                                placeholder: \"Product Name in English\",\n                                                                className: errors.nameEn ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.nameEn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.nameEn\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف المختصر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.shortDescription,\n                                                        onChange: (e)=>handleInputChange(\"shortDescription\", e.target.value),\n                                                        placeholder: \"وصف مختصر للمنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف التفصيلي *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                        placeholder: \"وصف تفصيلي للمنتج...\",\n                                                        rows: 4,\n                                                        className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.description ? \"border-red-500\" : \"border-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-xs mt-1\",\n                                                        children: errors.description\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"رمز المنتج (SKU) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.sku,\n                                                                onChange: (e)=>handleInputChange(\"sku\", e.target.value),\n                                                                placeholder: \"PRD-001\",\n                                                                className: errors.sku ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.sku && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.sku\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الباركود\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.barcode,\n                                                                onChange: (e)=>handleInputChange(\"barcode\", e.target.value),\n                                                                placeholder: \"1234567890123\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"التصنيف\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الفئة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.category,\n                                                                onChange: (e)=>handleInputChange(\"category\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.category ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر الفئة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category,\n                                                                            children: category\n                                                                        }, category, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.category\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.brand,\n                                                                onChange: (e)=>handleInputChange(\"brand\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.brand ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر العلامة التجارية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: brand,\n                                                                            children: brand\n                                                                        }, brand, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.brand\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"العلامات (Tags)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: currentTag,\n                                                                onChange: (e)=>setCurrentTag(e.target.value),\n                                                                placeholder: \"أضف علامة\",\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && (e.preventDefault(), addTag())\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addTag,\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-sm\",\n                                                                children: [\n                                                                    tag,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>removeTag(tag),\n                                                                        className: \"hover:text-red-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الأسعار والعملات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"سعر البيع *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                value: formData.price,\n                                                                onChange: (e)=>handleInputChange(\"price\", e.target.value),\n                                                                placeholder: \"0.00\",\n                                                                className: errors.price ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.priceCurrency,\n                                                                onChange: (e)=>handleInputChange(\"priceCurrency\", e.target.value),\n                                                                className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: _lib_currency__WEBPACK_IMPORTED_MODULE_7__.SUPPORTED_CURRENCIES.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: currency.code,\n                                                                        children: [\n                                                                            currency.nameAr,\n                                                                            \" (\",\n                                                                            currency.symbolAr,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, currency.code, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 661,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-xs mt-1\",\n                                                        children: errors.price\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.price && formData.priceCurrency !== \"IQD\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-600 mt-1\",\n                                                        children: [\n                                                            \"= \",\n                                                            (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)((0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.convertCurrency)(parseFloat(formData.price), formData.priceCurrency, \"IQD\"), \"IQD\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"سعر التكلفة (للاستيراد)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                value: formData.cost,\n                                                                onChange: (e)=>handleInputChange(\"cost\", e.target.value),\n                                                                placeholder: \"0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.costCurrency,\n                                                                onChange: (e)=>handleInputChange(\"costCurrency\", e.target.value),\n                                                                className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: _lib_currency__WEBPACK_IMPORTED_MODULE_7__.SUPPORTED_CURRENCIES.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: currency.code,\n                                                                        children: [\n                                                                            currency.nameAr,\n                                                                            \" (\",\n                                                                            currency.symbolAr,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, currency.code, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    formData.cost && formData.costCurrency !== \"IQD\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-600 mt-1\",\n                                                        children: [\n                                                            \"= \",\n                                                            (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)((0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.convertCurrency)(parseFloat(formData.cost), formData.costCurrency, \"IQD\"), \"IQD\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"السعر المقارن (اختياري)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                value: formData.comparePrice,\n                                                                onChange: (e)=>handleInputChange(\"comparePrice\", e.target.value),\n                                                                placeholder: \"0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.comparePriceCurrency,\n                                                                onChange: (e)=>handleInputChange(\"comparePriceCurrency\", e.target.value),\n                                                                className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                                children: _lib_currency__WEBPACK_IMPORTED_MODULE_7__.SUPPORTED_CURRENCIES.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: currency.code,\n                                                                        children: [\n                                                                            currency.nameAr,\n                                                                            \" (\",\n                                                                            currency.symbolAr,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, currency.code, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    formData.comparePrice && formData.comparePriceCurrency !== \"IQD\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-600 mt-1\",\n                                                        children: [\n                                                            \"= \",\n                                                            (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)((0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.convertCurrency)(parseFloat(formData.comparePrice), formData.comparePriceCurrency, \"IQD\"), \"IQD\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 15\n                                            }, this),\n                                            profitCalculation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-green-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-green-800 mb-3\",\n                                                        children: \"حساب الربح (بالدينار العراقي)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"التكلفة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 747,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(profitCalculation.costInIQD, \"IQD\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"سعر البيع\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(profitCalculation.sellingPriceInIQD, \"IQD\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"الربح\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold \".concat(profitCalculation.profitInIQD >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                                        children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(profitCalculation.profitInIQD, \"IQD\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"هامش الربح\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold \".concat(profitCalculation.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                                        children: [\n                                                                            profitCalculation.profitMargin.toFixed(2),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 743,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"إدارة المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"trackQuantity\",\n                                                        checked: formData.trackQuantity,\n                                                        onChange: (e)=>handleInputChange(\"trackQuantity\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"trackQuantity\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"تتبع الكمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.trackQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الكمية الحالية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.quantity,\n                                                                onChange: (e)=>handleInputChange(\"quantity\", e.target.value),\n                                                                placeholder: \"0\",\n                                                                className: errors.quantity ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            errors.quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأدنى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.minQuantity,\n                                                                onChange: (e)=>handleInputChange(\"minQuantity\", e.target.value),\n                                                                placeholder: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأقصى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.maxQuantity,\n                                                                onChange: (e)=>handleInputChange(\"maxQuantity\", e.target.value),\n                                                                placeholder: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الموقع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                value: formData.location,\n                                                                onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                                placeholder: \"A1-B2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"allowBackorder\",\n                                                        checked: formData.allowBackorder,\n                                                        onChange: (e)=>handleInputChange(\"allowBackorder\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"allowBackorder\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"السماح بالطلب المسبق عند نفاد المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"المواصفات التقنية\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: formData.specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                placeholder: \"المواصفة\",\n                                                                value: spec.key,\n                                                                onChange: (e)=>updateSpecification(index, \"key\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                placeholder: \"القيمة\",\n                                                                value: spec.value,\n                                                                onChange: (e)=>updateSpecification(index, \"value\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>removeSpecification(index),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: addSpecification,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"إضافة مواصفة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"حالة المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"draft\",\n                                                                children: \"مسودة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"active\",\n                                                                children: \"نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"inactive\",\n                                                                children: \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"featured\",\n                                                        checked: formData.featured,\n                                                        onChange: (e)=>handleInputChange(\"featured\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"featured\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 908,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"صور المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4 \".concat(uploadingImages ? 'animate-pulse' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                        children: uploadingImages ? \"جاري رفع الصور...\" : \"اسحب الصور هنا أو انقر للتحديد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        multiple: true,\n                                                        accept: \"image/*\",\n                                                        onChange: handleImageUpload,\n                                                        className: \"hidden\",\n                                                        id: \"image-upload\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>{\n                                                            var _document_getElementById;\n                                                            return (_document_getElementById = document.getElementById('image-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                        },\n                                                        disabled: uploadingImages,\n                                                        children: uploadingImages ? \"جاري الرفع...\" : \"اختيار الصور\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 959,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.images.length > 0 || previewImages.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-2\",\n                                                children: formData.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square relative rounded-lg overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: previewImages[index] || image,\n                                                                    alt: \"صورة \".concat(index + 1),\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeImage(index),\n                                                                className: \"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded\",\n                                                                children: \"الصورة الرئيسية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 973,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 971,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 941,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"معلومات الشحن\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوزن (جرام)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        type: \"number\",\n                                                        value: formData.weight,\n                                                        onChange: (e)=>handleInputChange(\"weight\", e.target.value),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1011,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1007,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الأبعاد (سم)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1020,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.length,\n                                                                onChange: (e)=>handleDimensionChange(\"length\", e.target.value),\n                                                                placeholder: \"طول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 1024,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.width,\n                                                                onChange: (e)=>handleDimensionChange(\"width\", e.target.value),\n                                                                placeholder: \"عرض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.height,\n                                                                onChange: (e)=>handleDimensionChange(\"height\", e.target.value),\n                                                                placeholder: \"ارتفاع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 1051,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"تحسين محركات البحث\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الرابط (Slug)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.slug,\n                                                        onChange: (e)=>handleInputChange(\"slug\", e.target.value),\n                                                        placeholder: \"product-slug\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1060,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1068,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: formData.metaTitle,\n                                                        onChange: (e)=>handleInputChange(\"metaTitle\", e.target.value),\n                                                        placeholder: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1071,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1067,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"وصف الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>handleInputChange(\"metaDescription\", e.target.value),\n                                                        placeholder: \"وصف الصفحة لمحركات البحث\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 1048,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 906,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 435,\n                columnNumber: 7\n            }, this),\n            Object.keys(errors).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"يرجى تصحيح الأخطاء التالية:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 1101,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 1099,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 text-sm text-red-700 list-disc list-inside\",\n                            children: Object.values(errors).map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: error\n                                }, index, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 1105,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 1103,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                    lineNumber: 1098,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 1097,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n        lineNumber: 404,\n        columnNumber: 5\n    }, this);\n}\n_s(NewProductPage, \"IGX/01kNojoTO2iCaCdmn70tBhg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter\n    ];\n});\n_c = NewProductPage;\nvar _c;\n$RefreshReg$(_c, \"NewProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/new/page.tsx\n"));

/***/ })

});