"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronLeft, Filter, Grid, List, SlidersHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import ProductCard from "@/components/product/product-card";

// بيانات وهمية للفئة
const categoryData = {
  "daily-lenses": {
    name: "العدسات اليومية",
    description: "عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي. تتميز بسهولة الاستخدام والنظافة المضمونة.",
    image: "https://picsum.photos/1200/400?random=20",
    benefits: [
      "راحة طوال اليوم",
      "سهولة الاستخدام",
      "نظافة مضمونة",
      "لا تحتاج تنظيف",
      "مناسبة للمبتدئين",
      "تقليل خطر العدوى"
    ]
  },
  "monthly-lenses": {
    name: "العدسات الشهرية",
    description: "عدسات لاصقة شهرية اقتصادية وعملية توفر راحة طويلة المدى مع جودة عالية.",
    image: "https://picsum.photos/1200/400?random=21",
    benefits: [
      "اقتصادية",
      "جودة عالية",
      "مقاومة للترسبات",
      "راحة طويلة المدى",
      "سهولة العناية",
      "متانة عالية"
    ]
  },
  "colored-lenses": {
    name: "العدسات الملونة",
    description: "عدسات ملونة لإطلالة مميزة وجذابة مع ألوان طبيعية وتغطية ممتازة.",
    image: "https://picsum.photos/1200/400?random=22",
    benefits: [
      "ألوان طبيعية",
      "تغطية ممتازة",
      "آمنة ومريحة",
      "تصاميم متنوعة",
      "مناسبة للمناسبات",
      "جودة الألوان"
    ]
  }
};

// منتجات وهمية للفئة
const categoryProducts = [
  {
    id: "1",
    name: "Acuvue Oasys Daily",
    nameAr: "عدسات أكيوفيو أوازيس اليومية",
    slug: "acuvue-oasys-daily",
    price: 120,
    comparePrice: 150,
    image: "https://picsum.photos/400/400?random=1",
    brand: "Johnson & Johnson",
    rating: 4.8,
    reviewCount: 124,
    isNew: true,
    isFeatured: true,
    inStock: true,
  },
  {
    id: "4",
    name: "Dailies Total 1",
    nameAr: "عدسات ديليز توتال ون",
    slug: "dailies-total-1",
    price: 140,
    comparePrice: 160,
    image: "https://picsum.photos/400/400?random=4",
    brand: "Alcon",
    rating: 4.9,
    reviewCount: 203,
    isNew: true,
    inStock: true,
  },
  {
    id: "9",
    name: "Acuvue Moist Daily",
    nameAr: "عدسات أكيوفيو مويست اليومية",
    slug: "acuvue-moist-daily",
    price: 95,
    comparePrice: 110,
    image: "https://picsum.photos/400/400?random=9",
    brand: "Johnson & Johnson",
    rating: 4.6,
    reviewCount: 156,
    inStock: true,
  },
  {
    id: "10",
    name: "Biotrue ONEday",
    nameAr: "عدسات بايوترو ون داي",
    slug: "biotrue-oneday",
    price: 105,
    image: "https://picsum.photos/400/400?random=10",
    brand: "Bausch & Lomb",
    rating: 4.5,
    reviewCount: 89,
    inStock: true,
  },
  {
    id: "11",
    name: "Clariti 1 day",
    nameAr: "عدسات كلاريتي ون داي",
    slug: "clariti-1-day",
    price: 85,
    comparePrice: 100,
    image: "https://picsum.photos/400/400?random=11",
    brand: "CooperVision",
    rating: 4.4,
    reviewCount: 67,
    inStock: true,
  },
  {
    id: "12",
    name: "MyDay Daily",
    nameAr: "عدسات ماي داي اليومية",
    slug: "myday-daily",
    price: 115,
    image: "https://picsum.photos/400/400?random=12",
    brand: "CooperVision",
    rating: 4.7,
    reviewCount: 134,
    inStock: false,
  },
];

const brands = ["الكل", ...Array.from(new Set(categoryProducts.map(p => p.brand)))];
const priceRanges = [
  { label: "الكل", min: 0, max: Infinity },
  { label: "أقل من 100 ريال", min: 0, max: 100 },
  { label: "100 - 150 ريال", min: 100, max: 150 },
  { label: "أكثر من 150 ريال", min: 150, max: Infinity },
];

export default function CategoryPage({ params }: { params: { slug: string } }) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBrand, setSelectedBrand] = useState("الكل");
  const [selectedPriceRange, setSelectedPriceRange] = useState(priceRanges[0]);
  const [sortBy, setSortBy] = useState("الأحدث");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);

  const category = categoryData[params.slug as keyof typeof categoryData];

  if (!category) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-3xl font-bold mb-4">الفئة غير موجودة</h1>
          <p className="text-gray-600 mb-8">عذراً، الفئة التي تبحث عنها غير متوفرة</p>
          <Button asChild>
            <Link href="/categories">العودة للفئات</Link>
          </Button>
        </main>
        <Footer />
      </div>
    );
  }

  const filteredProducts = categoryProducts.filter((product) => {
    const matchesSearch = product.nameAr.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesBrand = selectedBrand === "الكل" || product.brand === selectedBrand;
    const matchesPrice = product.price >= selectedPriceRange.min && product.price <= selectedPriceRange.max;
    
    return matchesSearch && matchesBrand && matchesPrice;
  });

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "السعر: من الأقل للأعلى":
        return a.price - b.price;
      case "السعر: من الأعلى للأقل":
        return b.price - a.price;
      case "الأعلى تقييماً":
        return (b.rating || 0) - (a.rating || 0);
      case "الأكثر مبيعاً":
        return (b.reviewCount || 0) - (a.reviewCount || 0);
      default:
        return 0;
    }
  });

  return (
    <div className="min-h-screen">
      <Header />
      
      <main>
        {/* مسار التنقل */}
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center gap-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-primary">الرئيسية</Link>
            <ChevronLeft className="h-4 w-4" />
            <Link href="/categories" className="hover:text-primary">الفئات</Link>
            <ChevronLeft className="h-4 w-4" />
            <span className="text-gray-900">{category.name}</span>
          </nav>
        </div>

        {/* بانر الفئة */}
        <section className="relative h-80 overflow-hidden">
          <Image
            src={category.image}
            alt={category.name}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black/50" />
          <div className="relative container mx-auto px-4 h-full flex items-center">
            <div className="text-white max-w-2xl">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">{category.name}</h1>
              <p className="text-xl opacity-90">{category.description}</p>
            </div>
          </div>
        </section>

        {/* مميزات الفئة */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold text-center mb-8">مميزات {category.name}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {category.benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-3 bg-white p-4 rounded-lg shadow-sm">
                  <div className="w-3 h-3 bg-primary rounded-full"></div>
                  <span className="font-medium">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* المنتجات */}
        <section className="container mx-auto px-4 py-12">
          {/* البحث والفلاتر */}
          <div className="mb-8">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <Input
                  placeholder="ابحث في المنتجات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  فلاتر
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
                >
                  {viewMode === "grid" ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* الفلاتر */}
            {showFilters && (
              <Card className="mb-6">
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">العلامة التجارية</label>
                      <select
                        value={selectedBrand}
                        onChange={(e) => setSelectedBrand(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        {brands.map((brand) => (
                          <option key={brand} value={brand}>{brand}</option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">نطاق السعر</label>
                      <select
                        value={selectedPriceRange.label}
                        onChange={(e) => {
                          const range = priceRanges.find(r => r.label === e.target.value);
                          if (range) setSelectedPriceRange(range);
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        {priceRanges.map((range) => (
                          <option key={range.label} value={range.label}>{range.label}</option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">ترتيب حسب</label>
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="الأحدث">الأحدث</option>
                        <option value="السعر: من الأقل للأعلى">السعر: من الأقل للأعلى</option>
                        <option value="السعر: من الأعلى للأقل">السعر: من الأعلى للأقل</option>
                        <option value="الأعلى تقييماً">الأعلى تقييماً</option>
                        <option value="الأكثر مبيعاً">الأكثر مبيعاً</option>
                      </select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* عدد النتائج */}
            <div className="flex justify-between items-center mb-6">
              <p className="text-gray-600">
                عرض {sortedProducts.length} من {categoryProducts.length} منتج
              </p>
            </div>
          </div>

          {/* شبكة المنتجات */}
          {sortedProducts.length > 0 ? (
            <div className={`grid gap-6 ${
              viewMode === "grid" 
                ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
                : "grid-cols-1"
            }`}>
              {sortedProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">لا توجد منتجات تطابق البحث</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedBrand("الكل");
                  setSelectedPriceRange(priceRanges[0]);
                }}
              >
                إعادة تعيين الفلاتر
              </Button>
            </div>
          )}
        </section>
      </main>

      <Footer />
    </div>
  );
}
