"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/admin/products/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Eye,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction EditProductPage(param) {\n    let { params } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [originalData, setOriginalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTag, setCurrentTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImages, setPreviewImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditProductPage.useEffect\": ()=>{\n            // محاكاة تحميل بيانات المنتج\n            const loadProduct = {\n                \"EditProductPage.useEffect.loadProduct\": async ()=>{\n                    try {\n                        // هنا سيتم جلب بيانات المنتج من API\n                        await new Promise({\n                            \"EditProductPage.useEffect.loadProduct\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"EditProductPage.useEffect.loadProduct\"]);\n                        // بيانات وهمية للمنتج\n                        const mockProduct = {\n                            id: params.id,\n                            name: \"عدسات أكيوفيو اليومية\",\n                            nameEn: \"Acuvue Oasys Daily\",\n                            description: \"عدسات لاصقة يومية مريحة وآمنة للاستخدام اليومي\",\n                            shortDescription: \"عدسات يومية مريحة\",\n                            sku: \"ACU-001\",\n                            barcode: \"1234567890123\",\n                            category: \"العدسات اليومية\",\n                            brand: \"Johnson & Johnson\",\n                            tags: [\n                                \"يومية\",\n                                \"مريحة\",\n                                \"آمنة\"\n                            ],\n                            price: \"120\",\n                            comparePrice: \"150\",\n                            cost: \"80\",\n                            trackQuantity: true,\n                            quantity: \"45\",\n                            minQuantity: \"10\",\n                            maxQuantity: \"100\",\n                            location: \"A1-B2\",\n                            weight: \"50\",\n                            dimensions: {\n                                length: \"10\",\n                                width: \"8\",\n                                height: \"2\"\n                            },\n                            metaTitle: \"عدسات أكيوفيو اليومية - VisionLens\",\n                            metaDescription: \"عدسات لاصقة يومية مريحة وآمنة\",\n                            slug: \"acuvue-oasys-daily\",\n                            status: \"active\",\n                            featured: true,\n                            allowBackorder: false,\n                            specifications: [\n                                {\n                                    key: \"نوع العدسة\",\n                                    value: \"يومية\"\n                                },\n                                {\n                                    key: \"المادة\",\n                                    value: \"سيليكون هيدروجيل\"\n                                },\n                                {\n                                    key: \"نفاذية الأكسجين\",\n                                    value: \"عالية\"\n                                }\n                            ],\n                            images: [\n                                \"https://picsum.photos/400/400?random=1\",\n                                \"https://picsum.photos/400/400?random=11\"\n                            ]\n                        };\n                        setFormData(mockProduct);\n                        setOriginalData(mockProduct);\n                        setPreviewImages(mockProduct.images);\n                    } catch (error) {\n                        console.error(\"Error loading product:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"EditProductPage.useEffect.loadProduct\"];\n            loadProduct();\n        }\n    }[\"EditProductPage.useEffect\"], [\n        params.id\n    ]);\n    const categories = [\n        \"العدسات اليومية\",\n        \"العدسات الشهرية\",\n        \"العدسات الملونة\",\n        \"العدسات الأسبوعية\",\n        \"النظارات الطبية\",\n        \"النظارات الشمسية\",\n        \"الإكسسوارات\"\n    ];\n    const brands = [\n        \"Johnson & Johnson\",\n        \"Alcon\",\n        \"CooperVision\",\n        \"Bausch & Lomb\",\n        \"Ray-Ban\",\n        \"Oakley\"\n    ];\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setHasChanges(true);\n        // إزالة الخطأ عند التعديل\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const handleDimensionChange = (dimension, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                dimensions: {\n                    ...prev.dimensions,\n                    [dimension]: value\n                }\n            }));\n        setHasChanges(true);\n    };\n    const addTag = ()=>{\n        if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        currentTag.trim()\n                    ]\n                }));\n            setCurrentTag(\"\");\n            setHasChanges(true);\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n        setHasChanges(true);\n    };\n    const addSpecification = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: [\n                    ...prev.specifications,\n                    {\n                        key: \"\",\n                        value: \"\"\n                    }\n                ]\n            }));\n        setHasChanges(true);\n    };\n    const updateSpecification = (index, field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.map((spec, i)=>i === index ? {\n                        ...spec,\n                        [field]: value\n                    } : spec)\n            }));\n        setHasChanges(true);\n    };\n    const removeSpecification = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.filter((_, i)=>i !== index)\n            }));\n        setHasChanges(true);\n    };\n    const handleImageUpload = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        setUploadingImages(true);\n        const newImages = [];\n        const newPreviews = [];\n        try {\n            for(let i = 0; i < files.length; i++){\n                const file = files[i];\n                const preview = URL.createObjectURL(file);\n                newPreviews.push(preview);\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                const imageUrl = \"https://picsum.photos/400/400?random=\".concat(Date.now(), \"-\").concat(i);\n                newImages.push(imageUrl);\n            }\n            setFormData((prev)=>({\n                    ...prev,\n                    images: [\n                        ...prev.images,\n                        ...newImages\n                    ]\n                }));\n            setPreviewImages((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n            setHasChanges(true);\n        } catch (error) {\n            console.error(\"Error uploading images:\", error);\n            alert(\"حدث خطأ أثناء رفع الصور\");\n        } finally{\n            setUploadingImages(false);\n        }\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n        setPreviewImages((prev)=>{\n            const newPreviews = prev.filter((_, i)=>i !== index);\n            if (prev[index]) {\n                URL.revokeObjectURL(prev[index]);\n            }\n            return newPreviews;\n        });\n        setHasChanges(true);\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) newErrors.name = \"اسم المنتج مطلوب\";\n        if (!formData.nameEn.trim()) newErrors.nameEn = \"الاسم الإنجليزي مطلوب\";\n        if (!formData.description.trim()) newErrors.description = \"الوصف مطلوب\";\n        if (!formData.sku.trim()) newErrors.sku = \"رمز المنتج مطلوب\";\n        if (!formData.category) newErrors.category = \"الفئة مطلوبة\";\n        if (!formData.brand) newErrors.brand = \"العلامة التجارية مطلوبة\";\n        if (!formData.price || parseFloat(formData.price) <= 0) {\n            newErrors.price = \"السعر مطلوب ويجب أن يكون أكبر من صفر\";\n        }\n        if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {\n            newErrors.quantity = \"الكمية مطلوبة\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const resetForm = ()=>{\n        setFormData(originalData);\n        setPreviewImages(originalData.images);\n        setHasChanges(false);\n        setErrors({});\n    };\n    const handleUpdate = async ()=>{\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log(\"Updating product:\", formData);\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            setOriginalData(formData);\n            setHasChanges(false);\n            alert(\"تم تحديث المنتج بنجاح!\");\n        } catch (error) {\n            console.error(\"Error updating product:\", error);\n            alert(\"حدث خطأ أثناء تحديث المنتج\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleDelete = async ()=>{\n        if (!confirm(\"هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.\")) {\n            return;\n        }\n        try {\n            // هنا سيتم حذف المنتج من API\n            console.log(\"Deleting product:\", params.id);\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            alert(\"تم حذف المنتج بنجاح!\");\n        // إعادة توجيه إلى صفحة المنتجات\n        // router.push(\"/admin/products\");\n        } catch (error) {\n            console.error(\"Error deleting product:\", error);\n            alert(\"حدث خطأ أثناء حذف المنتج\");\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"icon\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/products\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"تحميل المنتج...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded w-3/4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 310,\n            columnNumber: 7\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"icon\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/products\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"المنتج غير موجود\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-red-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"المنتج المطلوب غير موجود أو تم حذفه\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/admin/products\",\n                                        children: \"العودة إلى المنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"تعديل المنتج\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: product.name\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products/\".concat(product.slug),\n                                    target: \"_blank\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"معاينة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleDelete,\n                                className: \"text-red-600 hover:text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حذف\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleUpdate,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this),\n                                    isSubmitting ? \"جاري الحفظ...\" : \"حفظ التغييرات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"معلومات المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (عربي)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (إنجليزي)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.nameEn\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-gray-50 rounded-md\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"رمز المنتج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md font-mono\",\n                                                                children: product.sku\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الباركود\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md font-mono\",\n                                                                children: product.barcode\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"التصنيف والأسعار\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الفئة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.brand\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"سعر البيع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md font-semibold text-green-600\",\n                                                                children: [\n                                                                    product.price,\n                                                                    \" ريال\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"السعر المقارن\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.comparePrice ? \"\".concat(product.comparePrice, \" ريال\") : \"غير محدد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"سعر التكلفة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-50 rounded-md\",\n                                                                children: product.cost ? \"\".concat(product.cost, \" ريال\") : \"غير محدد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الكمية الحالية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 rounded-md font-semibold\",\n                                                            children: product.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الحد الأدنى\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 rounded-md\",\n                                                            children: product.minQuantity\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الحد الأقصى\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 rounded-md\",\n                                                            children: product.maxQuantity\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"الموقع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-50 rounded-md\",\n                                                            children: product.location\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"حالة المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(product.status === \"active\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                                        children: product.status === \"active\" ? \"نشط\" : \"غير نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(product.featured ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                        children: product.featured ? \"نعم\" : \"لا\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"المواصفات\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: product.specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: [\n                                                                spec.key,\n                                                                \":\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: spec.value\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"العلامات\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: product.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md text-sm\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-blue-200 bg-blue-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Eye_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"ملاحظة:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-blue-700\",\n                            children: \"هذه صفحة عرض المنتج. لتعديل المنتج، يمكنك إنشاء نموذج تعديل مشابه لنموذج الإضافة مع تعبئة البيانات الحالية مسبقاً.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 584,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\[id]\\\\edit\\\\page.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, this);\n}\n_s(EditProductPage, \"2oo3GQebJ9DgC9Y834lIQkV3bIE=\");\n_c = EditProductPage;\nvar _c;\n$RefreshReg$(_c, \"EditProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/[id]/edit/page.tsx\n"));

/***/ })

});