"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/[slug]/page",{

/***/ "(app-pages-browser)/./src/app/products/[slug]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/products/[slug]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,Heart,Minus,Plus,RotateCcw,Share2,Shield,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_layout_footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/footer */ \"(app-pages-browser)/./src/components/layout/footer.tsx\");\n/* harmony import */ var _components_product_product_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/product/product-card */ \"(app-pages-browser)/./src/components/product/product-card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// تقييمات وهمية\nconst reviews = [\n    {\n        id: 1,\n        user: \"أحمد محمد\",\n        rating: 5,\n        date: \"2024-01-10\",\n        comment: \"عدسات ممتازة وراحة طوال اليوم. أنصح بها بشدة!\"\n    },\n    {\n        id: 2,\n        user: \"فاطمة أحمد\",\n        rating: 4,\n        date: \"2024-01-08\",\n        comment: \"جودة عالية وسعر مناسب. التوصيل كان سريع.\"\n    },\n    {\n        id: 3,\n        user: \"محمد علي\",\n        rating: 5,\n        date: \"2024-01-05\",\n        comment: \"أفضل عدسات جربتها. لا أشعر بها في عيني.\"\n    }\n];\nfunction ProductDetailPage() {\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isWishlisted, setIsWishlisted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const handleAddToCart = ()=>{\n        console.log(\"Added \".concat(quantity, \" items to cart\"));\n    };\n    const handleWishlistToggle = ()=>{\n        setIsWishlisted(!isWishlisted);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center gap-2 text-sm text-gray-600 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                className: \"hover:text-primary\",\n                                children: \"الرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/products\",\n                                className: \"hover:text-primary\",\n                                children: \"المنتجات\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-900\",\n                                children: product.nameAr\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-square overflow-hidden rounded-lg border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: product.images[selectedImage],\n                                            alt: product.nameAr,\n                                            width: 600,\n                                            height: 600,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-2\",\n                                        children: product.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedImage(index),\n                                                className: \"aspect-square overflow-hidden rounded-lg border-2 \".concat(selectedImage === index ? \"border-primary\" : \"border-gray-200\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    src: image,\n                                                    alt: \"\".concat(product.nameAr, \" \").concat(index + 1),\n                                                    width: 150,\n                                                    height: 150,\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2\",\n                                                children: product.brand\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold mb-2\",\n                                                children: product.nameAr\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg text-gray-600\",\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 \".concat(i < Math.floor(product.rating) ? \"fill-yellow-400 text-yellow-400\" : \"text-gray-300\")\n                                                    }, i, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    product.rating,\n                                                    \" (\",\n                                                    product.reviewCount,\n                                                    \" تقييم)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-3xl font-bold text-primary\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatPrice)(product.price)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this),\n                                            product.comparePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl text-gray-500 line-through\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatPrice)(product.comparePrice)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.comparePrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-red-100 text-red-800 text-sm px-2 py-1 rounded\",\n                                                children: [\n                                                    \"وفر \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.formatPrice)(product.comparePrice - product.price)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"الكمية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                                                                disabled: quantity <= 1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-4 py-2 min-w-[60px] text-center\",\n                                                                children: quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>setQuantity(quantity + 1),\n                                                                disabled: quantity >= product.stockCount,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"متوفر \",\n                                                            product.stockCount,\n                                                            \" قطعة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"flex-1\",\n                                                        onClick: handleAddToCart,\n                                                        disabled: !product.inStock,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"أضف للسلة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: handleWishlistToggle,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(isWishlisted ? \"fill-red-500 text-red-500\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"شحن مجاني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"ضمان الجودة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"إرجاع مجاني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"description\",\n                                            label: \"الوصف\"\n                                        },\n                                        {\n                                            id: \"specifications\",\n                                            label: \"المواصفات\"\n                                        },\n                                        {\n                                            id: \"reviews\",\n                                            label: \"التقييمات\"\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-primary text-primary\" : \"border-transparent text-gray-500 hover:text-gray-700\"),\n                                            children: tab.label\n                                        }, tab.id, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: [\n                                    activeTab === \"description\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-3\",\n                                                        children: \"المميزات:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: product.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"specifications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: Object.entries(product.specifications).map((param)=>{\n                                            let [key, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between py-2 border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            key,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600\",\n                                                        children: value\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"reviews\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: [\n                                                            \"التقييمات (\",\n                                                            reviews.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        children: \"اكتب تقييم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: review.user\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: review.date\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 300,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex mb-2\",\n                                                                    children: [\n                                                                        ...Array(5)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_Heart_Minus_Plus_RotateCcw_Share2_Shield_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 \".concat(i < review.rating ? \"fill-yellow-400 text-yellow-400\" : \"text-gray-300\")\n                                                                        }, i, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: review.comment\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, review.id, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-6\",\n                                children: \"منتجات مشابهة\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                children: relatedProducts.map((product1)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_product_card__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        product: product1\n                                    }, product1.id, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\products\\\\[slug]\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailPage, \"F4aWEDo/DA7ySJGIUxp9SAp64wE=\");\n_c = ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/[slug]/page.tsx\n"));

/***/ })

});