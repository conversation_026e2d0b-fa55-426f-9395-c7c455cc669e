// إضافة منتجات تجريبية للاختبار
import { ProductsStore } from './products-store';

export const initSampleProducts = () => {
  // التحقق من وجود منتجات تجريبية محددة
  const existingProducts = ProductsStore.getAll();
  const hasSampleProducts = existingProducts.some(p =>
    p.sku === "DCL-001" || p.sku === "CMG-001" || p.sku === "MCL-001"
  );

  if (hasSampleProducts) {
    return; // لا نضيف منتجات إذا كانت المنتجات التجريبية موجودة بالفعل
  }

  // إضافة منتجات تجريبية
  const sampleProducts = [
    {
      name: "عدسات لاصقة يومية",
      nameEn: "Daily Contact Lenses",
      sku: "DCL-001",
      category: "العدسات اليومية",
      brand: "Johnson & Johnson",
      price: 25000,
      priceCurrency: "IQD",
      stock: 50,
      status: "active" as const,
      image: "https://picsum.photos/400/400?random=1",
      images: ["https://picsum.photos/400/400?random=1"],
      description: "عدسات لاصقة يومية عالية الجودة توفر راحة طوال اليوم",
      shortDescription: "عدسات يومية مريحة",
      tags: ["عدسات", "يومية", "راحة"],
      specifications: [
        { key: "النوع", value: "يومية" },
        { key: "المادة", value: "هيدروجيل" },
        { key: "محتوى الماء", value: "58%" }
      ],
      slug: "daily-contact-lenses-dcl-001",
      featured: true,
      allowBackorder: false,
      trackQuantity: true,
      location: "المخزن الرئيسي"
    },
    {
      name: "نظارات طبية كلاسيكية",
      nameEn: "Classic Medical Glasses",
      sku: "CMG-001",
      category: "النظارات الطبية",
      brand: "Ray-Ban",
      price: 150000,
      priceCurrency: "IQD",
      stock: 25,
      status: "active" as const,
      image: "https://picsum.photos/400/400?random=2",
      images: ["https://picsum.photos/400/400?random=2"],
      description: "نظارات طبية أنيقة بتصميم كلاسيكي مناسب لجميع الأعمار",
      shortDescription: "نظارات طبية كلاسيكية",
      tags: ["نظارات", "طبية", "كلاسيكية"],
      specifications: [
        { key: "المادة", value: "معدن" },
        { key: "اللون", value: "أسود" },
        { key: "الحجم", value: "متوسط" }
      ],
      slug: "classic-medical-glasses-cmg-001",
      featured: false,
      allowBackorder: true,
      trackQuantity: true,
      location: "المخزن الرئيسي"
    },
    {
      name: "عدسات ملونة شهرية",
      nameEn: "Monthly Colored Lenses",
      sku: "MCL-001",
      category: "العدسات الملونة",
      brand: "Alcon",
      price: 45000,
      priceCurrency: "IQD",
      stock: 30,
      status: "active" as const,
      image: "https://picsum.photos/400/400?random=3",
      images: ["https://picsum.photos/400/400?random=3"],
      description: "عدسات ملونة شهرية تمنحك إطلالة جذابة ومميزة",
      shortDescription: "عدسات ملونة شهرية",
      tags: ["عدسات", "ملونة", "شهرية"],
      specifications: [
        { key: "النوع", value: "شهرية" },
        { key: "اللون", value: "أزرق" },
        { key: "القطر", value: "14.2 مم" }
      ],
      slug: "monthly-colored-lenses-mcl-001",
      featured: true,
      allowBackorder: false,
      trackQuantity: true,
      location: "المخزن الرئيسي"
    }
  ];

  // إضافة المنتجات
  sampleProducts.forEach(product => {
    ProductsStore.add(product);
  });

  console.log("تم إضافة المنتجات التجريبية بنجاح");
};

// دالة لمسح المنتجات المكررة
export const cleanDuplicateProducts = () => {
  const allProducts = ProductsStore.getAll();
  const uniqueProducts = [];
  const seenIds = new Set();

  for (const product of allProducts) {
    if (!seenIds.has(product.id)) {
      seenIds.add(product.id);
      uniqueProducts.push(product);
    }
  }

  if (uniqueProducts.length !== allProducts.length) {
    ProductsStore.clear();
    ProductsStore.import(uniqueProducts);
    console.log(`تم مسح ${allProducts.length - uniqueProducts.length} منتج مكرر`);
  }
};
