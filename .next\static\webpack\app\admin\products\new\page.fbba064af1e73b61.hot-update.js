"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/new/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/new/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/admin/products/new/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,DollarSign,Eye,FileText,Package,Plus,Save,Tag,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction NewProductPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        // معلومات أساسية\n        name: \"\",\n        nameEn: \"\",\n        description: \"\",\n        shortDescription: \"\",\n        sku: \"\",\n        barcode: \"\",\n        // التصنيف\n        category: \"\",\n        brand: \"\",\n        tags: [],\n        // الأسعار\n        price: \"\",\n        comparePrice: \"\",\n        cost: \"\",\n        // المخزون\n        trackQuantity: true,\n        quantity: \"\",\n        minQuantity: \"\",\n        maxQuantity: \"\",\n        location: \"\",\n        // الشحن\n        weight: \"\",\n        dimensions: {\n            length: \"\",\n            width: \"\",\n            height: \"\"\n        },\n        // SEO\n        metaTitle: \"\",\n        metaDescription: \"\",\n        slug: \"\",\n        // الحالة\n        status: \"draft\",\n        featured: false,\n        allowBackorder: false,\n        // المواصفات\n        specifications: [],\n        // الصور\n        images: []\n    });\n    const [currentTag, setCurrentTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewImages, setPreviewImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const categories = [\n        \"العدسات اليومية\",\n        \"العدسات الشهرية\",\n        \"العدسات الملونة\",\n        \"العدسات الأسبوعية\",\n        \"النظارات الطبية\",\n        \"النظارات الشمسية\",\n        \"الإكسسوارات\"\n    ];\n    const brands = [\n        \"Johnson & Johnson\",\n        \"Alcon\",\n        \"CooperVision\",\n        \"Bausch & Lomb\",\n        \"Ray-Ban\",\n        \"Oakley\"\n    ];\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // إزالة الخطأ عند التعديل\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n        // توليد slug تلقائياً من الاسم\n        if (field === \"name\" && value) {\n            const slug = value.toLowerCase().replace(/[^a-z0-9\\u0600-\\u06FF\\s-]/g, \"\").replace(/\\s+/g, \"-\").trim();\n            setFormData((prev)=>({\n                    ...prev,\n                    slug\n                }));\n        }\n    };\n    const handleDimensionChange = (dimension, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                dimensions: {\n                    ...prev.dimensions,\n                    [dimension]: value\n                }\n            }));\n    };\n    const addTag = ()=>{\n        if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        currentTag.trim()\n                    ]\n                }));\n            setCurrentTag(\"\");\n        }\n    };\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const addSpecification = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: [\n                    ...prev.specifications,\n                    {\n                        key: \"\",\n                        value: \"\"\n                    }\n                ]\n            }));\n    };\n    const updateSpecification = (index, field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.map((spec, i)=>i === index ? {\n                        ...spec,\n                        [field]: value\n                    } : spec)\n            }));\n    };\n    const removeSpecification = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                specifications: prev.specifications.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        const files = event.target.files;\n        if (!files) return;\n        setUploadingImages(true);\n        const newImages = [];\n        const newPreviews = [];\n        try {\n            for(let i = 0; i < files.length; i++){\n                const file = files[i];\n                // إنشاء معاينة محلية\n                const preview = URL.createObjectURL(file);\n                newPreviews.push(preview);\n                // محاكاة رفع الصورة\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // في التطبيق الحقيقي، سيتم رفع الصورة إلى الخادم\n                const imageUrl = \"https://picsum.photos/400/400?random=\".concat(Date.now(), \"-\").concat(i);\n                newImages.push(imageUrl);\n            }\n            setFormData((prev)=>({\n                    ...prev,\n                    images: [\n                        ...prev.images,\n                        ...newImages\n                    ]\n                }));\n            setPreviewImages((prev)=>[\n                    ...prev,\n                    ...newPreviews\n                ]);\n        } catch (error) {\n            console.error(\"Error uploading images:\", error);\n            alert(\"حدث خطأ أثناء رفع الصور\");\n        } finally{\n            setUploadingImages(false);\n        }\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n        setPreviewImages((prev)=>{\n            const newPreviews = prev.filter((_, i)=>i !== index);\n            // تنظيف URL المؤقت\n            if (prev[index]) {\n                URL.revokeObjectURL(prev[index]);\n            }\n            return newPreviews;\n        });\n    };\n    const duplicateProduct = ()=>{\n        const duplicatedData = {\n            ...formData,\n            name: \"\".concat(formData.name, \" - نسخة\"),\n            nameEn: \"\".concat(formData.nameEn, \" - Copy\"),\n            sku: \"\".concat(formData.sku, \"-COPY\"),\n            slug: \"\".concat(formData.slug, \"-copy\")\n        };\n        setFormData(duplicatedData);\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) newErrors.name = \"اسم المنتج مطلوب\";\n        if (!formData.nameEn.trim()) newErrors.nameEn = \"الاسم الإنجليزي مطلوب\";\n        if (!formData.description.trim()) newErrors.description = \"الوصف مطلوب\";\n        if (!formData.sku.trim()) newErrors.sku = \"رمز المنتج مطلوب\";\n        if (!formData.category) newErrors.category = \"الفئة مطلوبة\";\n        if (!formData.brand) newErrors.brand = \"العلامة التجارية مطلوبة\";\n        if (!formData.price || parseFloat(formData.price) <= 0) {\n            newErrors.price = \"السعر مطلوب ويجب أن يكون أكبر من صفر\";\n        }\n        if (formData.trackQuantity && (!formData.quantity || parseInt(formData.quantity) < 0)) {\n            newErrors.quantity = \"الكمية مطلوبة\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // هنا سيتم إرسال البيانات إلى API\n            console.log(\"Product data:\", formData);\n            // محاكاة API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            alert(\"تم إضافة المنتج بنجاح!\");\n        // إعادة توجيه إلى صفحة المنتجات\n        // router.push(\"/admin/products\");\n        } catch (error) {\n            console.error(\"Error creating product:\", error);\n            alert(\"حدث خطأ أثناء إضافة المنتج\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleSaveAsDraft = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: \"draft\"\n            }));\n        handleSubmit(new Event(\"submit\"));\n    };\n    const handlePublish = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                status: \"active\"\n            }));\n        handleSubmit(new Event(\"submit\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin/products\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"إضافة منتج جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"إنشاء منتج جديد في المتجر\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: duplicateProduct,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نسخ المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleSaveAsDraft,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حفظ كمسودة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handlePublish,\n                                disabled: isSubmitting,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"نشر المنتج\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (عربي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: formData.name,\n                                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                                placeholder: \"أدخل اسم المنتج\",\n                                                                className: errors.name ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"اسم المنتج (إنجليزي) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: formData.nameEn,\n                                                                onChange: (e)=>handleInputChange(\"nameEn\", e.target.value),\n                                                                placeholder: \"Product Name in English\",\n                                                                className: errors.nameEn ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.nameEn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.nameEn\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف المختصر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        value: formData.shortDescription,\n                                                        onChange: (e)=>handleInputChange(\"shortDescription\", e.target.value),\n                                                        placeholder: \"وصف مختصر للمنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوصف التفصيلي *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.description,\n                                                        onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                        placeholder: \"وصف تفصيلي للمنتج...\",\n                                                        rows: 4,\n                                                        className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.description ? \"border-red-500\" : \"border-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-xs mt-1\",\n                                                        children: errors.description\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"رمز المنتج (SKU) *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: formData.sku,\n                                                                onChange: (e)=>handleInputChange(\"sku\", e.target.value),\n                                                                placeholder: \"PRD-001\",\n                                                                className: errors.sku ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.sku && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.sku\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الباركود\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: formData.barcode,\n                                                                onChange: (e)=>handleInputChange(\"barcode\", e.target.value),\n                                                                placeholder: \"1234567890123\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"التصنيف\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الفئة *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.category,\n                                                                onChange: (e)=>handleInputChange(\"category\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.category ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر الفئة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category,\n                                                                            children: category\n                                                                        }, category, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.category\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"العلامة التجارية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.brand,\n                                                                onChange: (e)=>handleInputChange(\"brand\", e.target.value),\n                                                                className: \"w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary \".concat(errors.brand ? \"border-red-500\" : \"border-gray-300\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر العلامة التجارية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    brands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: brand,\n                                                                            children: brand\n                                                                        }, brand, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 23\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            errors.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.brand\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"العلامات (Tags)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: currentTag,\n                                                                onChange: (e)=>setCurrentTag(e.target.value),\n                                                                placeholder: \"أضف علامة\",\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && (e.preventDefault(), addTag())\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addTag,\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-sm\",\n                                                                children: [\n                                                                    tag,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>removeTag(tag),\n                                                                        className: \"hover:text-red-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الأسعار\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"سعر البيع *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.price,\n                                                            onChange: (e)=>handleInputChange(\"price\", e.target.value),\n                                                            placeholder: \"0.00\",\n                                                            className: errors.price ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-500 text-xs mt-1\",\n                                                            children: errors.price\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر المقارن\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.comparePrice,\n                                                            onChange: (e)=>handleInputChange(\"comparePrice\", e.target.value),\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"سعر التكلفة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            value: formData.cost,\n                                                            onChange: (e)=>handleInputChange(\"cost\", e.target.value),\n                                                            placeholder: \"0.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"إدارة المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"trackQuantity\",\n                                                        checked: formData.trackQuantity,\n                                                        onChange: (e)=>handleInputChange(\"trackQuantity\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"trackQuantity\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"تتبع الكمية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 15\n                                            }, this),\n                                            formData.trackQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الكمية الحالية *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.quantity,\n                                                                onChange: (e)=>handleInputChange(\"quantity\", e.target.value),\n                                                                placeholder: \"0\",\n                                                                className: errors.quantity ? \"border-red-500\" : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            errors.quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-500 text-xs mt-1\",\n                                                                children: errors.quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأدنى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.minQuantity,\n                                                                onChange: (e)=>handleInputChange(\"minQuantity\", e.target.value),\n                                                                placeholder: \"0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الحد الأقصى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.maxQuantity,\n                                                                onChange: (e)=>handleInputChange(\"maxQuantity\", e.target.value),\n                                                                placeholder: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 628,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-2\",\n                                                                children: \"الموقع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: formData.location,\n                                                                onChange: (e)=>handleInputChange(\"location\", e.target.value),\n                                                                placeholder: \"A1-B2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"allowBackorder\",\n                                                        checked: formData.allowBackorder,\n                                                        onChange: (e)=>handleInputChange(\"allowBackorder\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"allowBackorder\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"السماح بالطلب المسبق عند نفاد المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"المواصفات التقنية\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: formData.specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                placeholder: \"المواصفة\",\n                                                                value: spec.key,\n                                                                onChange: (e)=>updateSpecification(index, \"key\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                placeholder: \"القيمة\",\n                                                                value: spec.value,\n                                                                onChange: (e)=>updateSpecification(index, \"value\", e.target.value),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"icon\",\n                                                                onClick: ()=>removeSpecification(index),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: addSpecification,\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"إضافة مواصفة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"حالة المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange(\"status\", e.target.value),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"draft\",\n                                                                children: \"مسودة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"active\",\n                                                                children: \"نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"inactive\",\n                                                                children: \"غير نشط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"featured\",\n                                                        checked: formData.featured,\n                                                        onChange: (e)=>handleInputChange(\"featured\", e.target.checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"featured\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"صور المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-2\",\n                                                    children: \"اسحب الصور هنا أو انقر للتحديد\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"اختيار الصور\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 745,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"معلومات الشحن\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الوزن (جرام)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        type: \"number\",\n                                                        value: formData.weight,\n                                                        onChange: (e)=>handleInputChange(\"weight\", e.target.value),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الأبعاد (سم)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.length,\n                                                                onChange: (e)=>handleDimensionChange(\"length\", e.target.value),\n                                                                placeholder: \"طول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.width,\n                                                                onChange: (e)=>handleDimensionChange(\"width\", e.target.value),\n                                                                placeholder: \"عرض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                type: \"number\",\n                                                                value: formData.dimensions.height,\n                                                                onChange: (e)=>handleDimensionChange(\"height\", e.target.value),\n                                                                placeholder: \"ارتفاع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"تحسين محركات البحث\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"الرابط (Slug)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        value: formData.slug,\n                                                        onChange: (e)=>handleInputChange(\"slug\", e.target.value),\n                                                        placeholder: \"product-slug\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        value: formData.metaTitle,\n                                                        onChange: (e)=>handleInputChange(\"metaTitle\", e.target.value),\n                                                        placeholder: \"عنوان الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-2\",\n                                                        children: \"وصف الصفحة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>handleInputChange(\"metaDescription\", e.target.value),\n                                                        placeholder: \"وصف الصفحة لمحركات البحث\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            Object.keys(errors).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_DollarSign_Eye_FileText_Package_Plus_Save_Tag_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"يرجى تصحيح الأخطاء التالية:\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 862,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mt-2 text-sm text-red-700 list-disc list-inside\",\n                            children: Object.values(errors).map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: error\n                                }, index, false, {\n                                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                                    lineNumber: 866,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                    lineNumber: 859,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n                lineNumber: 858,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\visionlens\\\\src\\\\app\\\\admin\\\\products\\\\new\\\\page.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n_s(NewProductPage, \"LSQc2JuvgyLm0Nq6yv0BvcZYq8w=\");\n_c = NewProductPage;\nvar _c;\n$RefreshReg$(_c, \"NewProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/new/page.tsx\n"));

/***/ })

});