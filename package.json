{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "متجر إلكتروني احترافي لبيع العدسات اللاصقة والنظارات الطبية - VisionLens E-commerce Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.6.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@stripe/stripe-js": "^2.1.11", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "next": "15.3.3", "next-auth": "^4.24.5", "next-themes": "^0.4.6", "postcss": "^8.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "recharts": "^2.8.0", "stripe": "^14.7.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^5.6.0", "tailwindcss": "^3.4.17", "tsx": "^4.1.0", "typescript": "^5"}, "engines": {"node": ">=18.0.0"}}